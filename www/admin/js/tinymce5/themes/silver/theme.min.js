/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(h){"use strict";var Q=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n]},p=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}},Z=function(t){return function(){return t}},d=function(t){return t};function v(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}var t,n,e,o,r,s,i,m=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return!e.apply(null,t)}},u=function(t){return function(){throw new Error(t)}},a=Z(!1),c=Z(!0),f=a,l=c,g=function(){return b},b=(o={fold:function(t,n){return t()},is:f,isSome:f,isNone:l,getOr:e=function(t){return t},getOrThunk:n=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:e,orThunk:n,map:g,ap:g,each:function(){},bind:g,flatten:g,exists:f,forall:l,filter:g,equals:t=function(t){return t.isNone()},equals_:t,toArray:function(){return[]},toString:Z("none()")},Object.freeze&&Object.freeze(o),o),y=function(e){var t=function(){return e},n=function(){return r},o=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:l,isNone:f,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return y(t(e))},ap:function(t){return t.fold(g,function(t){return y(t(e))})},each:function(t){t(e)},bind:o,flatten:t,exists:o,forall:o,filter:function(t){return t(e)?r:b},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(f,function(t){return n(e,t)})},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return r},tt={some:y,none:g,from:function(t){return null===t||t===undefined?b:y(t)}},x=function(n){return function(t){return function(t){if(null===t)return"null";var n=typeof t;return"object"===n&&Array.prototype.isPrototypeOf(t)?"array":"object"===n&&String.prototype.isPrototypeOf(t)?"string":n}(t)===n}},w=x("string"),S=x("object"),E=x("array"),C=x("boolean"),k=x("function"),O=x("number"),T=(r=Array.prototype.indexOf)===undefined?function(t,n){return N(t,n)}:function(t,n){return r.call(t,n)},B=function(t,n){return-1<T(t,n)},A=function(t,n){return H(t,n).isSome()},D=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=t.slice(o,o+n);e.push(r)}return e},_=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r,t)}return o},M=function(t,n){for(var e=0,o=t.length;e<o;e++)n(t[e],e,t)},F=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o,t)&&e.push(i)}return e},I=function(t,n,e){return function(t,n){for(var e=t.length-1;0<=e;e--)n(t[e],e,t)}(t,function(t){e=n(e,t)}),e},V=function(t,n,e){return M(t,function(t){e=n(e,t)}),e},R=function(t,n){for(var e=0,o=t.length;e<o;e++){var r=t[e];if(n(r,e,t))return tt.some(r)}return tt.none()},H=function(t,n){for(var e=0,o=t.length;e<o;e++)if(n(t[e],e,t))return tt.some(e);return tt.none()},N=function(t,n){for(var e=0,o=t.length;e<o;++e)if(t[e]===n)return e;return-1},P=Array.prototype.push,z=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!Array.prototype.isPrototypeOf(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);P.apply(n,t[e])}return n},L=function(t,n){var e=_(t,n);return z(e)},j=function(t,n){for(var e=0,o=t.length;e<o;++e)if(!0!==n(t[e],e,t))return!1;return!0},U=Array.prototype.slice,W=function(t){var n=U.call(t,0);return n.reverse(),n},G=function(t,n){return F(t,function(t){return!B(n,t)})},X=function(t){return[t]},Y=function(t){return 0===t.length?tt.none():tt.some(t[0])},q=function(t){return 0===t.length?tt.none():tt.some(t[t.length-1])},K=k(Array.from)?Array.from:function(t){return U.call(t)},J=Object.keys,$=Object.hasOwnProperty,nt=function(t,n){for(var e=J(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i,t)}},et=function(t,o){return ot(t,function(t,n,e){return{k:n,v:o(t,n,e)}})},ot=function(o,r){var i={};return nt(o,function(t,n){var e=r(t,n,o);i[e.k]=e.v}),i},rt=function(t,e){var o=[];return nt(t,function(t,n){o.push(e(t,n))}),o},it=function(t){return rt(t,function(t){return t})},ut=function(t,n){return at(t,n)?tt.some(t[n]):tt.none()},at=function(t,n){return $.call(t,n)},ct=function(n){return function(t){return at(t,n)?tt.from(t[n]):tt.none()}},st=function(t,n){return ct(n)(t)},ft=function(t,n){var e={};return e[t]=n,e},lt=function(e){return{is:function(t){return e===t},isValue:c,isError:a,getOr:Z(e),getOrThunk:Z(e),getOrDie:Z(e),or:function(t){return lt(e)},orThunk:function(t){return lt(e)},fold:function(t,n){return n(e)},map:function(t){return lt(t(e))},mapError:function(t){return lt(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOption:function(){return tt.some(e)}}},dt=function(e){return{is:a,isValue:a,isError:c,getOr:d,getOrThunk:function(t){return t()},getOrDie:function(){return u(String(e))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return dt(e)},mapError:function(t){return dt(t(e))},each:Q,bind:function(t){return dt(e)},exists:a,forall:c,toOption:tt.none}},mt={value:lt,error:dt},gt=function(u){if(!E(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return M(u,function(t,o){var n=J(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!E(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var t=arguments.length;if(t!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+t);for(var e=new Array(t),n=0;n<e.length;n++)e[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(t){var n=J(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!j(a,function(t){return B(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){console.log(t,{constructors:a,constructor:r,params:e})}}}}),e},pt=(gt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),Object.prototype.hasOwnProperty),ht=function(u){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r=t[o];for(var i in r)pt.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}},vt=ht(function(t,n){return S(t)&&S(n)?vt(t,n):n}),bt=ht(function(t,n){return n}),yt=function(t,n){return e=n,o={},nt(t,function(t,n){B(e,n)||(o[n]=t)}),o;var e,o},xt=function(t){return ct(t)},wt=function(t,n){return e=t,o=n,function(t){return at(t,e)?t[e]:o};var e,o},St=function(t,n){return st(t,n)},Ct=function(t,n){return ft(t,n)},kt=function(t){return n={},M(t,function(t){n[t.key]=t.value}),n;var n},Ot=function(t,n){var e,o,r,i,u,a=(e=[],o=[],M(t,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),{errors:e,values:o});return 0<a.errors.length?(u=a.errors,p(mt.error,z)(u)):(i=n,0===(r=a.values).length?mt.value(i):mt.value(vt(i,bt.apply(undefined,r))))},Et=function(t,n){return at(e=t,o=n)&&e[o]!==undefined&&null!==e[o];var e,o},Tt=function(t){var n=t,e=function(){return n};return{get:e,set:function(t){n=t},clone:function(){return Tt(e())}}},Bt=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},At=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return tt.none()},Dt=Z("touchstart"),_t=Z("touchmove"),Mt=Z("touchend"),Ft=Z("mousedown"),It=Z("mousemove"),Vt=Z("mouseout"),Rt=Z("mouseup"),Ht=Z("mouseover"),Nt=Z("focusin"),Pt=Z("focusout"),zt=Z("keydown"),Lt=Z("keyup"),jt=Z("input"),Ut=Z("change"),Wt=Z("click"),Gt=Z("transitionend"),Xt=Z("selectstart"),Yt=function(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}},qt=function(t,n){var e=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}return undefined}(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return Jt(o(1),o(2))},Kt=function(){return Jt(0,0)},Jt=function(t,n){return{major:t,minor:n}},$t={nu:Jt,detect:function(t,n){var e=String(n).toLowerCase();return 0===t.length?Kt():qt(t,e)},unknown:Kt},Qt="Firefox",Zt=function(t,n){return function(){return n===t}},tn=function(t){var n=t.current;return{current:n,version:t.version,isEdge:Zt("Edge",n),isChrome:Zt("Chrome",n),isIE:Zt("IE",n),isOpera:Zt("Opera",n),isFirefox:Zt(Qt,n),isSafari:Zt("Safari",n)}},nn={unknown:function(){return tn({current:undefined,version:$t.unknown()})},nu:tn,edge:Z("Edge"),chrome:Z("Chrome"),ie:Z("IE"),opera:Z("Opera"),firefox:Z(Qt),safari:Z("Safari")},en="Windows",on="Android",rn="Solaris",un="FreeBSD",an=function(t,n){return function(){return n===t}},cn=function(t){var n=t.current;return{current:n,version:t.version,isWindows:an(en,n),isiOS:an("iOS",n),isAndroid:an(on,n),isOSX:an("OSX",n),isLinux:an("Linux",n),isSolaris:an(rn,n),isFreeBSD:an(un,n)}},sn={unknown:function(){return cn({current:undefined,version:$t.unknown()})},nu:cn,windows:Z(en),ios:Z("iOS"),android:Z(on),linux:Z("Linux"),osx:Z("OSX"),solaris:Z(rn),freebsd:Z(un)},fn=function(t,n){var e=String(n).toLowerCase();return R(t,function(t){return t.search(e)})},ln=function(t,e){return fn(t,e).map(function(t){var n=$t.detect(t.versionRegexes,e);return{current:t.name,version:n}})},dn=function(t,e){return fn(t,e).map(function(t){var n=$t.detect(t.versionRegexes,e);return{current:t.name,version:n}})},mn=function(t,n){return-1!==t.indexOf(n)},gn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,pn=function(n){return function(t){return mn(t,n)}},hn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return mn(t,"edge/")&&mn(t,"chrome")&&mn(t,"safari")&&mn(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,gn],search:function(t){return mn(t,"chrome")&&!mn(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return mn(t,"msie")||mn(t,"trident")}},{name:"Opera",versionRegexes:[gn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:pn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:pn("firefox")},{name:"Safari",versionRegexes:[gn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(mn(t,"safari")||mn(t,"mobile/"))&&mn(t,"applewebkit")}}],vn=[{name:"Windows",search:pn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return mn(t,"iphone")||mn(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:pn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:pn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:pn("linux"),versionRegexes:[]},{name:"Solaris",search:pn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:pn("freebsd"),versionRegexes:[]}],bn={browsers:Z(hn),oses:Z(vn)},yn=function(t){var n,e,o,r,i,u,a,c,s,f,l,d=bn.browsers(),m=bn.oses(),g=ln(d,t).fold(nn.unknown,nn.nu),p=dn(m,t).fold(sn.unknown,sn.nu);return{browser:g,os:p,deviceType:(e=g,o=t,r=(n=p).isiOS()&&!0===/ipad/i.test(o),i=n.isiOS()&&!r,u=n.isAndroid()&&3===n.version.major,a=n.isAndroid()&&4===n.version.major,c=r||u||a&&!0===/mobile/i.test(o),s=n.isiOS()||n.isAndroid(),f=s&&!c,l=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(o),{isiPad:Z(r),isiPhone:Z(i),isTablet:Z(c),isPhone:Z(f),isTouch:Z(s),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:Z(l)})}},xn={detect:Yt(function(){var t=h.navigator.userAgent;return yn(t)})},wn={tap:Z("alloy.tap")},Sn=Z("alloy.focus"),Cn=Z("alloy.blur.post"),kn=Z("alloy.paste.post"),On=Z("alloy.receive"),En=Z("alloy.execute"),Tn=Z("alloy.focus.item"),Bn=wn.tap,An=xn.detect().deviceType.isTouch()?wn.tap:Wt,Dn=Z("alloy.longpress"),_n=Z("alloy.sandbox.close"),Mn=Z("alloy.typeahead.cancel"),Fn=Z("alloy.system.init"),In=Z("alloy.system.scroll"),Vn=Z("alloy.system.resize"),Rn=Z("alloy.system.attached"),Hn=Z("alloy.system.detached"),Nn=Z("alloy.system.dismissRequested"),Pn=Z("alloy.focusmanager.shifted"),zn=Z("alloy.slotcontainer.visibility"),Ln=Z("alloy.change.tab"),jn=Z("alloy.dismiss.tab"),Un=Z("alloy.highlight"),Wn=Z("alloy.dehighlight"),Gn=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:Z(t)}},Xn={fromHtml:function(t,n){var e=(n||h.document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw h.console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return Gn(e.childNodes[0])},fromTag:function(t,n){var e=(n||h.document).createElement(t);return Gn(e)},fromText:function(t,n){var e=(n||h.document).createTextNode(t);return Gn(e)},fromDom:Gn,fromPoint:function(t,n,e){var o=t.dom();return tt.from(o.elementFromPoint(n,e)).map(Gn)}},Yn=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(n.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+e.length+" arguments");var o={};return M(n,function(t,n){o[t]=Z(e[n])}),o}},qn=function(t){return t.slice(0).sort()},Kn=function(n,t){if(!E(t))throw new Error("The "+n+" fields must be an array. Was: "+t+".");M(t,function(t){if(!w(t))throw new Error("The value "+t+" in the "+n+" fields was not a string.")})},Jn=function(r,i){var e,u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Kn("required",r),Kn("optional",i),e=qn(u),R(e,function(t,n){return n<e.length-1&&t===e[n+1]}).each(function(t){throw new Error("The field: "+t+" occurs more than once in the combined fields: ["+e.join(", ")+"].")}),function(n){var e=J(n);j(r,function(t){return B(e,t)})||function(t,n){throw new Error("All required keys ("+qn(t).join(", ")+") were not specified. Specified keys were: "+qn(n).join(", ")+".")}(r,e);var t=F(e,function(t){return!B(u,t)});0<t.length&&function(t){throw new Error("Unsupported keys for object: "+qn(t).join(", "))}(t);var o={};return M(r,function(t){o[t]=Z(n[t])}),M(i,function(t){o[t]=Z(Object.prototype.hasOwnProperty.call(n,t)?tt.some(n[t]):tt.none())}),o}},$n="undefined"!=typeof window?window:Function("return this;")(),Qn=function(t,n){return function(t,n){for(var e=n!==undefined&&null!==n?n:$n,o=0;o<t.length&&e!==undefined&&null!==e;++o)e=e[t[o]];return e}(t.split("."),n)},Zn={getOrDie:function(t,n){var e=Qn(t,n);if(e===undefined||null===e)throw t+" not available on this browser";return e}},te=(h.Node.ATTRIBUTE_NODE,h.Node.CDATA_SECTION_NODE,h.Node.COMMENT_NODE,h.Node.DOCUMENT_NODE),ne=(h.Node.DOCUMENT_TYPE_NODE,h.Node.DOCUMENT_FRAGMENT_NODE,h.Node.ELEMENT_NODE),ee=h.Node.TEXT_NODE,oe=(h.Node.PROCESSING_INSTRUCTION_NODE,h.Node.ENTITY_REFERENCE_NODE,h.Node.ENTITY_NODE,h.Node.NOTATION_NODE,ne),re=te,ie=function(t,n){var e=t.dom();if(e.nodeType!==oe)return!1;if(e.matches!==undefined)return e.matches(n);if(e.msMatchesSelector!==undefined)return e.msMatchesSelector(n);if(e.webkitMatchesSelector!==undefined)return e.webkitMatchesSelector(n);if(e.mozMatchesSelector!==undefined)return e.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},ue=function(t){return t.nodeType!==oe&&t.nodeType!==re||0===t.childElementCount},ae=function(t,n){var e=n===undefined?h.document:n.dom();return ue(e)?[]:_(e.querySelectorAll(t),Xn.fromDom)},ce=function(t,n){return t.dom()===n.dom()},se=(xn.detect().browser.isIE(),function(t){return Xn.fromDom(t.dom().ownerDocument)}),fe=function(t){var n=t.dom().ownerDocument.defaultView;return Xn.fromDom(n)},le=function(t){var n=t.dom();return tt.from(n.parentNode).map(Xn.fromDom)},de=function(t){var n=t.dom();return tt.from(n.offsetParent).map(Xn.fromDom)},me=function(t){var n=t.dom();return _(n.childNodes,Xn.fromDom)},ge=function(t,n){var e=t.dom().childNodes;return tt.from(e[n]).map(Xn.fromDom)},pe=(Yn("element","offset"),function(n,e){le(n).each(function(t){t.dom().insertBefore(e.dom(),n.dom())})}),he=function(t,n){var e;(e=t.dom(),tt.from(e.nextSibling).map(Xn.fromDom)).fold(function(){le(t).each(function(t){be(t,n)})},function(t){pe(t,n)})},ve=function(n,e){ge(n,0).fold(function(){be(n,e)},function(t){n.dom().insertBefore(e.dom(),t.dom())})},be=function(t,n){t.dom().appendChild(n.dom())},ye=function(n,t){M(t,function(t){be(n,t)})},xe=function(t){t.dom().textContent="",M(me(t),function(t){we(t)})},we=function(t){var n=t.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},Se=function(t){return t.dom().innerHTML},Ce=function(t,n){var e,o,r=se(t).dom(),i=Xn.fromDom(r.createDocumentFragment()),u=(e=n,(o=(r||h.document).createElement("div")).innerHTML=e,me(Xn.fromDom(o)));ye(i,u),xe(t),be(t,i)},ke=function(t){return t.dom().nodeName.toLowerCase()},Oe=function(n){return function(t){return t.dom().nodeType===n}},Ee=Oe(ne),Te=Oe(ee),Be=Oe(te),Ae=function(t,n,e){if(!(w(e)||C(e)||O(e)))throw h.console.error("Invalid call to Attr.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")},De=function(t,n,e){Ae(t.dom(),n,e)},_e=function(t,n){var e=t.dom();nt(n,function(t,n){Ae(e,n,t)})},Me=function(t,n){var e=t.dom().getAttribute(n);return null===e?undefined:e},Fe=function(t,n){var e=t.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},Ie=function(t,n){t.dom().removeAttribute(n)},Ve=function(t){return n=t,e=!1,Xn.fromDom(n.dom().cloneNode(e));var n,e},Re=function(t){var n,e,o,r=Ve(t);return n=r,e=Xn.fromTag("div"),o=Xn.fromDom(n.dom().cloneNode(!0)),be(e,o),Se(e)},He=function(t){return Re(t)},Ne="unknown",Pe="__CHROME_INSPECTOR_CONNECTION_TO_ALLOY__";(i=s||(s={}))[i.STOP=0]="STOP",i[i.NORMAL=1]="NORMAL",i[i.LOGGING=2]="LOGGING";var ze=Tt({}),Le=function(n,t,e){var o,r,i,u;switch(St(ze.get(),n).orThunk(function(){var t=J(ze.get());return At(t,function(t){return-1<n.indexOf(t)?tt.some(ze.get()[t]):tt.none()})}).getOr(s.NORMAL)){case s.NORMAL:return e(Xe());case s.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();B(["mousemove","mouseover","mouseout",Fn()],o)||h.console.log(o,{event:o,time:t-u,target:r.dom(),sequence:_(i,function(t){return B(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+He(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case s.STOP:return!0}},je=["alloy/data/Fields","alloy/debugging/Debugging"],Ue=function(t,n,e){return Le(t,n,e)},We=function(){if(h.window[Pe]!==undefined)return h.window[Pe];var n=function(t,n){var e=ze.get();e[t]=n,ze.set(e)};return h.window[Pe]={systems:{},lookup:function(n){var e=h.window[Pe].systems,t=J(e);return At(t,function(t){return e[t].getByUid(n).toOption().map(function(t){return Ct(He(t.element()),(n=function(e){var t=e.spec();return{"(original.spec)":t,"(dom.ref)":e.element().dom(),"(element)":He(e.element()),"(initComponents)":_(t.components!==undefined?t.components:[],n),"(components)":_(e.components(),n),"(bound.events)":rt(e.events(),function(t,n){return[n]}).join(", "),"(behaviours)":t.behaviours!==undefined?et(t.behaviours,function(t,n){return t===undefined?"--revoked--":{config:t.configAsRaw(),"original-config":t.initialConfig,state:e.readState(n)}}):"none"}})(t));var n})}).orThunk(function(){return tt.some({error:"Systems ("+t.join(", ")+") did not contain uid: "+n})})},events:{setToNormal:function(t){n(t,s.NORMAL)},setToLogging:function(t){n(t,s.LOGGING)},setToStop:function(t){n(t,s.STOP)}}},h.window[Pe]},Ge=function(t,n){We().systems[t]=n},Xe=Z({logEventCut:Q,logEventStopped:Q,logNoParent:Q,logEventNoHandlers:Q,logEventResponse:Q,write:Q}),Ye=0,qe=function(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++Ye+String(n)},Ke=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Je=function(){return(Je=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function $e(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&(e[o[r]]=t[o[r]])}return e}var Qe,Ze,to=gt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),no=function(t){return to.defaultedThunk(Z(t))},eo=to.strict,oo=to.asOption,ro=to.defaultedThunk,io=to.mergeWithThunk;(Ze=Qe||(Qe={}))[Ze.Error=0]="Error",Ze[Ze.Value=1]="Value";var uo=function(t,n,e){return t.stype===Qe.Error?n(t.serror):e(t.svalue)},ao=function(t){return{stype:Qe.Value,svalue:t}},co=function(t){return{stype:Qe.Error,serror:t}},so=function(t){return t.fold(co,ao)},fo=function(t){return uo(t,mt.error,mt.value)},lo=ao,mo=function(t){var n=[],e=[];return M(t,function(t){uo(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e}},go=co,po=function(t,n){return t.stype===Qe.Value?n(t.svalue):t},ho=function(t,n){return t.stype===Qe.Error?n(t.serror):t},vo=function(t,n){return t.stype===Qe.Value?{stype:Qe.Value,svalue:n(t.svalue)}:t},bo=function(t,n){return t.stype===Qe.Error?{stype:Qe.Error,serror:n(t.serror)}:t},yo=function(t){return p(go,z)(t)},xo=function(t,n){var e,o,r=mo(t);return 0<r.errors.length?yo(r.errors):(e=r.values,o=n,0<e.length?lo(vt(o,bt.apply(undefined,e))):lo(o))},wo=function(t){var n=mo(t);return 0<n.errors.length?yo(n.errors):lo(n.values)},So=gt([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),Co=gt([{field:["name","presence","type"]},{state:["name"]}]),ko=function(){return Zn.getOrDie("JSON")},Oo=function(t,n,e){return ko().stringify(t,n,e)},Eo=function(t){return S(t)&&100<J(t).length?" removed due to size":Oo(t,null,2)},To=function(t,n){return go([{path:t,getErrorInfo:n}])},Bo=gt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Ao=function(e,o,r){return st(o,r).fold(function(){return t=r,n=o,To(e,function(){return'Could not find valid *strict* value for "'+t+'" in '+Eo(n)});var t,n},lo)},Do=function(t,n,e){var o=st(t,n).fold(function(){return e(t)},d);return lo(o)},_o=function(a,c,t,s){return t.fold(function(r,e,t,o){var i=function(t){var n=o.extract(a.concat([r]),s,t);return vo(n,function(t){return ft(e,s(t))})},u=function(t){return t.fold(function(){var t=ft(e,s(tt.none()));return lo(t)},function(t){var n=o.extract(a.concat([r]),s,t);return vo(n,function(t){return ft(e,s(tt.some(t)))})})};return t.fold(function(){return po(Ao(a,c,r),i)},function(t){return po(Do(c,r,t),i)},function(){return po(lo(st(c,r)),u)},function(t){return po((e=t,o=st(n=c,r).map(function(t){return!0===t?e(n):t}),lo(o)),u);var n,e,o},function(t){var n=t(c),e=vo(Do(c,r,Z({})),function(t){return vt(n,t)});return po(e,i)})},function(t,n){var e=n(c);return lo(ft(t,s(e)))})},Mo=function(o){return{extract:function(e,t,n){return ho(o(n,t),function(t){return n=t,To(e,function(){return n});var n})},toString:function(){return"val"},toDsl:function(){return So.itemOf(o)}}},Fo=function(t){var c=Io(t),s=I(t,function(n,t){return t.fold(function(t){return vt(n,Ct(t,!0))},Z(n))},{});return{extract:function(t,n,e){var o,r,i,u=C(e)?[]:(r=J(o=e),F(r,function(t){return Et(o,t)})),a=F(u,function(t){return!Et(s,t)});return 0===a.length?c.extract(t,n,e):(i=a,To(t,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:c.toString,toDsl:c.toDsl}},Io=function(a){return{extract:function(t,n,e){return o=t,r=e,i=n,u=_(a,function(t){return _o(o,r,t,i)}),xo(u,{});var o,r,i,u},toString:function(){return"obj{\n"+_(a,function(t){return t.fold(function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"},toDsl:function(){return So.objOf(_(a,function(t){return t.fold(function(t,n,e,o){return Co.field(t,e,o)},function(t,n){return Co.state(t)})}))}}},Vo=function(r){return{extract:function(e,o,t){var n=_(t,function(t,n){return r.extract(e.concat(["["+n+"]"]),o,t)});return wo(n)},toString:function(){return"array("+r.toString()+")"},toDsl:function(){return So.arrOf(r)}}},Ro=function(a,c){return{extract:function(e,o,r){var t,n,i=J(r),u=(t=e,n=i,Vo(Mo(a)).extract(t,d,n));return po(u,function(t){var n=_(t,function(t){return Bo.field(t,t,eo(),c)});return Io(n).extract(e,o,r)})},toString:function(){return"setOf("+c.toString()+")"},toDsl:function(){return So.setOf(a,c)}}},Ho=Z(Mo(lo)),No=p(Vo,Io),Po=Bo.state,zo=Bo.field,Lo=function(e,n,o,r,i){return St(r,i).fold(function(){return t=r,n=i,To(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+Eo(t)});var t,n},function(t){return Io(t).extract(e.concat(["branch: "+i]),n,o)})},jo=function(r,i){return{extract:function(n,e,o){return St(o,r).fold(function(){return t=r,To(n,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return Lo(n,e,o,i,t)})},toString:function(){return"chooseOn("+r+"). Possible values: "+J(i)},toDsl:function(){return So.choiceOf(r,i)}}},Uo=Mo(lo),Wo=function(n){return Mo(function(t){return n(t).fold(go,lo)})},Go=function(n,t){return Ro(function(t){return so(n(t))},t)},Xo=function(t,n,e){return fo((o=t,r=d,i=e,u=n.extract([o],r,i),bo(u,function(t){return{input:i,errors:t}})));var o,r,i,u},Yo=function(t){return t.fold(function(t){throw new Error(Ko(t))},d)},qo=function(t,n,e){return Yo(Xo(t,n,e))},Ko=function(t){return"Errors: \n"+(n=t.errors,e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n,_(e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}))+"\n\nInput object: "+Eo(t.input);var n,e},Jo=function(t,n){return jo(t,n)},$o=Z(Uo),Qo=function(e,o){return Mo(function(t){var n=typeof t;return e(t)?lo(t):go("Expected type: "+o+" but got: "+n)})},Zo=Qo(O,"number"),tr=Qo(w,"string"),nr=Qo(C,"boolean"),er=Qo(k,"function"),or=function(n){return Wo(function(t){return B(n,t)?mt.value(t):mt.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})},rr=function(t){return zo(t,t,eo(),Ho())},ir=function(t,n){return zo(t,t,eo(),n)},ur=function(t){return ir(t,tr)},ar=function(t,n){return zo(t,t,eo(),or(n))},cr=function(t){return ir(t,er)},sr=function(t,n){return zo(t,t,eo(),Io(n))},fr=function(t,n){return zo(t,t,eo(),No(n))},lr=function(t,n){return zo(t,t,eo(),Vo(n))},dr=function(t){return zo(t,t,oo(),Ho())},mr=function(t,n){return zo(t,t,oo(),n)},gr=function(t){return mr(t,tr)},pr=function(t){return mr(t,er)},hr=function(t,n){return zo(t,t,oo(),Io(n))},vr=function(t,n){return zo(t,t,no(n),Ho())},br=function(t,n,e){return zo(t,t,no(n),e)},yr=function(t,n){return br(t,n,Zo)},xr=function(t,n){return br(t,n,tr)},wr=function(t,n,e){return br(t,n,or(e))},Sr=function(t,n){return br(t,n,nr)},Cr=function(t,n){return br(t,n,er)},kr=function(t,n,e){return zo(t,t,no(n),Io(e))},Or=function(t,n){return Po(t,n)},Er=function(t,n){return ce(t.element(),n.event().target())},Tr=function(t){if(!Et(t,"can")&&!Et(t,"abort")&&!Et(t,"run"))throw new Error("EventHandler defined by: "+Oo(t,null,2)+" does not have can, abort, or run!");return qo("Extracting event.handler",Fo([vr("can",Z(!0)),vr("abort",Z(!1)),vr("run",Q)]),t)},Br=function(e){var n,o,r,i,t=(n=e,o=function(t){return t.can},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return V(n,function(t,n){return t&&o(n).apply(undefined,e)},!0)}),u=(r=e,i=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return V(r,function(t,n){return t||i(n).apply(undefined,e)},!1)});return Tr({can:t,abort:u,run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];M(e,function(t){t.run.apply(undefined,n)})}})},Ar=function(t,n){Fr(t,t.element(),n,{})},Dr=function(t,n,e){Fr(t,t.element(),n,e)},_r=function(t){Ar(t,En())},Mr=function(t,n,e){Fr(t,n,e,{})},Fr=function(t,n,e,o){var r=Je({target:n},o);t.getSystem().triggerEvent(e,n,et(r,Z))},Ir=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event())};function Vr(t,n,e,o,r){return t(e,o)?tt.some(e):k(r)&&r(e)?tt.none():n(e,o,r)}var Rr,Hr,Nr,Pr=function(t){var n=Te(t)?t.dom().parentNode:t.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)},zr=Yt(function(){return Lr(Xn.fromDom(h.document))}),Lr=function(t){var n=t.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return Xn.fromDom(n)},jr=function(t,n,e){for(var o=t.dom(),r=k(e)?e:Z(!1);o.parentNode;){o=o.parentNode;var i=Xn.fromDom(o);if(n(i))return tt.some(i);if(r(i))break}return tt.none()},Ur=function(t,n,e){return Vr(function(t){return n(t)},jr,t,n,e)},Wr=function(t,o){var r=function(t){for(var n=0;n<t.childNodes.length;n++){if(o(Xn.fromDom(t.childNodes[n])))return tt.some(Xn.fromDom(t.childNodes[n]));var e=r(t.childNodes[n]);if(e.isSome())return e}return tt.none()};return r(t.dom())},Gr=function(t,n,e){return Ur(t,function(t){return n(t).isSome()},e).bind(n)},Xr=function(t){return kt(t)},Yr=function(t,n){return{key:t,value:Tr({abort:n})}},qr=function(t){return{key:t,value:Tr({run:function(t,n){n.event().prevent()}})}},Kr=function(t,n){return{key:t,value:Tr({run:n})}},Jr=function(t,n,e){return{key:t,value:Tr({run:function(t){n.apply(undefined,[t].concat(e))}})}},$r=function(t){return function(e){return{key:t,value:Tr({run:function(t,n){Er(t,n)&&e(t,n)}})}}},Qr=function(t,n,e){var o,r,i=n.partUids[e];return r=i,Kr(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){Ir(t,t.element(),o,n)})})},Zr=function(t,r){return Kr(t,function(n,t){var e=t.event(),o=n.getSystem().getByDom(e.target()).fold(function(){return Gr(e.target(),function(t){return n.getSystem().getByDom(t).toOption()},Z(!1)).getOr(n)},function(t){return t});r(n,o,t)})},ti=function(t){return Kr(t,function(t,n){n.cut()})},ni=function(t){return Kr(t,function(t,n){n.stop()})},ei=function(t,n){return $r(t)(n)},oi=$r(Rn()),ri=$r(Hn()),ii=$r(Fn()),ui=(Rr=En(),function(t){return Kr(Rr,t)}),ai=Xr([(Hr=Sn(),Nr=function(t,n){var e,o,r=n.event().originator(),i=n.event().target();return o=i,!(ce(e=r,t.element())&&!ce(e,o)&&(h.console.warn(Sn()+" did not get interpreted by the desired target. \nOriginator: "+He(r)+"\nTarget: "+He(i)+"\nCheck the "+Sn()+" event handlers"),1))},{key:Hr,value:Tr({can:Nr})})]),ci=/* */Object.freeze({events:ai}),si=Z("alloy-id-"),fi=Z("data-alloy-id"),li=si(),di=fi(),mi=function(t,n){Object.defineProperty(t.dom(),di,{value:n,writable:!0})},gi=function(t){var n=Ee(t)?t.dom()[di]:null;return tt.from(n)},pi=function(t){return qe(t)},hi=d,vi=function(n){var t=function(t){return function(){throw new Error("The component must be in a context to send: "+t+"\n"+He(n().element())+" is not in context.")}};return{debugInfo:Z("fake"),triggerEvent:t("triggerEvent"),triggerFocus:t("triggerFocus"),triggerEscape:t("triggerEscape"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),broadcast:t("broadcast"),broadcastOn:t("broadcastOn"),broadcastEvent:t("broadcastEvent"),isConnected:Z(!1)}},bi=vi(),yi=function(t){return _(t,function(t){return o=n="/*",r=(e=t).length-n.length,""!==o&&(e.length<o.length||e.substr(r,r+o.length)!==o)?t:t.substring(0,t.length-"/*".length);var n,e,o,r})},xi=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:yi(i)}},t},wi=qe("alloy-premade"),Si=function(t){return Ct(wi,t)},Ci=function(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(undefined,[t.getApis()].concat([t].concat(n)))},n=o.toString(),e=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:yi(i.slice(1))}},t;var t,n,e,r,i},ki={init:function(){return Oi({readState:function(){return"No State required"}})}},Oi=function(t){return t},Ei=function(t,r){var i={};return nt(t,function(t,o){nt(t,function(t,n){var e=wt(n,[])(i);i[n]=e.concat([r(o,t)])})}),i},Ti=function(t){return{classes:t.classes!==undefined?t.classes:[],attributes:t.attributes!==undefined?t.attributes:{},styles:t.styles!==undefined?t.styles:{}}},Bi=function(t,n){return e=v.apply(undefined,[t.handler].concat(n)),o=t.purpose(),{cHandler:e,purpose:Z(o)};var e,o},Ai=function(t){return t.cHandler},Di=function(t,n){return{name:Z(t),handler:Z(n)}},_i=function(t,n,e){var o,r,i=Je({},e,(o=t,r={},M(n,function(t){r[t.name()]=t.handlers(o)}),r));return Ei(i,Di)},Mi=function(t){var n,i=k(n=t)?{can:Z(!0),abort:Z(!1),run:n}:n;return function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(undefined,r)?n.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}},Fi=function(t,n,e){var o,r,i=n[e];return i?function(u,a,t,c){var n=t.slice(0);try{var e=n.sort(function(t,n){var e=t[a](),o=n[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+Oo(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+Oo(c,null,2));return r<i?-1:i<r?1:0});return mt.value(e)}catch(o){return mt.error([o])}}("Event: "+e,"name",t,i).map(function(t){var n=_(t,function(t){return t.handler()});return Br(n)}):(o=e,r=t,mt.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+Oo(_(r,function(t){return t.name()}),null,2)]))},Ii=function(t,i){var n=rt(t,function(o,r){return(1===o.length?mt.value(o[0].handler()):Fi(o,i,r)).map(function(t){var n=Mi(t),e=1<o.length?F(i,function(n){return B(o,function(t){return t.name()===n})}).join(" > "):o[0].name();return Ct(r,{handler:n,purpose:Z(e)})})});return Ot(n,{})},Vi=function(t){return Xo("custom.definition",Io([zo("dom","dom",eo(),Io([rr("tag"),vr("styles",{}),vr("classes",[]),vr("attributes",{}),dr("value"),dr("innerHtml")])),rr("components"),rr("uid"),vr("events",{}),vr("apis",{}),zo("eventOrder","eventOrder",(n={"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]},to.mergeWithThunk(Z(n))),$o()),dr("domModification")]),t);var n},Ri=function(t,n){var e=Me(t,n);return e===undefined||""===e?[]:e.split(" ")},Hi=function(t){return t.dom().classList!==undefined},Ni=function(t,n){return r=n,i=Ri(e=t,o="class").concat([r]),De(e,o,i.join(" ")),!0;var e,o,r,i},Pi=function(t,n){return r=n,0<(i=F(Ri(e=t,o="class"),function(t){return t!==r})).length?De(e,o,i.join(" ")):Ie(e,o),!1;var e,o,r,i},zi=function(t,n){Hi(t)?t.dom().classList.add(n):Ni(t,n)},Li=function(t){0===(Hi(t)?t.dom().classList:Ri(t,"class")).length&&Ie(t,"class")},ji=function(t,n){Hi(t)?t.dom().classList.remove(n):Pi(t,n),Li(t)},Ui=function(t,n){return Hi(t)&&t.dom().classList.contains(n)},Wi=function(n,t){M(t,function(t){zi(n,t)})},Gi=function(n,t){M(t,function(t){ji(n,t)})},Xi=function(t){return t.style!==undefined},Yi=function(t,n,e){if(!w(e))throw h.console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);Xi(t)&&t.style.setProperty(n,e)},qi=function(t,n){Xi(t)&&t.style.removeProperty(n)},Ki=function(t,n,e){var o=t.dom();Yi(o,n,e)},Ji=function(t,n){var e=t.dom();nt(n,function(t,n){Yi(e,n,t)})},$i=function(t,n){var e=t.dom(),o=h.window.getComputedStyle(e).getPropertyValue(n),r=""!==o||Pr(t)?o:Qi(e,n);return null===r?undefined:r},Qi=function(t,n){return Xi(t)?t.style.getPropertyValue(n):""},Zi=function(t,n){var e=t.dom(),o=Qi(e,n);return tt.from(o).filter(function(t){return 0<t.length})},tu=function(t,n,e){var o=Xn.fromTag(t);return Ki(o,n,e),Zi(o,n).isSome()},nu=function(t,n){var e=t.dom();qi(e,n),Fe(t,"style")&&""===Me(t,"style").replace(/^\s+|\s+$/g,"")&&Ie(t,"style")},eu=function(t){return t.dom().offsetWidth},ou=function(t){return t.dom().value},ru=function(t,n){if(n===undefined)throw new Error("Value.set was undefined");t.dom().value=n},iu=function(t,n){return e=t,r=_(o=n,function(t){return hr(t.name(),[rr("config"),vr("state",ki)])}),i=Xo("component.behaviours",Io(r),e.behaviours).fold(function(t){throw new Error(Ko(t)+"\nComplete spec:\n"+Oo(e,null,2))},function(t){return t}),{list:o,data:et(i,function(t){var n=t.map(function(t){return{config:t.config,state:t.state.init(t.config)}});return function(){return n}})};var e,o,r,i},uu=function(t){var n,e,o,r=(n=t,e=wt("behaviours",{})(n),o=F(J(e),function(t){return e[t]!==undefined}),_(o,function(t){return e[t].me}));return iu(t,r)},au=function(t,n,e){var o,r,i,u=Je({},(o=t).dom,{uid:o.uid,domChildren:_(o.components,function(t){return t.element()})}),a=t.domModification.fold(function(){return Ti({})},Ti),c={"alloy.base.modification":a},s=0<n.length?function(n,t,e,o){var r=Je({},t);M(e,function(t){r[t.name()]=t.exhibit(n,o)});var i=Ei(r,function(t,n){return{name:t,modification:n}}),u=function(t){return I(t,function(t,n){return Je({},n.modification,t)},{})},a=I(i.classes,function(t,n){return n.modification.concat(t)},[]),c=u(i.attributes),s=u(i.styles);return Ti({classes:a,attributes:c,styles:s})}(e,c,n,u):a;return i=s,Je({},r=u,{attributes:Je({},r.attributes,i.attributes),styles:Je({},r.styles,i.styles),classes:r.classes.concat(i.classes)})},cu=function(t,n,e){var o,r,i,u,a,c,s={"alloy.base.behaviour":(o=t,o.events)};return(r=e,i=t.eventOrder,u=n,a=s,c=_i(r,u,a),Ii(c,i)).getOrDie()},su=function(e){var t=function(){return f},o=Tt(bi),n=Yo(Vi(e)),r=uu(e),i=r.list,u=r.data,a=function(t){var n=Xn.fromTag(t.tag);_e(n,t.attributes),Wi(n,t.classes),Ji(n,t.styles),t.innerHtml.each(function(t){return Ce(n,t)});var e=t.domChildren;return ye(n,e),t.value.each(function(t){ru(n,t)}),t.uid,mi(n,t.uid),n}(au(n,i,u)),c=cu(n,i,u),s=Tt(n.components),f={getSystem:o.get,config:function(t){var n=u;return(k(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+Oo(e,null,2))})()},hasConfigured:function(t){return k(u[t.name()])},spec:Z(e),readState:function(t){return u[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return n.apis},connect:function(t){o.set(t)},disconnect:function(){o.set(vi(t))},element:Z(a),syncComponents:function(){var t=me(a),n=L(t,function(t){return o.get().getByDom(t).fold(function(){return[]},function(t){return[t]})});s.set(n)},components:s.get,events:Z(c)};return f},fu=function(t){var n,e,o=hi(t),r=o.events,i=$e(o,["events"]),u=(n=i,e=wt("components",[])(n),_(e,gu)),a=Je({},i,{events:Je({},ci,r),components:u});return mt.value(su(a))},lu=function(t){var n=Xn.fromText(t);return du({element:n})},du=function(t){var n=qo("external.component",Fo([rr("element"),dr("uid")]),t),e=Tt(vi());n.uid.each(function(t){mi(n.element,t)});var o={getSystem:e.get,config:tt.none,hasConfigured:Z(!1),connect:function(t){e.set(t)},disconnect:function(){e.set(vi(function(){return o}))},getApis:function(){return{}},element:Z(n.element),spec:Z(t),readState:Z("No state"),syncComponents:Q,components:Z([]),events:Z({})};return Si(o)},mu=pi,gu=function(n){return(t=n,St(t,wi)).fold(function(){var t=n.hasOwnProperty("uid")?n:Je({uid:mu("")},n);return fu(t).getOrDie()},function(t){return t});var t},pu=Si,hu=function(t,n,e){return jr(t,function(t){return ie(t,n)},e)},vu=function(t,n){return e=n,r=(o=t)===undefined?h.document:o.dom(),ue(r)?tt.none():tt.from(r.querySelector(e)).map(Xn.fromDom);var e,o,r},bu=function(t,n,e){return Vr(ie,hu,t,n,e)},yu=function(n,t){return(e=t,Ur(e,function(t){if(!Ee(t))return!1;var n=Me(t,"id");return n!==undefined&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=Me(t,"id"),e=se(t);return vu(e,'[aria-owns="'+n+'"]')})).exists(function(t){return xu(n,t)});var e},xu=function(n,t){return e=t,r=Z(!(o=function(t){return ce(t,n.element())})),Ur(e,o,r).isSome()||yu(n,t);var e,o,r},wu=Z([rr("menu"),rr("selectedMenu")]),Su=Z([rr("item"),rr("selectedItem")]),Cu=(Z(Io(Su().concat(wu()))),Z(Io(Su()))),ku=sr("initSize",[rr("numColumns"),rr("numRows")]),Ou=function(){return sr("markers",[rr("backgroundMenu")].concat(wu()).concat(Su()))},Eu=function(t){return sr("markers",_(t,rr))},Tu=function(t,n,e){return function(){var t=new Error;if(t.stack!==undefined){var n=t.stack.split("\n");R(n,function(n){return 0<n.indexOf("alloy")&&!A(je,function(t){return-1<n.indexOf(t)})}).getOr(Ne)}}(),zo(n,n,e,Wo(function(e){return mt.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(undefined,t)})}))},Bu=function(t){return Tu(0,t,no(Q))},Au=function(t){return Tu(0,t,no(tt.none))},Du=function(t){return Tu(0,t,eo())},_u=function(t){return Tu(0,t,eo())},Mu=function(t,n){return Or(t,Z(n))},Fu=function(t){return Or(t,d)},Iu=Z(ku),Vu=function(e,o,r){return ii(function(t,n){r(t,e,o)})},Ru=function(t,n,e,o,r,i){var u,a,c=Fo(t),s=hr(n,[(u="config",a=t,zo(u,u,oo(),Fo(a)))]);return Pu(c,s,n,e,o,r,i)},Hu=function(r,i,u){var t,n,e,o,a,c;return t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:Z(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,t.config,t.state].concat(n))})},n=u,e=i.toString(),o=e.indexOf(")")+1,a=e.indexOf("("),c=e.substring(a+1,o-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:n,parameters:yi(c.slice(0,1).concat(c.slice(3)))}},t},Nu=function(t){return{key:t,value:undefined}},Pu=function(e,t,o,r,n,i,u){var a=function(t){return Et(t,o)?t[o]():tt.none()},c=et(n,function(t,n){return Hu(o,t,n)}),s=et(i,function(t,n){return xi(t,n)}),f=Je({},s,c,{revoke:v(Nu,o),config:function(t){var n=qo(o+"-config",e,t);return{key:o,value:{config:n,me:f,configAsRaw:Yt(function(){return qo(o+"-config",e,t)}),initialConfig:t,state:u}}},schema:function(){return t},exhibit:function(t,e){return a(t).bind(function(n){return St(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(Ti({}))},name:function(){return o},handlers:function(t){return a(t).map(function(t){return wt("events",function(t,n){return{}})(r)(t.config,t.state)}).getOr({})}});return f},zu=function(t){return kt(t)},Lu=Fo([rr("fields"),rr("name"),vr("active",{}),vr("apis",{}),vr("state",ki),vr("extra",{})]),ju=function(t){var n=qo("Creating behaviour: "+t.name,Lu,t);return Ru(n.fields,n.name,n.active,n.apis,n.extra,n.state)},Uu=Fo([rr("branchKey"),rr("branches"),rr("name"),vr("active",{}),vr("apis",{}),vr("state",ki),vr("extra",{})]),Wu=function(t){var n,e,o,r,i,u,a,c,s=qo("Creating behaviour: "+t.name,Uu,t);return n=Jo(s.branchKey,s.branches),e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,c=hr(e,[mr("config",a=n)]),Pu(a,c,e,o,r,i,u)},Gu=Z(undefined),Xu=/* */Object.freeze({events:function(a){return Xr([Kr(On(),function(r,i){var t,n,u=a.channels,e=J(u),o=(t=e,(n=i).universal()?t:F(t,function(t){return B(n.channels(),t)}));M(o,function(t){var n=u[t],e=n.schema,o=qo("channel["+t+"] data\nReceiver: "+He(r.element()),e,i.data());n.onReceive(r,o)})})])}}),Yu=[ir("channels",Go(mt.value,Fo([Du("onReceive"),vr("schema",$o())])))],qu=ju({fields:Yu,name:"receiving",active:Xu}),Ku=/* */Object.freeze({exhibit:function(t,n){return Ti({classes:[],styles:n.useFixed?{}:{position:"relative"}})}}),Ju=function(e,o){return{left:Z(e),top:Z(o),translate:function(t,n){return Ju(e+t,o+n)}}},$u=Ju,Qu=function(t,n){return t!==undefined?t:n!==undefined?n:0},Zu=function(t){var n,e,o=t.dom().ownerDocument,r=o.body,i=(n=Xn.fromDom(o),(e=n.dom())===e.window&&n instanceof h.Window?n:Be(n)?e.defaultView||e.parentWindow:null),u=o.documentElement,a=Qu(i.pageYOffset,u.scrollTop),c=Qu(i.pageXOffset,u.scrollLeft),s=Qu(u.clientTop,r.clientTop),f=Qu(u.clientLeft,r.clientLeft);return ta(t).translate(c-f,a-s)},ta=function(t){var n,e,o,r=t.dom(),i=r.ownerDocument,u=i.body,a=Xn.fromDom(i.documentElement);return u===r?$u(u.offsetLeft,u.offsetTop):(n=t,e=a||Xn.fromDom(h.document.documentElement),jr(n,v(ce,e)).isSome()?(o=r.getBoundingClientRect(),$u(o.left,o.top)):$u(0,0))},na=(xn.detect().browser.isSafari(),function(t){var n=t!==undefined?t.dom():h.document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return $u(e,o)});function ea(o,r){var t=function(t){var n=r(t);if(n<=0||null===n){var e=$i(t,o);return parseFloat(e)||0}return n},i=function(r,t){return V(t,function(t,n){var e=$i(r,n),o=e===undefined?0:parseInt(e,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!O(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom();Xi(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}var oa,ra,ia=ea("width",function(t){return t.dom().offsetWidth}),ua=function(t){return ia.get(t)},aa=function(t){return ia.getOuter(t)},ca=ea("height",function(t){var n=t.dom();return Pr(t)?n.getBoundingClientRect().height:n.offsetHeight}),sa=function(t){return ca.get(t)},fa=function(t){return ca.getOuter(t)},la=Jn(["x","y","width","height","maxHeight","direction","classes","label","candidateYforTest"],[]),da=Yn("position","left","top","right","bottom"),ma=gt([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ga=ma.southeast,pa=ma.southwest,ha=ma.northeast,va=ma.northwest,ba=ma.south,ya=ma.north,xa=ma.east,wa=ma.west,Sa=Yn("point","width","height"),Ca=Yn("x","y","width","height"),ka=function(t,n,e,o){return{x:Z(t),y:Z(n),width:Z(e),height:Z(o),right:Z(t+e),bottom:Z(n+o)}},Oa=function(t){var n=Zu(t),e=aa(t),o=fa(t);return ka(n.left(),n.top(),e,o)},Ea=function(o,t){return o.view(t).fold(Z([]),function(t){var n=o.owner(t),e=Ea(o,n);return[t].concat(e)})},Ta=/* */Object.freeze({view:function(t){return(t.dom()===h.document?tt.none():tt.from(t.dom().defaultView.frameElement)).map(Xn.fromDom)},owner:function(t){return se(t)}}),Ba=function(o){var t,n,e,r,i=Xn.fromDom(h.document),u=na(i);return(t=o,e=(n=Ta).owner(t),r=Ea(n,e),tt.some(r)).fold(v(Zu,o),function(t){var n=ta(o),e=I(t,function(t,n){var e=ta(n);return{left:t.left+e.left(),top:t.top+e.top()}},{left:0,top:0});return $u(e.left+n.left()+u.left(),e.top+n.top()+u.top())})},Aa=function(){var t=h.window.innerWidth,n=h.window.innerHeight,e=Xn.fromDom(h.document),o=na(e);return ka(o.left(),o.top(),t,n)},Da=gt([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),_a=function(t,n,e,o,r,i){var u,a,c,s,f,l,d,m,g,p=n.x()-e,h=n.y()-o,v=r-(p+n.width()),b=i-(h+n.height()),y=tt.some(p),x=tt.some(h),w=tt.some(v),S=tt.some(b),C=tt.none();return u=n.direction(),a=function(){return da(t,y,x,C,C)},c=function(){return da(t,C,x,w,C)},s=function(){return da(t,y,C,C,S)},f=function(){return da(t,C,C,w,S)},l=function(){return da(t,y,x,C,C)},d=function(){return da(t,y,C,C,S)},m=function(){return da(t,y,x,C,C)},g=function(){return da(t,C,x,w,C)},u.fold(a,c,s,f,l,d,m,g)},Ma=function(t,n){var e=v(Ba,n),o=t.fold(e,e,function(){var t=na();return Ba(n).translate(-t.left(),-t.top())}),r=aa(n),i=fa(n);return ka(o.left(),o.top(),r,i)},Fa=Da.relative,Ia=Da.fixed,Va=Yn("anchorBox","origin"),Ra=gt([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Ha=function(t,H,N,P,z){var L=N.width(),j=N.height(),o=function(t,o,r,i){var n,e,u,a,c,s,f,l,d,m,g,p,h,v,b,y,x,w,S,C,k,O,E,T,B,A,D,_,M,F,I,V,R=t(H,N,P);return(e=L,u=j,a=z,d=(n=R).x(),m=n.y(),g=n.bubble().offset().left(),p=n.bubble().offset().top(),h=a.x(),v=a.y(),b=a.width(),y=a.height(),C=v<=(w=m+p),k=(S=h<=(x=d+g))&&C,O=x+e<=h+b&&w+u<=v+y,E=S?Math.min(e,h+b-x):Math.abs(h-(x+e)),T=C?Math.min(u,v+y-w):Math.abs(v-(w+u)),B=a.x()+a.width(),A=Math.max(a.x(),x),D=Math.min(A,B),M=Z((_=C?w:w+(u-T))+T-v),F=Z(v+y-_),c=n.direction(),f=s=F,l=M,I=c.fold(s,s,l,l,s,l,f,f),V=la({x:D,y:_,width:E,height:T,maxHeight:I,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:w}),k&&O?Ra.fit(V):Ra.nofit(V,E,T)).fold(Ra.fit,function(t,n,e){return i<e||r<n?Ra.nofit(t,n,e):Ra.nofit(o,r,i)})};return V(t,function(t,n){var e=v(o,n);return t.fold(Ra.fit,e)},Ra.nofit(la({x:H.x(),y:H.y(),width:N.width(),height:N.height(),maxHeight:N.height(),direction:ga(),classes:[],label:"none",candidateYforTest:H.y()}),-1,-1)).fold(d,d)},Na=function(t,n,e,o){nu(n,"max-height");var r,i={width:Z(aa(r=n)),height:Z(fa(r))};return Ha(o.preference(),t,i,e,o.bounds())},Pa=function(t,n,e){var o,r,i,u,a,c=function(t){return t+"px"},s=(o=e.origin(),r=n,o.fold(function(){return da("absolute",tt.some(r.x()),tt.some(r.y()),tt.none(),tt.none())},function(t,n,e,o){return _a("absolute",r,t,n,e,o)},function(t,n,e,o){return _a("fixed",r,t,n,e,o)}));i=t,u={position:tt.some(s.position()),left:s.left().map(c),top:s.top().map(c),right:s.right().map(c),bottom:s.bottom().map(c)},a=i.dom(),nt(u,function(t,n){t.fold(function(){qi(a,n)},function(t){Yi(a,n,t)})})},za=function(t,n){var e,o,r;e=t,o=Math.floor(n),r=ca.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]),Ki(e,"max-height",r+"px")},La=Z(function(t,n){za(t,n),Ji(t,{"overflow-x":"hidden","overflow-y":"auto"})}),ja=Z(function(t,n){za(t,n)}),Ua=Jn(["bounds","origin","preference","maxHeightFunction"],[]),Wa=function(t,n,e,o,r,i){var u,a,c,s,f,l=(u=i,a="maxHeightFunction",c=La(),u[a]===undefined?c:u[a]),d=t.anchorBox(),m=t.origin(),g=Ua({bounds:(s=m,f=r,f.fold(function(){return s.fold(Aa,Aa,ka)},function(t){return s.fold(t,t,ka)})),origin:m,preference:o,maxHeightFunction:l});Ga(d,n,e,g)},Ga=function(t,n,e,o){var r,i,u,a,c=Na(t,n,e,o);Pa(n,c,o),r=n,i=c.classes(),Gi(r,i.off),Wi(r,i.on),u=n,a=c,o.maxHeightFunction()(u,a.maxHeight())},Xa=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Ya=function(t,n,e){var r=function(t){return St(e,t).getOr([])},o=function(t,n,e){var o=G(Xa,e);return{offset:function(){return $u(t,n)},classesOn:function(){return L(e,r)},classesOff:function(){return L(o,r)}}};return{southeast:function(){return o(-t,n,["top","alignLeft"])},southwest:function(){return o(t,n,["top","alignRight"])},south:function(){return o(-t/2,n,["top","alignCentre"])},northeast:function(){return o(-t,-n,["bottom","alignLeft"])},northwest:function(){return o(t,-n,["bottom","alignRight"])},north:function(){return o(-t/2,-n,["bottom","alignCentre"])},east:function(){return o(t,-n/2,["valignCentre","left"])},west:function(){return o(-t,-n/2,["valignCentre","right"])}}},qa=function(){return Ya(0,0,{})},Ka=Yn("x","y","bubble","direction","label"),Ja=function(t){return t.x()},$a=function(t,n){return t.x()+t.width()/2-n.width()/2},Qa=function(t,n){return t.x()+t.width()-n.width()},Za=function(t,n){return t.y()-n.height()},tc=function(t){return t.y()+t.height()},nc=function(t,n){return t.y()+t.height()/2-n.height()/2},ec=function(t,n,e){return Ka(Ja(t),tc(t),e.southeast(),ga(),"layout-se")},oc=function(t,n,e){return Ka(Qa(t,n),tc(t),e.southwest(),pa(),"layout-sw")},rc=function(t,n,e){return Ka(Ja(t),Za(t,n),e.northeast(),ha(),"layout-ne")},ic=function(t,n,e){return Ka(Qa(t,n),Za(t,n),e.northwest(),va(),"layout-nw")},uc=function(t,n,e){return Ka($a(t,n),Za(t,n),e.north(),ya(),"layout-n")},ac=function(t,n,e){return Ka($a(t,n),tc(t),e.south(),ba(),"layout-s")},cc=function(t,n,e){return Ka((o=t).x()+o.width(),nc(t,n),e.east(),xa(),"layout-e");var o},sc=function(t,n,e){return Ka((o=n,t.x()-o.width()),nc(t,n),e.west(),wa(),"layout-w");var o},fc=function(){return[ec,oc,rc,ic,ac,uc,cc,sc]},lc=function(){return[oc,ec,ic,rc,ac,uc,cc,sc]},dc=function(t){return t},mc=function(n,e){return function(t){return"rtl"===gc(t)?e:n}},gc=function(t){return"rtl"===$i(t,"direction")?"rtl":"ltr"},pc=function(){return hr("layouts",[rr("onLtr"),rr("onRtl")])},hc=function(n,t,e,o){var r=t.layouts.map(function(t){return t.onLtr(n)}).getOr(e),i=t.layouts.map(function(t){return t.onRtl(n)}).getOr(o);return mc(r,i)(n)},vc=[rr("hotspot"),dr("bubble"),pc(),Mu("placement",function(t,n,e){var o=n.hotspot,r=Ma(e,o.element()),i=hc(t.element(),n,fc(),lc());return tt.some(dc({anchorBox:r,bubble:n.bubble.getOr(qa()),overrides:{},layouts:i,placer:tt.none()}))})],bc=[rr("x"),rr("y"),vr("height",0),vr("width",0),vr("bubble",qa()),pc(),Mu("placement",function(t,n,e){var o=ka(n.x,n.y,n.width,n.height),r=hc(t.element(),n,fc(),lc());return tt.some(dc({anchorBox:o,bubble:n.bubble,overrides:{},layouts:r,placer:tt.none()}))})],yc=(gt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),gt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}])),xc=Yn("start","soffset","finish","foffset"),wc=(yc.domRange,yc.relative,yc.exact),Sc=function(t,n,e,o){var r,i,u,a,c,s=(i=n,u=e,a=o,(c=se(r=t).dom().createRange()).setStart(r.dom(),i),c.setEnd(u.dom(),a),c),f=ce(t,e)&&n===o;return s.collapsed&&!f},Cc=function(t,n,e){var o,r,i=t.document.createRange();return o=i,n.fold(function(t){o.setStartBefore(t.dom())},function(t,n){o.setStart(t.dom(),n)},function(t){o.setStartAfter(t.dom())}),r=i,e.fold(function(t){r.setEndBefore(t.dom())},function(t,n){r.setEnd(t.dom(),n)},function(t){r.setEndAfter(t.dom())}),i},kc=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom(),e),i.setEnd(o.dom(),r),i},Oc=function(t){return{left:Z(t.left),top:Z(t.top),right:Z(t.right),bottom:Z(t.bottom),width:Z(t.width),height:Z(t.height)}},Ec=gt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Tc=function(t,n,e){return n(Xn.fromDom(e.startContainer),e.startOffset,Xn.fromDom(e.endContainer),e.endOffset)},Bc=function(t,n){var r,e,o,i=(r=t,n.match({domRange:function(t){return{ltr:Z(t),rtl:tt.none}},relative:function(t,n){return{ltr:Yt(function(){return Cc(r,t,n)}),rtl:Yt(function(){return tt.some(Cc(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Yt(function(){return kc(r,t,n,e,o)}),rtl:Yt(function(){return tt.some(kc(r,e,o,t,n))})}}}));return(o=(e=i).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return Ec.rtl(Xn.fromDom(t.endContainer),t.endOffset,Xn.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return Tc(0,Ec.ltr,o)}):Tc(0,Ec.ltr,o)},Ac=function HA(e,o){var r=function(t){return e(t)?tt.from(t.dom().nodeValue):tt.none()},t=xn.detect().browser,n=t.isIE()&&10===t.version.major?function(t){try{return r(t)}catch(n){return tt.none()}}:r;return{get:function(t){if(!e(t))throw new Error("Can only get "+o+" value of a "+o+" node");return n(t).getOr("")},getOption:n,set:function(t,n){if(!e(t))throw new Error("Can only set raw "+o+" value of a "+o+" node");t.dom().nodeValue=n}}}(Te,"text"),Dc=function(t){return Ac.get(t)},_c=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(t,n){return ae(n,t)}),Mc=function(t){var n=Xn.fromDom(t.anchorNode),e=Xn.fromDom(t.focusNode);return Sc(n,t.anchorOffset,e,t.focusOffset)?tt.some(xc(n,t.anchorOffset,e,t.focusOffset)):function(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return tt.some(xc(Xn.fromDom(n.startContainer),n.startOffset,Xn.fromDom(e.endContainer),e.endOffset))}return tt.none()}(t)},Fc=function(t,n){var i,e,o,r,u=Bc(i=t,n).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom(),n),r.setEnd(e.dom(),o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(t.dom(),n),r}});return o=(e=u).getClientRects(),0<(r=0<o.length?o[0]:e.getBoundingClientRect()).width||0<r.height?tt.some(r).map(Oc):tt.none()},Ic=Yn("element","offset"),Vc=gt([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Rc=function(t){return t.fold(function(t){return t},function(t,n,e){return t.translate(-n,-e)})},Hc=function(t){return t.fold(function(t){return t},function(t,n,e){return t})},Nc=function(t){return V(t,function(t,n){return t.translate(n.left(),n.top())},$u(0,0))},Pc=function(t){var n=_(t,Hc);return Nc(n)},zc=Vc.screen,Lc=Vc.absolute,jc=function(t,n,e){var o,r,i,u=se(t.element()),a=na(u),c=(o=t,r=e,i=fe(r.root).dom(),tt.from(i.frameElement).map(Xn.fromDom).filter(function(t){var n=se(t),e=se(o.element());return ce(n,e)}).map(Zu)).getOr(a);return Lc(c,a.left(),a.top())},Uc=function(t,n,e,o){var r=t,i=n,u=e,a=o;t<0&&(r=0,u=e+t),n<0&&(i=0,a=o+n);var c=zc($u(r,i));return tt.some(Sa(c,u,a))},Wc=function(t,c,s,f,l){return t.map(function(t){var n,e,o,r=[c,t.point()],i=(n=function(){return Pc(r)},e=function(){return Pc(r)},o=function(){return t=_(r,Rc),Nc(t);var t},f.fold(n,e,o)),u=Ca(i.left(),i.top(),t.width(),t.height()),a=hc(l,s,s.showAbove?[rc,ic,ec,oc,uc,ac]:[ec,oc,rc,ic,ac,ac],s.showAbove?[ic,rc,oc,ec,uc,ac]:[oc,ec,ic,rc,ac,uc]);return dc({anchorBox:u,bubble:s.bubble.getOr(qa()),overrides:s.overrides,layouts:a,placer:tt.none()})})},Gc=Yn("element","offset"),Xc=function(t,n){return Te(t)?Gc(t,n):function(t,n){var e=me(t);if(0===e.length)return Ic(t,n);if(n<e.length)return Ic(e[n],0);var o=e[e.length-1],r=Te(o)?Dc(o).length:me(o).length;return Ic(o,r)}(t,n)},Yc=function(n,t){return t.getSelection.getOrThunk(function(){return function(){return t=n,tt.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(Mc);var t}})().map(function(t){var n=Xc(t.start(),t.soffset()),e=Xc(t.finish(),t.foffset());return xc(n.element(),n.offset(),e.element(),e.offset())})},qc=[dr("getSelection"),rr("root"),dr("bubble"),pc(),vr("overrides",{}),vr("showAbove",!1),Mu("placement",function(t,n,e){var o=fe(n.root).dom(),r=jc(t,0,n),i=Yc(o,n).bind(function(t){var n;return Fc(o,(n=t,yc.exact(n.start(),n.soffset(),n.finish(),n.foffset()))).orThunk(function(){var n=Xn.fromText("\ufeff");return pe(t.start(),n),Fc(o,wc(n,0,n,1)).map(function(t){return we(n),t})}).bind(function(t){return Uc(t.left(),t.top(),t.width(),t.height())})}),u=Yc(o,n).bind(function(t){return Ee(t.start())?tt.some(t.start()):le(t.start())}).getOr(t.element());return Wc(i,r,n,e,u)})],Kc=[rr("node"),rr("root"),dr("bubble"),pc(),vr("overrides",{}),vr("showAbove",!1),Mu("placement",function(r,i,u){var a=jc(r,0,i);return i.node.bind(function(t){var n=t.dom().getBoundingClientRect(),e=Uc(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element());return Wc(e,a,i,u,o)})})],Jc=function(t){return t.x()+t.width()},$c=function(t,n){return t.x()-n.width()},Qc=function(t,n){return t.y()-n.height()+t.height()},Zc=function(t){return t.y()},ts=function(t,n,e){return Ka(Jc(t),Zc(t),e.southeast(),ga(),"link-layout-se")},ns=function(t,n,e){return Ka($c(t,n),Zc(t),e.southwest(),pa(),"link-layout-sw")},es=function(t,n,e){return Ka(Jc(t),Qc(t,n),e.northeast(),ha(),"link-layout-ne")},os=function(t,n,e){return Ka($c(t,n),Qc(t,n),e.northwest(),va(),"link-layout-nw")},rs=[rr("item"),pc(),Mu("placement",function(t,n,e){var o=Ma(e,n.item.element()),r=hc(t.element(),n,[ts,ns,es,os],[ns,ts,os,es]);return tt.some(dc({anchorBox:o,bubble:qa(),overrides:{},layouts:r,placer:tt.none()}))})],is=Jo("anchor",{selection:qc,node:Kc,hotspot:vc,submenu:rs,makeshift:bc}),us=function(t,n,e,o,r){var i,u=(i=e.anchorBox,Va(i,n));Wa(u,r.element(),e.bubble,e.layouts,o,e.overrides)},as=function(n,t,e,o,r,i){var u=qo("positioning anchor.info",is,o);Ki(r.element(),"position","fixed");var a=Zi(r.element(),"visibility");Ki(r.element(),"visibility","hidden");var c,s,f,l=t.useFixed?Ia(0,0,h.window.innerWidth,h.window.innerHeight):(s=Zu((c=n).element()),f=c.element().dom().getBoundingClientRect(),Fa(s.left(),s.top(),f.width,f.height)),d=u.placement,m=i.map(function(t){return function(){return Oa(t)}}).or(t.getBounds);d(n,u,l).each(function(t){t.placer.getOr(us)(n,l,t,m,r)}),a.fold(function(){nu(r.element(),"visibility")},function(t){Ki(r.element(),"visibility",t)}),Zi(r.element(),"left").isNone()&&Zi(r.element(),"top").isNone()&&Zi(r.element(),"right").isNone()&&Zi(r.element(),"bottom").isNone()&&Zi(r.element(),"position").is("fixed")&&nu(r.element(),"position")},cs=/* */Object.freeze({position:function(t,n,e,o,r){var i=tt.none();as(t,n,e,o,r,i)},positionWithin:as,getMode:function(t,n,e){return n.useFixed?"fixed":"absolute"}}),ss=[vr("useFixed",!1),dr("getBounds")],fs=ju({fields:ss,name:"positioning",active:Ku,apis:cs}),ls=function(t){Ar(t,Hn());var n=t.components();M(n,ls)},ds=function(t){var n=t.components();M(n,ds),Ar(t,Rn())},ms=function(t,n){gs(t,n,be)},gs=function(t,n,e){t.getSystem().addToWorld(n),e(t.element(),n.element()),Pr(t.element())&&ds(n),t.syncComponents()},ps=function(t){ls(t),we(t.element()),t.getSystem().removeFromWorld(t)},hs=function(n){var t=le(n.element()).bind(function(t){return n.getSystem().getByDom(t).fold(tt.none,tt.some)});ps(n),t.each(function(t){t.syncComponents()})},vs=function(t){var n=t.components();M(n,ps),xe(t.element()),t.syncComponents()},bs=function(t,n){ys(t,n,be)},ys=function(t,n,e){e(t,n.element());var o=me(n.element());M(o,function(t){n.getByDom(t).each(ds)})},xs=function(n){var t=me(n.element());M(t,function(t){n.getByDom(t).each(ls)}),we(n.element())},ws=function(t,n,e,o){var r=function(n,t,e,o){e.get().each(function(t){vs(n)});var r=t.getAttachPoint(n);ms(r,n);var i=n.getSystem().build(o);return ms(n,i),e.set(i),i}(t,n,e,o);return n.onOpen(t,r),r},Ss=function(n,e,o){o.get().each(function(t){vs(n),hs(n),e.onClose(n,t),o.clear()})},Cs=function(t,n,e){return e.isOpen()},ks=function(t,n,e){var o,r,i,u,a=n.getAttachPoint(t);Ki(t.element(),"position",fs.getMode(a)),o=t,r="visibility",i=n.cloakVisibilityAttr,u="hidden",Zi(o.element(),r).fold(function(){Ie(o.element(),i)},function(t){De(o.element(),i,t)}),Ki(o.element(),r,u)},Os=function(t,n,e){var o;o=t.element(),A(["top","left","right","bottom"],function(t){return Zi(o,t).isSome()})||nu(t.element(),"position"),function(t,n,e){if(Fe(t.element(),e)){var o=Me(t.element(),e);Ki(t.element(),n,o)}else nu(t.element(),n)}(t,"visibility",n.cloakVisibilityAttr)},Es=/* */Object.freeze({cloak:ks,decloak:Os,open:ws,openWhileCloaked:function(t,n,e,o,r){ks(t,n,e),ws(t,n,e,o),r(),Os(t,n,e)},close:Ss,isOpen:Cs,isPartOf:function(n,e,t,o){return Cs(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()}}),Ts=/* */Object.freeze({events:function(e,o){return Xr([Kr(_n(),function(t,n){Ss(t,e,o)})])}}),Bs=[Bu("onOpen"),Bu("onClose"),rr("isPartOf"),rr("getAttachPoint"),vr("cloakVisibilityAttr","data-precloak-visibility")],As=ju({fields:Bs,name:"sandboxing",active:Ts,apis:Es,state:/* */Object.freeze({init:function(){var n=Tt(tt.none()),t=Z("not-implemented");return Oi({readState:t,isOpen:function(){return n.get().isSome()},clear:function(){n.set(tt.none())},set:function(t){n.set(tt.some(t))},get:function(t){return n.get()}})}})}),Ds=Z("dismiss.popups"),_s=Z("mouse.released"),Ms=Fo([vr("isExtraPart",Z(!1)),hr("fireEventInstead",[vr("event",Nn())])]),Fs=function(t){var n=Is(t);return qu.config(n)},Is=function(t){var e=qo("Dismissal",Ms,t);return{channels:Ct(Ds(),{schema:Fo([rr("target")]),onReceive:function(n,t){As.isOpen(n)&&(As.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return As.close(n)},function(t){return Ar(n,t.event)}))}})}},Vs=function(o,t){return kr(o,{},_(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,zo(n,n,oo(),Mo(function(t){return go("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([Or("dump",d)]))},Rs=function(t){return t.dump},Hs=function(t,n){return Je({},t.dump,zu(n))},Ns=Vs,Ps=Hs,zs="placeholder",Ls=gt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),js=function(t,n,e,o){return e.uiType===zs?(i=e,u=o,(r=t).exists(function(t){return t!==i.owner})?Ls.single(!0,Z(i)):St(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+J(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+Oo(i,null,2))},function(t){return t.replace()})):Ls.single(!1,Z(e));var r,i,u},Us=function(i,u,a,c){return js(i,0,a,c).fold(function(t,n){var e=n(u,a.config,a.validated),o=St(e,"components").getOr([]),r=L(o,function(t){return Us(i,u,t,c)});return[Je({},e,{components:r})]},function(t,n){var e=n(u,a.config,a.validated);return a.validated.preprocess.getOr(d)(e)})},Ws=function(n,e,t,o){var r,i,u,a=et(o,function(t,n){return o=t,r=!1,{name:Z(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(!0===r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),c=(r=n,i=e,u=a,L(t,function(t){return Us(r,i,t,u)}));return nt(a,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+Oo(e.components,null,2))}),c},Gs=Ls.single,Xs=Ls.multiple,Ys=Z(zs),qs=gt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ks=vr("factory",{sketch:d}),Js=vr("schema",[]),$s=rr("name"),Qs=zo("pname","pname",ro(function(t){return"<alloy."+qe(t.name)+">"}),$o()),Zs=Or("schema",function(){return[dr("preprocess")]}),tf=vr("defaults",Z({})),nf=vr("overrides",Z({})),ef=Io([Ks,Js,$s,Qs,tf,nf]),of=Io([Ks,Js,$s,tf,nf]),rf=Io([Ks,Js,$s,Qs,tf,nf]),uf=Io([Ks,Zs,$s,rr("unit"),Qs,tf,nf]),af=function(t){return t.fold(tt.some,tt.none,tt.some,tt.some)},cf=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},sf=function(e,o){return function(t){var n=qo("Converting part type",o,t);return e(n)}},ff=sf(qs.required,ef),lf=sf(qs.external,of),df=sf(qs.optional,rf),mf=sf(qs.group,uf),gf=Z("entirety"),pf=/* */Object.freeze({required:ff,external:lf,optional:df,group:mf,asNamedPart:af,name:cf,asCommon:function(t){return t.fold(d,d,d,d)},original:gf}),hf=function(t,n,e,o){return vt(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},vf=function(r,t){var n={};return M(t,function(t){af(t).each(function(e){var o=bf(r,e.pname);n[e.name]=function(t){var n=qo("Part: "+e.name+" in "+r,Io(e.schema),t);return Je({},o,{config:t,validated:n})}})}),n},bf=function(t,n){return{uiType:Ys(),owner:t,name:n}},yf=function(t,n,e){return{uiType:Ys(),owner:t,name:n,config:e,validated:{}}},xf=function(t){return L(t,function(t){return t.fold(tt.none,tt.some,tt.none,tt.none).map(function(t){return sr(t.name,t.schema.concat([Fu(gf())]))}).toArray()})},wf=function(t){return _(t,cf)},Sf=function(t,n,e){return o=n,i={},r={},M(e,function(t){t.fold(function(o){i[o.pname]=Gs(!0,function(t,n,e){return o.factory.sketch(hf(t,o,n,e))})},function(t){var n=o.parts[t.name];r[t.name]=Z(t.factory.sketch(hf(o,t,n[gf()]),n))},function(o){i[o.pname]=Gs(!1,function(t,n,e){return o.factory.sketch(hf(t,o,n,e))})},function(r){i[r.pname]=Xs(!0,function(n,t,e){var o=n[r.name];return _(o,function(t){return r.factory.sketch(vt(r.defaults(n,t,e),t,r.overrides(n,t)))})})})}),{internals:Z(i),externals:Z(r)};var o,i,r},Cf=function(t,n,e){return Ws(tt.some(t),n,n.components,e)},kf=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOption()},Of=function(t,n,e){return kf(t,n,e).getOrDie("Could not find part: "+e)},Ef=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return M(e,function(t){o[t]=i.getByUid(r[t])}),et(o,Z)},Tf=function(t,n){var e=t.getSystem();return et(n.partUids,function(t,n){return Z(e.getByUid(t))})},Bf=function(t){return J(t.partUids)},Af=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return M(e,function(t){o[t]=i.getByUid(r[t]).getOrDie()}),et(o,Z)},Df=function(n,t){var e=wf(t);return kt(_(e,function(t){return{key:t,value:n+"-"+t}}))},_f=function(n){return zo("partUids","partUids",io(function(t){return Df(t.uid,n)}),$o())},Mf=/* */Object.freeze({generate:vf,generateOne:yf,schemas:xf,names:wf,substitutes:Sf,components:Cf,defaultUids:Df,defaultUidsSchema:_f,getAllParts:Tf,getAllPartNames:Bf,getPart:kf,getPartOrDie:Of,getParts:Ef,getPartsOrDie:Af}),Ff=function(t,n,e,o,r){var i,u,a=(u=r,(0<(i=o).length?[sr("parts",i)]:[]).concat([rr("uid"),vr("dom",{}),vr("components",[]),Fu("originalSpec"),vr("debug.sketcher",{})]).concat(u));return qo(t+" [SpecSchema]",Fo(a.concat(n)),e)},If=function(t,n,e,o,r){var i=Vf(r),u=xf(e),a=_f(e),c=Ff(t,n,i,u,[a]),s=Sf(0,c,e);return o(c,Cf(t,c,s.internals()),i,s.externals())},Vf=function(t){return t.hasOwnProperty("uid")?t:Je({},t,{uid:pi("uid")})},Rf=Fo([rr("name"),rr("factory"),rr("configFields"),vr("apis",{}),vr("extraApis",{})]),Hf=Fo([rr("name"),rr("factory"),rr("configFields"),rr("partFields"),vr("apis",{}),vr("extraApis",{})]),Nf=function(t){var i=qo("Sketcher for "+t.name,Rf,t),n=et(i.apis,Ci),e=et(i.extraApis,function(t,n){return xi(t,n)});return Je({name:Z(i.name),partFields:Z([]),configFields:Z(i.configFields),sketch:function(t){return n=i.name,e=i.configFields,o=i.factory,r=Vf(t),o(Ff(n,e,r,[],[]),r);var n,e,o,r}},n,e)},Pf=function(t){var n=qo("Sketcher for "+t.name,Hf,t),e=vf(n.name,n.partFields),o=et(n.apis,Ci),r=et(n.extraApis,function(t,n){return xi(t,n)});return Je({name:Z(n.name),partFields:Z(n.partFields),configFields:Z(n.configFields),sketch:function(t){return If(n.name,n.configFields,n.partFields,n.factory,t)},parts:Z(e)},o,r)},zf=function(t){return"input"===ke(t)&&"radio"!==Me(t,"type")||"textarea"===ke(t)},Lf=/* */Object.freeze({getCurrent:function(t,n,e){return n.find(t)}}),jf=[rr("find")],Uf=ju({fields:jf,name:"composing",apis:Lf}),Wf=function(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r},Gf=function(t,n,e){return t<=n?n:e<=t?e:t},Xf=function(e,o,t,r){var n=_c(e.element(),"."+o.highlightClass);M(n,function(n){A(r,function(t){return t.element()===n})||(ji(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),Ar(t,Wn())}))})},Yf=function(t,n,e,o){Xf(t,n,0,[o]),qf(t,n,e,o)||(zi(o.element(),n.highlightClass),n.onHighlight(t,o),Ar(o,Un()))},qf=function(t,n,e,o){return Ui(o.element(),n.highlightClass)},Kf=function(t,n,e,o){var r=_c(t.element(),"."+n.itemClass);return tt.from(r[o]).fold(function(){return mt.error("No element found with index "+o)},t.getSystem().getByDom)},Jf=function(n,t,e){return vu(n.element(),"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},$f=function(n,t,e){var o=_c(n.element(),"."+t.itemClass);return(0<o.length?tt.some(o[o.length-1]):tt.none()).bind(function(t){return n.getSystem().getByDom(t).toOption()})},Qf=function(e,n,t,o){var r=_c(e.element(),"."+n.itemClass);return H(r,function(t){return Ui(t,n.highlightClass)}).bind(function(t){var n=Wf(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOption()})},Zf=function(n,t,e){var o=_c(n.element(),"."+t.itemClass);return Bt(_(o,function(t){return n.getSystem().getByDom(t).toOption()}))},tl=/* */Object.freeze({dehighlightAll:function(t,n,e){return Xf(t,n,0,[])},dehighlight:function(t,n,e,o){qf(t,n,e,o)&&(ji(o.element(),n.highlightClass),n.onDehighlight(t,o),Ar(o,Wn()))},highlight:Yf,highlightFirst:function(n,e,o){Jf(n,e,o).each(function(t){Yf(n,e,o,t)})},highlightLast:function(n,e,o){$f(n,e,o).each(function(t){Yf(n,e,o,t)})},highlightAt:function(n,e,o,t){Kf(n,e,o,t).fold(function(t){throw new Error(t)},function(t){Yf(n,e,o,t)})},highlightBy:function(n,e,o,t){var r=Zf(n,e,o);R(r,t).each(function(t){Yf(n,e,o,t)})},isHighlighted:qf,getHighlighted:function(n,t,e){return vu(n.element(),"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},getFirst:Jf,getLast:$f,getPrevious:function(t,n,e){return Qf(t,n,0,-1)},getNext:function(t,n,e){return Qf(t,n,0,1)},getCandidates:Zf}),nl=[rr("highlightClass"),rr("itemClass"),Bu("onHighlight"),Bu("onDehighlight")],el=ju({fields:nl,name:"highlighting",apis:tl}),ol=function(t,n,e){var o=W(t.slice(0,n)),r=W(t.slice(n+1));return R(o.concat(r),e)},rl=function(t,n,e){var o=W(t.slice(0,n));return R(o,e)},il=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return R(r.concat(o),e)},ul=function(t,n,e){var o=t.slice(n+1);return R(o,e)},al=function(e){return function(t){var n=t.raw();return B(e,n.which)}},cl=function(t){return function(n){return j(t,function(t){return t(n)})}},sl=function(t){return!0===t.raw().shiftKey},fl=function(t){return!0===t.raw().ctrlKey},ll=m(sl),dl=function(t,n){return{matches:t,classification:n}},ml=function(t){t.dom().focus()},gl=function(t){var n=t!==undefined?t.dom():h.document;return tt.from(n.activeElement).map(Xn.fromDom)},pl=function(n){return gl(se(n)).filter(function(t){return n.dom().contains(t.dom())})},hl=function(t,n,e){n.exists(function(n){return e.exists(function(t){return ce(t,n)})})||Dr(t,Pn(),{prevFocus:n,newFocus:e})},vl=function(){var r=function(t){return pl(t.element())};return{get:r,set:function(t,n){var e=r(t);t.getSystem().triggerFocus(n,t.element());var o=r(t);hl(t,e,o)}}},bl=function(){var r=function(t){return el.getHighlighted(t).map(function(t){return t.element()})};return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(Q,function(t){el.highlight(n,t)});var o=r(n);hl(n,e,o)}}};(ra=oa||(oa={})).OnFocusMode="onFocus",ra.OnEnterOrSpaceMode="onEnterOrSpace",ra.OnApiMode="onApi";var yl=function(t,n,e,o,a){var c=function(n,e,t,o,r){var i,u,a=t(n,e,o,r);return(i=a,u=e.event(),R(i,function(t){return t.matches(u)}).map(function(t){return t.classification})).bind(function(t){return t(n,e,o,r)})},r={schema:function(){return t.concat([vr("focusManager",vl()),br("focusInside","onFocus",Wo(function(t){return B(["onFocus","onEnterOrSpace","onApi"],t)?mt.value(t):mt.error("Invalid value for focusInside")})),Mu("handler",r),Mu("state",n),Mu("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==oa.OnFocusMode?tt.none():a(i).map(function(e){return Kr(Sn(),function(t,n){e(t,i,u),n.stop()})});return Xr(t.toArray().concat([Kr(zt(),function(o,r){c(o,r,e,i,u).fold(function(){var n,e,t;n=o,e=r,t=al([32].concat([13]))(e.event()),i.focusInside===oa.OnEnterOrSpaceMode&&t&&Er(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),Kr(Lt(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})]))}};return r},xl=function(t){var n=[dr("onEscape"),dr("onEnter"),vr("selector",'[data-alloy-tabstop="true"]'),vr("firstTabstop",0),vr("useTabstopAt",Z(!0)),dr("visibilitySelector")].concat([t]),u=function(t,n){var e=t.visibilitySelector.bind(function(t){return bu(n,t)}).getOr(n);return 0<sa(e)},e=function(n,e){var t,o,r,i;(t=n,o=e,r=_c(t.element(),o.selector),i=F(r,function(t){return u(o,t)}),tt.from(i[o.firstTabstop])).each(function(t){e.focusManager.set(n,t)})},a=function(n,t,e,o,r){return r(t,e,function(t){return u(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?tt.some(!0):tt.none()},function(t){return o.focusManager.set(n,t),tt.some(!0)})},i=function(n,t,e,o){var r,i,u=_c(n.element(),e.selector);return(r=n,i=e,i.focusManager.get(r).bind(function(t){return bu(t,i.selector)})).bind(function(t){return H(u,v(ce,t)).bind(function(t){return a(n,u,t,e,o)})})},o=Z([dl(cl([sl,al([9])]),function(t,n,e,o){var r=e.cyclic?ol:rl;return i(t,0,e,r)}),dl(al([9]),function(t,n,e,o){var r=e.cyclic?il:ul;return i(t,0,e,r)}),dl(al([27]),function(n,e,t,o){return t.onEscape.bind(function(t){return t(n,e)})}),dl(cl([ll,al([13])]),function(n,e,t,o){return t.onEnter.bind(function(t){return t(n,e)})})]),r=Z([]);return yl(n,ki.init,o,r,function(){return tt.some(e)})},wl=xl(Or("cyclic",Z(!1))),Sl=xl(Or("cyclic",Z(!0))),Cl=function(t,n,e){return zf(e)&&al([32])(n.event())?tt.none():(Mr(t,e,En()),tt.some(!0))},kl=function(t,n){return tt.some(!0)},Ol=[vr("execute",Cl),vr("useSpace",!1),vr("useEnter",!0),vr("useControlEnter",!1),vr("useDown",!1)],El=function(t,n,e){return e.execute(t,n,t.element())},Tl=yl(Ol,ki.init,function(t,n,e,o){var r=e.useSpace&&!zf(t.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[dl(al(a),El)].concat(e.useControlEnter?[dl(cl([fl,al([13])]),El)]:[])},function(t,n,e,o){return e.useSpace&&!zf(t.element())?[dl(al([32]),kl)]:[]},function(){return tt.none()}),Bl=function(t){var e=Tt(tt.none());return Oi({readState:function(){return e.get().map(function(t){return{numRows:t.numRows(),numColumns:t.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set(tt.some({numRows:Z(t),numColumns:Z(n)}))},getNumRows:function(){return e.get().map(function(t){return t.numRows()})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns()})}})},Al=/* */Object.freeze({flatgrid:Bl,init:function(t){return t.state(t)}}),Dl=function(i){return function(t,n,e,o){var r=i(t.element());return Il(r,t,n,e,o)}},_l=function(t,n){var e=mc(t,n);return Dl(e)},Ml=function(t,n){var e=mc(n,t);return Dl(e)},Fl=function(r){return function(t,n,e,o){return Il(r,t,n,e,o)}},Il=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element(),t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},Vl=Fl,Rl=Fl,Hl=Fl,Nl=function(t){var n,e=t.dom();return!((n=e).offsetWidth<=0&&n.offsetHeight<=0)},Pl=Jn(["index","candidates"],[]),zl=function(t,n,e){return Ll(t,n,e,Nl)},Ll=function(t,n,e,o){var r,i=v(ce,n),u=_c(t,e),a=F(u,Nl);return H(r=a,i).map(function(t){return Pl({index:t,candidates:r})})},jl=function(t,n){return H(t,function(t){return ce(n,t)})},Ul=function(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row()*o+t.column();return 0<=n&&n<e.length?tt.some(e[n]):tt.none()})},Wl=function(r,t,i,u,a){return Ul(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=Wf(n,a,0,e-1);return tt.some({row:Z(t),column:Z(o)})})},Gl=function(i,t,u,a,c){return Ul(i,t,a,function(t,n){var e=Wf(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Gf(n,0,o-1);return tt.some({row:Z(e),column:Z(r)})})},Xl=[rr("selector"),vr("execute",Cl),Au("onEscape"),vr("captureTab",!1),Iu()],Yl=function(n,e,t){vu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},ql=function(r){return function(t,n,e,o){return zl(t,n,e.selector).bind(function(t){return r(t.candidates(),t.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}},Kl=function(t,n,e,o){return e.captureTab?tt.some(!0):tt.none()},Jl=ql(function(t,n,e,o){return Wl(t,n,e,o,-1)}),$l=ql(function(t,n,e,o){return Wl(t,n,e,o,1)}),Ql=ql(function(t,n,e,o){return Gl(t,n,e,o,-1)}),Zl=ql(function(t,n,e,o){return Gl(t,n,e,o,1)}),td=Z([dl(al([37]),_l(Jl,$l)),dl(al([39]),Ml(Jl,$l)),dl(al([38]),Vl(Ql)),dl(al([40]),Rl(Zl)),dl(cl([sl,al([9])]),Kl),dl(cl([ll,al([9])]),Kl),dl(al([27]),function(t,n,e,o){return e.onEscape(t,n)}),dl(al([32].concat([13])),function(n,e,o,t){return(r=n,i=o,i.focusManager.get(r).bind(function(t){return bu(t,i.selector)})).bind(function(t){return o.execute(n,e,t)});var r,i})]),nd=Z([dl(al([32]),kl)]),ed=yl(Xl,Bl,td,nd,function(){return tt.some(Yl)}),od=function(t,n,e,i){var u=function(t,n,e){var o,r=Wf(n,i,0,e.length-1);return r===t?tt.none():(o=e[r],"button"===ke(o)&&"disabled"===Me(o,"disabled")?u(t,r,e):tt.from(e[r]))};return zl(t,e,n).bind(function(t){var n=t.index(),e=t.candidates();return u(n,n,e)})},rd=[rr("selector"),vr("getInitial",tt.none),vr("execute",Cl),Au("onEscape"),vr("executeOnMove",!1),vr("allowVertical",!0)],id=function(n,e,o){return(t=n,r=o,r.focusManager.get(t).bind(function(t){return bu(t,r.selector)})).bind(function(t){return o.execute(n,e,t)});var t,r},ud=function(n,e){e.getInitial(n).orThunk(function(){return vu(n.element(),e.selector)}).each(function(t){e.focusManager.set(n,t)})},ad=function(t,n,e){return od(t,e.selector,n,-1)},cd=function(t,n,e){return od(t,e.selector,n,1)},sd=function(o){return function(t,n,e){return o(t,n,e).bind(function(){return e.executeOnMove?id(t,n,e):tt.some(!0)})}},fd=function(t,n,e,o){return e.onEscape(t,n)},ld=Z([dl(al([32]),kl)]),dd=yl(rd,ki.init,function(t,n,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[dl(al(r),sd(_l(ad,cd))),dl(al(i),sd(Ml(ad,cd))),dl(al([13]),id),dl(al([32]),id),dl(al([27]),fd)]},ld,function(){return tt.some(ud)}),md=Jn(["rowIndex","columnIndex","cell"],[]),gd=function(t,n,e){return tt.from(t[n]).bind(function(t){return tt.from(t[e]).map(function(t){return md({rowIndex:n,columnIndex:e,cell:t})})})},pd=function(t,n,e,o){var r=t[n].length,i=Wf(e,o,0,r-1);return gd(t,n,i)},hd=function(t,n,e,o){var r=Wf(e,o,0,t.length-1),i=t[r].length,u=Gf(n,0,i-1);return gd(t,r,u)},vd=function(t,n,e,o){var r=t[n].length,i=Gf(e+o,0,r-1);return gd(t,n,i)},bd=function(t,n,e,o){var r=Gf(e+o,0,t.length-1),i=t[r].length,u=Gf(n,0,i-1);return gd(t,r,u)},yd=[sr("selectors",[rr("row"),rr("cell")]),vr("cycles",!0),vr("previousSelector",tt.none),vr("execute",Cl)],xd=function(n,e){e.previousSelector(n).orThunk(function(){var t=e.selectors;return vu(n.element(),t.cell)}).each(function(t){e.focusManager.set(n,t)})},wd=function(t,n){return function(e,o,i){var u=i.cycles?t:n;return bu(o,i.selectors.row).bind(function(t){var n=_c(t,i.selectors.cell);return jl(n,o).bind(function(o){var r=_c(e,i.selectors.row);return jl(r,t).bind(function(t){var n,e=(n=i,_(r,function(t){return _c(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell()})})})})}},Sd=wd(function(t,n,e){return pd(t,n,e,-1)},function(t,n,e){return vd(t,n,e,-1)}),Cd=wd(function(t,n,e){return pd(t,n,e,1)},function(t,n,e){return vd(t,n,e,1)}),kd=wd(function(t,n,e){return hd(t,e,n,-1)},function(t,n,e){return bd(t,e,n,-1)}),Od=wd(function(t,n,e){return hd(t,e,n,1)},function(t,n,e){return bd(t,e,n,1)}),Ed=Z([dl(al([37]),_l(Sd,Cd)),dl(al([39]),Ml(Sd,Cd)),dl(al([38]),Vl(kd)),dl(al([40]),Rl(Od)),dl(al([32].concat([13])),function(n,e,o){return pl(n.element()).bind(function(t){return o.execute(n,e,t)})})]),Td=Z([dl(al([32]),kl)]),Bd=yl(yd,ki.init,Ed,Td,function(){return tt.some(xd)}),Ad=[rr("selector"),vr("execute",Cl),vr("moveOnTab",!1)],Dd=function(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})},_d=function(n,e){vu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},Md=function(t,n,e){return od(t,e.selector,n,-1)},Fd=function(t,n,e){return od(t,e.selector,n,1)},Id=Z([dl(al([38]),Hl(Md)),dl(al([40]),Hl(Fd)),dl(cl([sl,al([9])]),function(t,n,e){return e.moveOnTab?Hl(Md)(t,n,e):tt.none()}),dl(cl([ll,al([9])]),function(t,n,e){return e.moveOnTab?Hl(Fd)(t,n,e):tt.none()}),dl(al([13]),Dd),dl(al([32]),Dd)]),Vd=Z([dl(al([32]),kl)]),Rd=yl(Ad,ki.init,Id,Vd,function(){return tt.some(_d)}),Hd=[Au("onSpace"),Au("onEnter"),Au("onShiftEnter"),Au("onLeft"),Au("onRight"),Au("onTab"),Au("onShiftTab"),Au("onUp"),Au("onDown"),Au("onEscape"),vr("stopSpaceKeyup",!1),dr("focusIn")],Nd=yl(Hd,ki.init,function(t,n,e){return[dl(al([32]),e.onSpace),dl(cl([ll,al([13])]),e.onEnter),dl(cl([sl,al([13])]),e.onShiftEnter),dl(cl([sl,al([9])]),e.onShiftTab),dl(cl([ll,al([9])]),e.onTab),dl(al([38]),e.onUp),dl(al([40]),e.onDown),dl(al([37]),e.onLeft),dl(al([39]),e.onRight),dl(al([32]),e.onSpace),dl(al([27]),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[dl(al([32]),kl)]:[]},function(t){return t.focusIn}),Pd=wl.schema(),zd=Sl.schema(),Ld=dd.schema(),jd=ed.schema(),Ud=Bd.schema(),Wd=Tl.schema(),Gd=Rd.schema(),Xd=Nd.schema(),Yd=Wu({branchKey:"mode",branches:/* */Object.freeze({acyclic:Pd,cyclic:zd,flow:Ld,flatgrid:jd,matrix:Ud,execution:Wd,menu:Gd,special:Xd}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element(),n.element())},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){Et(e,"setGridSize")?e.setGridSize(o,r):h.console.error("Layout does not support setGridSize")}},state:Al}),qd=function(t,n,e,o){var r=t.getSystem().build(o);gs(t,r,e)},Kd=function(t,n,e,o){var r=Jd(t,n);R(r,function(t){return ce(o.element(),t.element())}).each(hs)},Jd=function(t,n){return t.components()},$d=function(n,e,t,r,o){var i=Jd(n,e);return tt.from(i[r]).map(function(t){return Kd(n,e,0,t),o.each(function(t){qd(n,0,function(t,n){var e,o;o=n,ge(e=t,r).fold(function(){be(e,o)},function(t){pe(t,o)})},t)}),t})},Qd=ju({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(t,n,e,o){qd(t,0,be,o)},prepend:function(t,n,e,o){qd(t,0,ve,o)},remove:Kd,replaceAt:$d,replaceBy:function(n,e,t,o,r){var i=Jd(n,e);return H(i,o).bind(function(t){return $d(n,e,0,t,r)})},set:function(n,t,e,o){var r,i,u,a;vs(n),r=function(){var t=_(o,n.getSystem().build);M(t,function(t){ms(n,t)})},i=n.element(),u=se(i),a=gl(u).bind(function(n){var t=function(t){return ce(n,t)};return t(i)?tt.some(i):Wr(i,t)}),r(i),a.each(function(n){gl(u).filter(function(t){return ce(t,n)}).fold(function(){ml(n)},Q)})},contents:Jd})}),Zd=function(t,n,e){n.store.manager.onLoad(t,n,e)},tm=function(t,n,e){n.store.manager.onUnload(t,n,e)},nm=/* */Object.freeze({onLoad:Zd,onUnload:tm,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),em=/* */Object.freeze({events:function(e,o){var t=e.resetOnDom?[oi(function(t,n){Zd(t,e,o)}),ri(function(t,n){tm(t,e,o)})]:[Vu(e,o,Zd)];return Xr(t)}}),om=function(){var t=Tt(null);return Oi({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})},rm=function(){var i=Tt({}),u=Tt({});return Oi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return St(i.get(),t).orThunk(function(){return St(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};M(t,function(n){o[n.value]=n,St(n,"meta").each(function(t){St(t,"text").each(function(t){r[t]=n})})}),i.set(Je({},n,o)),u.set(Je({},e,r))},clear:function(){i.set({}),u.set({})}})},im=/* */Object.freeze({memory:om,dataset:rm,manual:function(){return Oi({readState:function(){}})},init:function(t){return t.store.manager.state(t)}}),um=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},am=[dr("initialValue"),rr("getFallbackEntry"),rr("getDataKey"),rr("setValue"),Mu("manager",{setValue:um,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(t){return t})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){um(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:rm})],cm=[rr("getValue"),vr("setValue",Q),dr("initialValue"),Mu("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:Q,state:ki.init})],sm=[dr("initialValue"),Mu("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:om})],fm=[br("store",{mode:"memory"},Jo("mode",{memory:sm,manual:cm,dataset:am})),Bu("onSetValue"),vr("resetOnDom",!1)],lm=ju({fields:fm,name:"representing",active:em,apis:nm,extra:{setValueFrom:function(t,n){var e=lm.getValue(n);lm.setValue(t,e)}},state:im}),dm=function(t,n){n.ignore||(ml(t.element()),n.onFocus(t))},mm=/* */Object.freeze({focus:dm,blur:function(t,n){n.ignore||t.element().dom().blur()},isFocused:function(t){return n=t.element(),e=se(n).dom(),n.dom()===e.activeElement;var n,e}}),gm=/* */Object.freeze({exhibit:function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return Ti(e)},events:function(e){return Xr([Kr(Sn(),function(t,n){dm(t,e),n.stop()})].concat(e.stopMousedown?[Kr(Ft(),function(t,n){n.event().prevent()})]:[]))}}),pm=[Bu("onFocus"),vr("stopMousedown",!1),vr("ignore",!1)],hm=ju({fields:pm,name:"focusing",active:gm,apis:mm}),vm=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},bm=function(n,t,e){t.toggleClass.each(function(t){e.get()?zi(n.element(),t):ji(n.element(),t)})},ym=function(t,n,e){Sm(t,n,e,!e.get())},xm=function(t,n,e){e.set(!0),bm(t,n,e),vm(t,n,e)},wm=function(t,n,e){e.set(!1),bm(t,n,e),vm(t,n,e)},Sm=function(t,n,e,o){(o?xm:wm)(t,n,e)},Cm=function(t,n,e){Sm(t,n,e,n.selected)},km=/* */Object.freeze({onLoad:Cm,toggle:ym,isOn:function(t,n,e){return e.get()},on:xm,off:wm,set:Sm}),Om=/* */Object.freeze({exhibit:function(t,n,e){return Ti({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=ym,ui(function(t){r(t,e,o)})),u=Vu(t,n,Cm);return Xr(z([t.toggleOnExecute?[i]:[],[u]]))}}),Em=/* */Object.freeze({init:function(t){var n=Tt(!1);return{readState:function(){return n.get()},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(!1)}}}}),Tm=function(t,n,e){De(t.element(),"aria-expanded",e)},Bm=[vr("selected",!1),dr("toggleClass"),vr("toggleOnExecute",!0),br("aria",{mode:"none"},Jo("mode",{pressed:[vr("syncWithExpanded",!1),Mu("update",function(t,n,e){De(t.element(),"aria-pressed",e),n.syncWithExpanded&&Tm(t,n,e)})],checked:[Mu("update",function(t,n,e){De(t.element(),"aria-checked",e)})],expanded:[Mu("update",Tm)],selected:[Mu("update",function(t,n,e){De(t.element(),"aria-selected",e)})],none:[Mu("update",Q)]}))],Am=ju({fields:Bm,name:"toggling",active:Om,apis:km,state:Em}),Dm="alloy.item-hover",_m="alloy.item-focus",Mm=function(t){(pl(t.element()).isNone()||hm.isFocused(t))&&(hm.isFocused(t)||hm.focus(t),Dr(t,Dm,{item:t}))},Fm=function(t){Dr(t,_m,{item:t})},Im=Z(Dm),Vm=Z(_m),Rm=function(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=Xr(n),ju({fields:[rr("enabled")],name:e,active:{events:Z(o)}})),configAsRaw:Z({}),initialConfig:{},state:ki}}},Hm=[rr("data"),rr("components"),rr("dom"),vr("hasSubmenu",!1),dr("toggling"),Ns("itemBehaviours",[Am,hm,Yd,lm]),vr("ignoreFocus",!1),vr("domModification",{}),Mu("builder",function(t){return{dom:t.dom,domModification:Je({},t.domModification,{attributes:Je({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes,{"aria-haspopup":t.hasSubmenu},t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Ps(t.itemBehaviours,[t.toggling.fold(Am.revoke,function(t){return Am.config(Je({aria:{mode:"checked"}},t))}),hm.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Fm(t)}}),Yd.config({mode:"execution"}),lm.config({store:{mode:"memory",initialValue:t.data}}),Rm("item-type-events",[Kr(An(),_r),ti(Ft()),Kr(Ht(),Mm),Kr(Tn(),hm.focus)])]),components:t.components,eventOrder:t.eventOrder}}),vr("eventOrder",{})],Nm=[rr("dom"),rr("components"),Mu("builder",function(t){return{dom:t.dom,components:t.components,events:Xr([ni(Tn())])}})],Pm=Z([ff({name:"widget",overrides:function(n){return{behaviours:zu([lm.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:function(){}}})])}}})]),zm=[rr("uid"),rr("data"),rr("components"),rr("dom"),vr("autofocus",!1),vr("ignoreFocus",!1),Ns("widgetBehaviours",[lm,hm,Yd]),vr("domModification",{}),_f(Pm()),Mu("builder",function(e){var t=Sf(0,e,Pm()),n=Cf("item-widget",e,t.internals()),o=function(t){return kf(t,e,"widget").map(function(t){return Yd.focusIn(t),t})},r=function(t,n){return zf(n.event().target())||e.autofocus&&n.setSource(t.element()),tt.none()};return{dom:e.dom,components:n,domModification:e.domModification,events:Xr([ui(function(t,n){o(t).each(function(t){n.stop()})}),Kr(Ht(),Mm),Kr(Tn(),function(t,n){e.autofocus?o(t):hm.focus(t)})]),behaviours:Ps(e.widgetBehaviours,[lm.config({store:{mode:"memory",initialValue:e.data}}),hm.config({ignore:e.ignoreFocus,onFocus:function(t){Fm(t)}}),Yd.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:Gu(),onLeft:r,onRight:r,onEscape:function(t,n){return hm.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element()),tt.none()):(hm.focus(t),tt.some(!0))}})])}})],Lm=Jo("type",{widget:zm,item:Hm,separator:Nm}),jm=Z([mf({factory:{sketch:function(t){var n=qo("menu.spec item",Lm,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return n.hasOwnProperty("uid")?n:Je({},n,{uid:pi("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Um=Z([rr("value"),rr("items"),rr("dom"),rr("components"),vr("eventOrder",{}),Vs("menuBehaviours",[el,lm,Uf,Yd]),br("movement",{mode:"menu",moveOnTab:!0},Jo("mode",{grid:[Iu(),Mu("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[Mu("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),rr("rowSelector")],menu:[vr("moveOnTab",!0),Mu("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),ir("markers",Cu()),vr("fakeFocus",!1),vr("focusManager",vl()),Bu("onHighlight")]),Wm=Z("alloy.menu-focus"),Gm=Pf({name:"Menu",configFields:Um(),partFields:jm(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:Hs(t.menuBehaviours,[el.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),lm.config({store:{mode:"memory",initialValue:t.value}}),Uf.config({find:tt.some}),Yd.config(t.movement.config(t,t.movement))]),events:Xr([Kr(Vm(),function(n,e){var t=e.event();n.getSystem().getByDom(t.target()).each(function(t){el.highlight(n,t),e.stop(),Dr(n,Wm(),{menu:n,item:t})})}),Kr(Im(),function(t,n){var e=n.event().item();el.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Xm=function(e,o,r,t){return St(r,t).bind(function(t){return St(e,t).bind(function(t){var n=Xm(e,o,r,t);return tt.some([t].concat(n))})}).getOr([])},Ym=function(t,n){var e={};nt(t,function(t,n){M(t,function(t){e[t]=n})});var o=n,r=ot(n,function(t,n){return{k:t,v:n}}),i=et(r,function(t,n){return[n].concat(Xm(e,o,r,n))});return et(e,function(t){return St(i,t).getOr([t])})},qm=function(){var i=Tt({}),u=Tt({}),a=Tt({}),c=Tt(tt.none()),s=Tt({}),n=function(t){return St(u.get(),t)};return{setMenuBuilt:function(t,n){var e;u.set(Je({},u.get(),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){c.set(tt.some(t)),i.set(e),u.set(n),s.set(o);var r=Ym(o,e);a.set(r)},expand:function(e){return St(i.get(),e).map(function(t){var n=St(a.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return St(a.get(),t)},collapse:function(t){return St(a.get(),t).bind(function(t){return 1<t.length?tt.some(t.slice(1)):tt.none()})},lookupMenu:n,otherMenus:function(t){var n=s.get();return G(J(n),t)},getPrimary:function(){return c.get().bind(function(t){return n(t).bind(function(t){return"prepared"===t.type?tt.some(t.menu):tt.none()})})},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(tt.none())},isClear:function(){return c.get().isNone()}}},Km=Z("collapse-item"),Jm=Nf({name:"TieredMenu",configFields:[_u("onExecute"),_u("onEscape"),Du("onOpenMenu"),Du("onOpenSubmenu"),Bu("onCollapseMenu"),vr("highlightImmediately",!0),sr("data",[rr("primary"),rr("menus"),rr("expansions")]),vr("fakeFocus",!1),Bu("onHighlight"),Bu("onHover"),Ou(),rr("dom"),vr("navigateOnHover",!0),vr("stayInDom",!1),Vs("tmenuBehaviours",[Yd,el,Uf,Qd]),vr("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)}},factory:function(a,t){var c,n,i=Tt(tt.none()),s=qm(),e=function(t){var o,r,n,e=(o=t,r=a.data.primary,n=a.data.menus,et(n,function(t,n){var e=function(){return Gm.sketch(Je({dom:t.dom},t,{value:n,items:t.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?bl():vl()}))};return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),i=u(t);return s.setContents(a.data.primary,e,a.data.expansions,i),s.getPrimary()},f=function(t){return lm.getValue(t).value},u=function(t){return et(a.data.menus,function(t,n){return L(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})})},l=function(n,t){el.highlight(n,t),el.getHighlighted(t).orThunk(function(){return el.getFirst(t)}).each(function(t){Mr(n,t.element(),Tn())})},d=function(n,t){return Bt(_(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?tt.some(t.menu):tt.none()})}))},m=function(n,t,e){var o=d(t,t.otherMenus(e));M(o,function(t){Gi(t.element(),[a.markers.backgroundMenu]),a.stayInDom||Qd.remove(n,t)})},g=function(t,o){var r,n=(r=t,i.get().getOrThunk(function(){var e={},t=_c(r.element(),"."+a.markers.item),n=F(t,function(t){return"true"===Me(t,"aria-haspopup")});return M(n,function(t){r.getSystem().getByDom(t).each(function(t){var n=f(t);e[n]=t})}),i.set(tt.some(e)),e}));nt(n,function(t,n){var e=B(o,n);De(t.element(),"aria-expanded",e)})},p=function(o,r,i){return tt.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return tt.none();var n=t.menu,e=d(r,i.slice(1));return M(e,function(t){zi(t.element(),a.markers.backgroundMenu)}),Pr(n.element())||Qd.append(o,pu(n)),Gi(n.element(),[a.markers.backgroundMenu]),l(o,n),m(o,r,i),tt.some(n)})})};(n=c||(c={}))[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var h=function(r,i,u){void 0===u&&(u=c.HighlightSubmenu);var t=f(i);return s.expand(t).bind(function(o){return g(r,o),tt.from(o[0]).bind(function(e){return s.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return s.setMenuBuilt(n,o),o}(r,e,t);return Pr(n.element())||Qd.append(r,pu(n)),a.onOpenSubmenu(r,i,n),u===c.HighlightSubmenu?(el.highlightFirst(n),p(r,s,o)):(el.dehighlightAll(n),tt.some(i))})})})},o=function(n,e){var t=f(e);return s.collapse(t).bind(function(t){return g(n,t),p(n,s,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})},r=function(e){return function(n,t){return bu(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOption().bind(function(t){return e(n,t).map(function(){return!0})})})}},v=Xr([Kr(Wm(),function(n,t){var e=t.event().menu();el.highlight(n,e);var o=f(t.event().item());s.refresh(o).each(function(t){return m(n,s,t)})}),ui(function(n,t){var e=t.event().target();n.getSystem().getByDom(e).each(function(t){0===f(t).indexOf("collapse-item")&&o(n,t),h(n,t,c.HighlightSubmenu).fold(function(){a.onExecute(n,t)},function(){})})}),oi(function(n,t){e(n).each(function(t){Qd.append(n,pu(t)),a.onOpenMenu(n,t),a.highlightImmediately&&l(n,t)})})].concat(a.navigateOnHover?[Kr(Im(),function(t,n){var e,o,r=n.event().item();e=t,o=f(r),s.refresh(o).bind(function(t){return g(e,t),p(e,s,t)}),h(t,r,c.HighlightParent),a.onHover(t,r)})]:[])),b={collapseMenu:function(n){el.getHighlighted(n).each(function(t){el.getHighlighted(t).each(function(t){o(n,t)})})},highlightPrimary:function(n){s.getPrimary().each(function(t){l(n,t)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Hs(a.tmenuBehaviours,[Yd.config({mode:"special",onRight:r(function(t,n){return zf(n.element())?tt.none():h(t,n,c.HighlightSubmenu)}),onLeft:r(function(t,n){return zf(n.element())?tt.none():o(t,n)}),onEscape:r(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){s.getPrimary().each(function(t){Mr(n,t.element(),Tn())})}}),el.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Uf.config({find:function(t){return el.getHighlighted(t)}}),Qd.config({})]),eventOrder:a.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:Ct(t,n),expansions:{}}},collapseItem:function(t){return{value:qe(Km()),meta:{text:t}}}}}),$m=Nf({name:"InlineView",configFields:[rr("lazySink"),Bu("onShow"),Bu("onHide"),pr("onEscape"),Vs("inlineBehaviours",[As,qu]),hr("fireDismissalEventInstead",[vr("event",Nn())]),vr("getRelated",tt.none),vr("eventOrder",tt.none)],factory:function(s,t){var r=function(t,n,e,o){var r=s.lazySink(t).getOrDie();As.openWhileCloaked(t,e,function(){return fs.positionWithin(r,n,t,o)}),s.onShow(t)},n={setContent:function(t,n){As.open(t,n)},showAt:function(t,n,e){var o=tt.none();r(t,n,e,o)},showWithin:r,showMenuAt:function(t,n,e){var o,r,i,u,a,c=(o=s,r=t,i=n,u=e,a=function(){return o.lazySink(r)},Jm.sketch({dom:{tag:"div"},data:u.data,markers:u.menu.markers,onEscape:function(){return As.close(r),o.onEscape.map(function(t){return t(r)}),tt.some(!0)},onExecute:function(){return tt.some(!0)},onOpenMenu:function(t,n){fs.position(a().getOrDie(),i,n)},onOpenSubmenu:function(t,n,e){var o=a().getOrDie();fs.position(o,{anchor:"submenu",item:n},e)}}));As.open(t,c),s.onShow(t)},hide:function(t){As.close(t),s.onHide(t)},getContent:function(t){return As.getState(t)},isOpen:As.isOpen};return{uid:s.uid,dom:s.dom,behaviours:Hs(s.inlineBehaviours,[As.config({isPartOf:function(t,n,e){return xu(n,e)||(o=t,r=e,s.getRelated(o).exists(function(t){return xu(t,r)}));var o,r},getAttachPoint:function(t){return s.lazySink(t).getOrDie()}}),Fs(Je({isExtraPart:Z(!1)},s.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))]),eventOrder:s.eventOrder,apis:n}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)}}}),Qm=function(t){var n=function(t,n){n.stop(),_r(t)},e=xn.detect().deviceType.isTouch()?[Kr(Bn(),n)]:[Kr(Wt(),n),Kr(Ft(),function(t,n){n.cut()})];return Xr(z([t.map(function(e){return Kr(En(),function(t,n){e(t),n.stop()})}).toArray(),e]))},Zm=Nf({name:"Button",factory:function(t){var n=Qm(t.action),e=t.dom.tag,o=function(n){return St(t.dom,"attributes").bind(function(t){return St(t,n)})};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Ps(t.buttonBehaviours,[hm.config({}),Yd.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==e)return{role:o("role").getOr("button")};var t=o("type").getOr("button"),n=o("role").map(function(t){return{role:t}}).getOr({});return Je({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[vr("uid",undefined),rr("dom"),vr("components",[]),Ns("buttonBehaviours",[hm,Yd]),dr("action"),dr("role"),vr("eventOrder",{})]}),tg=function(t){var n=function e(t){return t.uid!==undefined}(t)&&Et(t,"uid")?t.uid:pi("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).fold(tt.none,tt.some)},asSpec:function(){return Je({},t,{uid:n})}}},ng=function(t){return tt.from(t()["temporary-placeholder"]).getOr("!not found!")},eg=function(t,n){return tt.from(n()[t]).getOrThunk(function(){return ng(n)})},og={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},rg=Nf({name:"Notification",factory:function(n){var t,e,o=tg({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:zu([Qd.config({})])}),r=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},i=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},u=tg({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(0)]},i(0)],behaviours:zu([Qd.config({})])}),a={updateProgress:function(t,n){t.getSystem().isConnected()&&u.getOpt(t).each(function(t){Qd.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(n)]},i(n)])})},updateText:function(t,n){if(t.getSystem().isConnected()){var e=o.get(t);Qd.set(e,[lu(n)])}}},c=z([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return tt.from(og[t])}).toArray()]);return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:(t=c,e=n.iconProvider,At(t,function(t){return tt.from(e()[t])}).getOrThunk(function(){return ng(e)}))}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:zu([Qd.config({})])}].concat(n.progress?[u.asSpec()]:[]).concat(Zm.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:eg("close",n.iconProvider),attributes:{"aria-label":n.translationProvider("Close")}}}],action:function(t){n.onAction(t)}})),apis:a}},configFields:[dr("level"),rr("progress"),rr("icon"),rr("onAction"),rr("text"),rr("iconProvider"),rr("translationProvider")],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function ig(r,i,u){var a=i.backstage;return{open:function(t,n){var e=function(){n(),$m.hide(r)},o=gu(rg.sketch({text:t.text,level:B(["success","error","warning","info"],t.type)?t.type:undefined,progress:!0===t.progressBar,icon:tt.from(t.icon),onAction:e,iconProvider:a.shared.providers.icons,translationProvider:a.shared.providers.translate})),r=gu($m.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:i.backstage.shared.getSink,fireDismissalEventInstead:{}}));return u.add(r),t.timeout&&setTimeout(function(){e()},t.timeout),{close:e,moveTo:function(t,n){$m.showAt(r,{anchor:"makeshift",x:t,y:n},pu(o))},moveRel:function(t,n){$m.showAt(r,i.backstage.shared.anchors.banner(),pu(o))},text:function(t){rg.updateText(o,t)},settings:t,getEl:function(){},progressBar:{value:function(t){rg.updateProgress(o,t)}}}},close:function(t){t.close()},reposition:function(t){M(t,function(t){t.moveTo(0,0)}),function(e){if(0<e.length){var t=e.slice(0,1)[0],n=(o=r).inline?o.getElement():o.getContentAreaContainer();t.moveRel(n,"tc-tc"),M(e,function(t,n){0<n&&t.moveRel(e[n-1].getEl(),"bc-tc")})}var o}(t)},getArgs:function(t){return t.settings}}}var ug,ag,cg=function(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null!==r&&clearTimeout(r),r=setTimeout(function(){e.apply(null,t),r=null},o)}}},sg=/[\u00a0 \t\r\n]/,fg=function(e,t,n,o,r){return void 0===r&&(r=0),(i=e).collapsed&&3===i.startContainer.nodeType?function(t,n,e,o){var r;for(r=n-1;0<=r;r--){if(sg.test(t.charAt(r)))return tt.none();if(t.charAt(r)===e)break}return-1===r||n-r<o?tt.none():tt.some(t.substring(r+1,n))}(n,o,t,r).map(function(t){var n=e.cloneRange();return n.setStart(e.startContainer,e.startOffset-t.length-1),n.setEnd(e.startContainer,e.startOffset),{text:t,rng:n}}):tt.none();var i},lg=function(e,t){t.on("keypress",e.onKeypress.throttle),t.on("remove",e.onKeypress.cancel);var o=function(t,n){Dr(t,zt(),{raw:n})};t.on("keydown",function(n){var t=function(){return e.getView().bind(el.getHighlighted)};8===n.which&&e.onKeypress.throttle(n),e.isActive()&&(27===n.which?e.closeIfNecessary():32===n.which?e.closeIfNecessary():13===n.which?(t().each(_r),n.preventDefault()):40===n.which?(t().fold(function(){e.getView().each(el.highlightFirst)},function(t){o(t,n)}),n.preventDefault()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){o(t,n),n.preventDefault()}))})},dg=tinymce.util.Tools.resolve("tinymce.util.Promise"),mg=function(t,n){return 0===t.startOffset||/\s/.test(n.charAt(t.startOffset-1))},gg=function(t,n){var e,o,r,i=n(),u=t.selection.getRng(),a=u.startContainer.nodeValue;return(e=u,o=a,r=i,At(r.triggerChars,function(n){return fg(e,n,o,e.startOffset).map(function(t){return{range:t.rng,text:t.text,triggerChar:n}})})).map(function(e){var t=F(i.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOr(mg)(e.range,a,e.text)});return{lookupData:dg.all(_(t,function(n){return n.fetch(e.text,n.maxResults).then(function(t){return{items:t,columns:n.columns,onAction:n.onAction}})})),triggerChar:e.triggerChar,range:e.range}})},pg=Io([Or("type",function(){return"autocompleteitem"}),Or("active",function(){return!1}),Or("disabled",function(){return!1}),vr("meta",{}),ur("value"),gr("text"),gr("icon")]),hg=Io([ur("type"),ur("ch"),yr("minChars",1),vr("columns",1),yr("maxResults",10),pr("matches"),cr("fetch"),cr("onAction")]),vg=function(t){var n,e,o=t.ui.registry.getAll().popups,r=et(o,function(t){return(n=t,Xo("Autocompleter",hg,n)).fold(function(t){throw new Error(Ko(t))},function(t){return t});var n}),i=(n=rt(r,function(t){return t.ch}),e={},M(n,function(t){e[t]={}}),J(e)),u=it(r);return{dataset:r,triggerChars:i,lookupByChar:function(n){return F(u,function(t){return t.ch===n})}}},bg=[Sr("disabled",!1),gr("text"),gr("shortcut"),zo("value","value",ro(function(){return qe("menuitem-value")}),$o()),vr("meta",{})],yg=Io([ur("type"),Cr("onSetup",function(){return Q}),Cr("onAction",Q),gr("icon")].concat(bg)),xg=Io([ur("type"),cr("getSubmenuItems"),Cr("onSetup",function(){return Q}),gr("icon")].concat(bg)),wg=Io([ur("type"),Sr("active",!1),Cr("onSetup",function(){return Q}),cr("onAction")].concat(bg)),Sg=Io([ur("type"),Sr("active",!1),gr("icon")].concat(bg)),Cg=Io([ur("type"),gr("text")]),kg=Io([ur("type"),ar("fancytype",["inserttable"]),Cr("onAction",Q)]),Og=function(t,o,n){var r=_c(t.element(),"."+n);if(0<r.length){var e=H(r,function(t){var n=t.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return tt.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return tt.none()},Eg=function(t,n){return zu([Rm(t,n)])},Tg=function(t){return Eg(qe("unnamed-events"),t)},Bg=[rr("lazySink"),rr("tooltipDom"),vr("exclusive",!0),vr("tooltipComponents",[]),vr("delay",300),wr("mode","normal",["normal","follow-highlight"]),vr("anchor",function(t){return{anchor:"hotspot",hotspot:t,layouts:{onLtr:Z([ac,uc,ec,rc,oc,ic]),onRtl:Z([ac,uc,ec,rc,oc,ic])}}}),Bu("onHide"),Bu("onShow")],Ag=/* */Object.freeze({init:function(){var e=Tt(tt.none()),n=Tt(tt.none()),o=function(){e.get().each(function(t){h.clearTimeout(t)})},t=Z("not-implemented");return Oi({getTooltip:function(){return n.get()},isShowing:function(){return n.get().isSome()},setTooltip:function(t){n.set(tt.some(t))},clearTooltip:function(){n.set(tt.none())},clearTimer:o,resetTimer:function(t,n){o(),e.set(tt.some(h.setTimeout(function(){t()},n)))},readState:t})}}),Dg=qe("tooltip.exclusive"),_g=qe("tooltip.show"),Mg=qe("tooltip.hide"),Fg=function(t,n,e){t.getSystem().broadcastOn([Dg],{})},Ig=/* */Object.freeze({hideAllExclusive:Fg,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&Qd.set(t,o)})}}),Vg=ju({fields:Bg,name:"tooltipping",active:/* */Object.freeze({events:function(o,r){var e=function(n){r.getTooltip().each(function(t){hs(t),o.onHide(n,t),r.clearTooltip()}),r.clearTimer()};return Xr(z([[Kr(_g,function(t){r.resetTimer(function(){!function(n){if(!r.isShowing()){Fg(n);var t=o.lazySink(n).getOrDie(),e=n.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:Xr("normal"===o.mode?[Kr(Ht(),function(t){Ar(n,_g)}),Kr(Vt(),function(t){Ar(n,Mg)})]:[]),behaviours:zu([Qd.config({})])});r.setTooltip(e),ms(t,e),o.onShow(n,e),fs.position(t,o.anchor(n),e)}}(t)},o.delay)}),Kr(Mg,function(t){r.resetTimer(function(){e(t)},o.delay)}),Kr(On(),function(t,n){B(n.channels(),Dg)&&e(t)}),ri(function(t){e(t)})],"normal"===o.mode?[Kr(Nt(),function(t){Ar(t,_g)}),Kr(Cn(),function(t){Ar(t,Mg)}),Kr(Ht(),function(t){Ar(t,_g)}),Kr(Vt(),function(t){Ar(t,Mg)})]:[Kr(Un(),function(t,n){Ar(t,_g)}),Kr(Wn(),function(t){Ar(t,Mg)})]]))}}),state:Ag,apis:Ig}),Rg=function(t){var n,e,o,r=Xn.fromHtml(t),i=me(r),u=(e=(n=r).dom().attributes!==undefined?n.dom().attributes:[],V(e,function(t,n){var e;return"class"===n.name?t:Je({},t,((e={})[n.name]=n.value,e))},{})),a=(o=r,Array.prototype.slice.call(o.dom().classList,0)),c=0===i.length?{}:{innerHtml:Se(r)};return Je({tag:ke(r),classes:a,attributes:u},c)},Hg=tinymce.util.Tools.resolve("tinymce.util.I18n"),Ng="tox-menu-nav__js",Pg="tox-collection__item",zg="tox-swatch",Lg={normal:Ng,color:zg},jg="tox-collection__item--enabled",Ug="tox-collection__item-icon",Wg="tox-collection__item-label",Gg="tox-collection__item--active",Xg=function(t){return St(Lg,t).getOr(Ng)},Yg=tinymce.util.Tools.resolve("tinymce.Env"),qg=function(t){var e=Yg.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},n=t.split("+"),o=_(n,function(t){var n=t.toLowerCase().trim();return at(e,n)?e[n]:t});return Yg.mac?o.join(""):o.join("+")},Kg=function(t){return{dom:{tag:"span",classes:[Ug],innerHtml:t}}},Jg=function(t){return{dom:{tag:"span",classes:[Wg]},components:[lu(Hg.translate(t))]}},$g=function(t,n){return{dom:{tag:"span",classes:[Wg]},components:[{dom:{tag:t.tag,attributes:{style:t.styleAttr}},components:[lu(Hg.translate(n))]}]}},Qg=function(t){return{dom:{tag:"span",classes:["tox-collection__item-accessory"],innerHtml:qg(t)}}},Zg=function(t){return{dom:{tag:"span",classes:[Ug,"tox-collection__item-checkmark"],innerHtml:eg("checkmark",t)}}},tp=function(t,r,n,i){void 0===i&&(i=tt.none());var e,o,u,a,c,s,f,l,d,m,g=t.iconContent.map(function(t){return n=t,e=r.icons,o=i,tt.from(e()[n]).or(o).getOrThunk(function(){return ng(e)});var n,e,o}),p=tt.from(t.meta).fold(function(){return Jg},function(t){return at(t,"style")?v($g,t.style):Jg});return"color"===t.presets?(s=t.ariaLabel,f=t.value,{dom:(l=zg,d=g.getOr(""),m=s.map(function(t){return' title="'+t+'"'}).getOr(""),Rg("custom"===f?'<button class="'+l+' tox-swatches__picker-btn"'+m+">"+d+"</button>":"remove"===f?'<div class="'+l+' tox-swatch--remove"'+m+">"+d+"</div>":'<div class="'+l+'" style="background-color: '+f+'" data-mce-color="'+f+'"'+m+"></div>")),optComponents:[]}):(e=t,o=g,u=p,a=n?e.checkMark.orThunk(function(){return o.or(tt.some("")).map(Kg)}):tt.none(),c=e.ariaLabel.map(function(t){return{attributes:{title:Hg.translate(t)}}}).getOr({}),{dom:bt({tag:"div",classes:[Ng,Pg]},c),optComponents:[a,e.textContent.map(u),e.shortcutContent.map(Qg),e.caret]})},np=["input","button","textarea"],ep=function(t,n,e){n.disabled&&cp(t,n,e)},op=function(t){return B(np,ke(t.element()))},rp=function(t){De(t.element(),"disabled","disabled")},ip=function(t){Ie(t.element(),"disabled")},up=function(t){De(t.element(),"aria-disabled","true")},ap=function(t){De(t.element(),"aria-disabled","false")},cp=function(n,t,e){t.disableClass.each(function(t){zi(n.element(),t)}),(op(n)?rp:up)(n)},sp=function(t){return op(t)?Fe(t.element(),"disabled"):"true"===Me(t.element(),"aria-disabled")},fp=/* */Object.freeze({enable:function(n,t,e){t.disableClass.each(function(t){ji(n.element(),t)}),(op(n)?ip:ap)(n)},disable:cp,isDisabled:sp,onLoad:ep}),lp=/* */Object.freeze({exhibit:function(t,n,e){return Ti({classes:n.disabled?n.disableClass.map(X).getOr([]):[]})},events:function(t,n){return Xr([Yr(En(),function(t,n){return sp(t)}),Vu(t,n,ep)])}}),dp=[vr("disabled",!1),dr("disableClass")],mp=ju({fields:dp,name:"disabling",active:lp,apis:fp}),gp=function(t){return mp.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},pp=function(t){return mp.config({disabled:t})},hp=function(t){return mp.config({disabled:t,disableClass:"tox-tbtn--disabled"})},vp=function(t,n){var e=t.getApi(n);return function(t){t(e)}},bp=function(e,o){return oi(function(t){vp(e,t)(function(t){var n=e.onSetup(t);null!==n&&n!==undefined&&o.set(n)})})},yp=function(n,e){return ri(function(t){return vp(n,t)(e.get())})};(ag=ug||(ug={}))[ag.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",ag[ag.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var xp,wp,Sp=ug,Cp={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},kp=function(t){return L(t,function(t){return t.toArray()})},Op=function(t,n,e){var o,r,i=Tt(Q);return{type:"item",dom:n.dom,components:kp(n.optComponents),data:t.data,eventOrder:Cp,hasSubmenu:t.triggersSubmenu,itemBehaviours:zu([Rm("item-events",[(o=t,r=e,ui(function(t,n){vp(o,t)(o.onAction),o.triggersSubmenu||r!==Sp.CLOSE_ON_EXECUTE||(Ar(t,_n()),n.stop())})),bp(t,i),yp(t,i)]),gp(t.disabled),Qd.config({})].concat(t.itemBehaviours))}},Ep=function(t){return{value:t.value,meta:bt({text:t.text.getOr("")},t.meta)}},Tp=Z(vf("item-widget",Pm())),Bp=qe("cell-over"),Ap=qe("cell-execute"),Dp=function(n,e,t){var o,r=function(t){return Dr(t,Ap,{row:n,col:e})};return gu({dom:{tag:"div",attributes:(o={role:"button"},o["aria-labelledby"]=t,o)},behaviours:zu([Rm("insert-table-picker-cell",[Kr(Ht(),hm.focus),Kr(En(),r),Kr(An(),r)]),Am.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),hm.config({onFocus:function(t){return Dr(t,Bp,{row:n,col:e})}})])})},_p={inserttable:function NA(o){var t,n=qe("size-label"),a=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(Dp(r,u,t));o.push(i)}return o}(n,10,10),c=tg({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[lu("0x0")],behaviours:zu([Qd.config({})])});return{type:"widget",data:{value:qe("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Tp().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:(t=a,L(t,function(t){return _(t,pu)})).concat(c.asSpec()),behaviours:zu([Rm("insert-table-picker",[Zr(Bp,function(t,n,e){var o,r,i=e.event().row(),u=e.event().col();!function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Am.set(t[i][u],i<=n&&u<=e)}(a,i,u,10,10),Qd.set(c.get(t),[(o=i,r=u,lu(r+1+"x"+(o+1)))])}),Zr(Ap,function(t,n,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),Ar(t,_n())})]),Yd.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}}},Mp=function(n,t,e,o,r,i,u){var a=tp({presets:e,textContent:t?n.text:tt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:tt.none(),checkMark:t?tt.some(Zg(u.icons)):tt.none(),caret:tt.none(),value:n.value},u,!0);return vt(Op({data:Ep(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){Am.set(n,t)},isActive:function(){return Am.isOn(n)},isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:jg,toggleOnExecute:!1,selected:n.active}})},Fp=function(n,t,e,o,r,i,u){void 0===u&&(u=!0);var a,c,s=tp({presets:e,textContent:t?n.text:tt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:tt.none(),checkMark:tt.none(),caret:tt.none(),value:n.value},i.providers,u,n.icon);return Op({data:Ep(n),disabled:n.disabled,getApi:function(){return{}},onAction:function(t){return o(n.value,n.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:(a=n.meta,c=i,ut(a,"tooltipWorker").map(function(e){return[Vg.config({lazySink:c.getSink,tooltipDom:{tag:"div"},tooltipComponents:[],anchor:function(t){return{anchor:"submenu",item:t}},mode:"follow-highlight",onShow:function(n,t){e(function(t){Vg.setComponents(n,[du({element:Xn.fromDom(t)})])})}})]}).getOr([]))},s,r)},Ip=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:Je({tag:"div",classes:[Pg,"tox-collection__group-heading"]},n),components:[]}},Vp=function(t,n,e,o){void 0===o&&(o=!0);var r=tp({presets:"normal",iconContent:t.icon,textContent:t.text,ariaLabel:t.text,caret:tt.none(),checkMark:tt.none(),shortcutContent:t.shortcut},e,o);return Op({data:Ep(t),getApi:function(n){return{isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n)},Rp=function(t,n,e,o){void 0===o&&(o=!0);var r,i=(r=e.icons,{dom:{tag:"span",classes:["tox-collection__item-caret"],innerHtml:eg("chevron-right",r)}}),u=tp({presets:"normal",iconContent:t.icon,textContent:t.text,ariaLabel:t.text,caret:tt.some(i),checkMark:tt.none(),shortcutContent:t.shortcut},e,o);return Op({data:Ep(t),getApi:function(n){return{isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},disabled:t.disabled,onAction:Q,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,n)},Hp=function(t,n,e){var o=tp({iconContent:tt.none(),textContent:t.text,ariaLabel:t.text,checkMark:tt.some(Zg(e.icons)),caret:tt.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,!0);return vt(Op({data:Ep(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){Am.set(n,t)},isActive:function(){return Am.isOn(n)},isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,n),{toggling:{toggleClass:jg,toggleOnExecute:!1,selected:t.active}})},Np=function(n){return(t=_p,e=n.fancytype,Object.prototype.hasOwnProperty.call(t,e)?tt.some(t[e]):tt.none()).map(function(t){return t(n)});var t,e},Pp=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:(n=t,"color"===n?"tox-swatches":"tox-menu"),tieredMenu:"tox-tiered-menu"};var n},zp=function(t){var n=Pp(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:Xg(t)}},Lp=[Gm.parts().items({})],jp=function(t,n,e){var o=Pp(e);return{dom:{tag:"div",classes:z([[o.tieredMenu]])},markers:zp(e)}},Up=function(t,n){var e=zp(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}},Wp=function(e,o){return function(t){var n=D(t,o);return _(n,function(t){return{dom:e,components:t}})}},Gp=function(n,i,t){return void 0===t&&(t=!0),{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[Gm.parts().items({preprocess:function(t){return"auto"!==n&&1<n?Wp({tag:"div",classes:["tox-collection__group"]},n)(t):(e=function(t,n){return"separator"===i[n].type},o=[],r=[],M(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],at(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),_(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}}));var e,o,r}})]}};(wp=xp||(xp={}))[wp.ContentFocus=0]="ContentFocus",wp[wp.UiFocus=1]="UiFocus";var Xp=function(t){return console.error(Ko(t)),console.log(t),tt.none()},Yp=function(t){return t.icon!==undefined||"togglemenuitem"===t.type||"choicemenuitem"===t.type},qp=function(t){return A(t,Yp)},Kp=function(t,n,e,o){switch(void 0===o&&(o=!0),t.type){case"menuitem":return(c=t,Xo("menuitem",yg,c)).fold(Xp,function(t){return tt.some(Vp(t,n,e,o))});case"nestedmenuitem":return(a=t,Xo("nestedmenuitem",xg,a)).fold(Xp,function(t){return tt.some(Rp(t,n,e,o))});case"togglemenuitem":return(u=t,Xo("togglemenuitem",wg,u)).fold(Xp,function(t){return tt.some(Hp(t,n,e))});case"separator":return(i=t,Xo("separatormenuitem",Cg,i)).fold(Xp,function(t){return tt.some(Ip(t))});case"fancymenuitem":return(r=t,Xo("fancymenuitem",kg,r)).fold(Xp,function(t){return Np(t)});default:return console.error("Unknown item in general menu",t),tt.none()}var r,i,u,a,c},Jp=function(t,n,e,o,r){var i,u,a,c,s,f,l;return"color"===r?{value:t,dom:(i=o,l={dom:{tag:"div",classes:["tox-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Gm.parts().items({preprocess:"auto"!==i?Wp({tag:"div",classes:["tox-swatches__row"]},i):d})]}]}).dom,components:l.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(l=Gp(o,e)).dom,components:l.components,items:e}:"normal"===r&&1===o?{value:t,dom:(l=Gp(1,e)).dom,components:l.components,items:e}:"normal"===r?{value:t,dom:(l=Gp(o,e)).dom,components:l.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:(a=n,c=o,s=r,f=Pp(s),{tag:"div",classes:z([[f.menu,"tox-menu-"+c+"-column"],a?[f.hasIcons]:[]])}),components:Lp,items:e}:{value:t,dom:(u=o,l={dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Gm.parts().items({preprocess:Wp({tag:"div",classes:["tox-collection__group"]},u)})]}).dom,components:l.components,items:e}},$p=function(t,e,o,r,i,u,a){return Bt(_(t,function(n){return"choiceitem"===n.type?(t=n,Xo("choicemenuitem",Sg,t)).fold(Xp,function(t){return tt.some(Mp(t,1===o,r,e,u(n.value),i,a))}):tt.none();var t}))},Qp=function(t,e,n,o,r){var i=1===n,u=!i||qp(t);return Bt(_(t,function(t){return(n=t,Xo("Autocompleter.Item",pg,n)).fold(Xp,function(t){return tt.some(Fp(t,i,"normal",e,o,r,u))});var n}))},Zp=function(t,n,e,o,r,i,u,a){var c=qp(n),s=$p(n,e,o,"color"!==r?"normal":"color",i,u,a);return Jp(t,c,s,o,r)},th=function(t,n,e,o){var r=qp(n),i=Bt(_(n,function(t){return Kp(t,e,o,r)}));return Jp(t,r,i,1,"normal")},nh=function(t){return Jm.singleData(t.value,t)},eh=function(g,p){var h=gu($m.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],lazySink:p.getSink})),t=function(){return $m.isOpen(h)},v=function(){t()&&$m.hide(h)},n=Yt(function(){return vg(g)}),e=cg(function(t){(" "===t.key?tt.none():gg(g,n)).fold(v,function(m){m.lookupData.then(function(t){var e,n,o,r,i,u,a,c,s,f,l=(e=m.triggerChar,o=At(n=t,function(t){return tt.from(t.columns)}).getOr(1),L(n,function(i){var t=i.items;return Qp(t,function(o,r){var t=g.selection.getRng(),n=t.startContainer;fg(t,e,n.data,t.startOffset).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var n=t.rng,e={hide:v};i.onAction(e,n,o,r)})},o,Sp.BUBBLE_TO_SANDBOX,p)}));if(0<l.length){var d=At(t,function(t){return tt.from(t.columns)}).getOr(1);$m.showAt(h,{anchor:"selection",root:Xn.fromDom(g.getBody()),getSelection:function(){return tt.some({start:function(){return Xn.fromDom(m.range.startContainer)},soffset:function(){return m.range.startOffset},finish:function(){return Xn.fromDom(m.range.endContainer)},foffset:function(){return m.range.endOffset}})}},Gm.sketch((r=Jp("autocompleter-value",!0,l,d,"normal"),i=d,u=xp.ContentFocus,a="normal",c=u===xp.ContentFocus?bl():vl(),s=Up(i,a),f=zp(a),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:f.selectedItem,item:f.item},movement:s,fakeFocus:u===xp.ContentFocus,focusManager:c,menuBehaviours:Tg("auto"!==i?[]:[oi(function(o,t){Og(o,4,f.item).each(function(t){var n=t.numColumns,e=t.numRows;Yd.setGridSize(o,e,n)})})])}))),$m.getContent(h).each(el.highlightFirst)}else v()})})},50);lg({onKeypress:e,closeIfNecessary:v,isActive:t,getView:function(){return $m.getContent(h)}},g)},oh=function(m,g){return function(t){if(m(t)){var n,e,o,r,i,u,a,c=Xn.fromDom(t.target),s=function(){t.stopPropagation()},f=function(){t.preventDefault()},l=p(f,s),d=(n=c,e=t.clientX,o=t.clientY,r=s,i=f,u=l,a=t,{target:Z(n),x:Z(e),y:Z(o),stop:r,prevent:i,kill:u,raw:Z(a)});g(d)}}},rh=function(t,n,e,o,r){var i=oh(e,o);return t.dom().addEventListener(n,i,r),{unbind:v(ih,t,n,i,r)}},ih=function(t,n,e,o){t.dom().removeEventListener(n,e,o)},uh=Z(!0),ah=function(t,n,e){return rh(t,n,uh,e,!1)},ch=function(t,n,e){return rh(t,n,uh,e,!0)},sh=function(t,n,e){return bu(t,n,e).isSome()};function fh(e,o){var r=null;return{cancel:function(){null!==r&&(h.clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=h.setTimeout(function(){e.apply(null,t),r=null},o)}}}var lh=function(t){var n=t.raw();return n.touches===undefined||1!==n.touches.length?tt.none():tt.some(n.touches[0])},dh=function(e){var u=Tt(tt.none()),o=fh(function(t){u.set(tt.none()),e.triggerEvent(Dn(),t)},400),r=kt([{key:Dt(),value:function(e){return lh(e).each(function(t){o.cancel();var n={x:Z(t.clientX),y:Z(t.clientY),target:e.target};o.schedule(e),u.set(tt.some(n))}),tt.none()}},{key:_t(),value:function(t){return o.cancel(),lh(t).each(function(i){u.get().each(function(t){var n,e,o,r;n=i,e=t,o=Math.abs(n.clientX-e.x()),r=Math.abs(n.clientY-e.y()),(5<o||5<r)&&u.set(tt.none())})}),tt.none()}},{key:Mt(),value:function(n){return o.cancel(),u.get().filter(function(t){return ce(t.target(),n.target())}).map(function(t){return e.triggerEvent(Bn(),n)})}}]);return{fireIfReady:function(n,t){return St(r,t).bind(function(t){return t(n)})}}},mh=xn.detect().browser.isFirefox(),gh=Fo([cr("triggerEvent"),vr("stopBackspace",!0)]),ph=function(n,t){var e,o,r,i,u=qo("Getting GUI events settings",gh,t),a=xn.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],c=dh(u),s=_(a.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return ah(n,t,function(n){c.fireIfReady(n,t).each(function(t){t&&n.kill()}),u.triggerEvent(t,n)&&n.kill()})}),f=Tt(tt.none()),l=ah(n,"paste",function(n){c.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),u.triggerEvent("paste",n)&&n.kill(),f.set(tt.some(h.setTimeout(function(){u.triggerEvent(kn(),n)},0)))}),d=ah(n,"keydown",function(t){var n;u.triggerEvent("keydown",t)?t.kill():!0!==u.stopBackspace||8!==(n=t).raw().which||B(["input","textarea"],ke(n.target()))||sh(n.target(),'[contenteditable="true"]')||t.prevent()}),m=(e=n,o=function(t){u.triggerEvent("focusin",t)&&t.kill()},mh?ch(e,"focus",o):ah(e,"focusin",o)),g=Tt(tt.none()),p=(r=n,i=function(t){u.triggerEvent("focusout",t)&&t.kill(),g.set(tt.some(h.setTimeout(function(){u.triggerEvent(Cn(),t)},0)))},mh?ch(r,"blur",i):ah(r,"focusout",i));return{unbind:function(){M(s,function(t){t.unbind()}),d.unbind(),m.unbind(),p.unbind(),l.unbind(),f.get().each(h.clearTimeout),g.get().each(h.clearTimeout)}}},hh=function(t,n){var e=St(t,"target").map(function(t){return t()}).getOr(n);return Tt(e)},vh=gt([{stopped:[]},{resume:["element"]},{complete:[]}]),bh=function(t,o,n,e,r,i){var u,a,c,s,f=t(o,e),l=(u=n,a=r,c=Tt(!1),s=Tt(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:Z(u),setSource:a.set,getSource:a.get});return f.fold(function(){return i.logEventNoHandlers(o,e),vh.complete()},function(n){var e=n.descHandler();return Ai(e)(l),l.isStopped()?(i.logEventStopped(o,n.element(),e.purpose()),vh.stopped()):l.isCut()?(i.logEventCut(o,n.element(),e.purpose()),vh.complete()):le(n.element()).fold(function(){return i.logNoParent(o,n.element(),e.purpose()),vh.complete()},function(t){return i.logEventResponse(o,n.element(),e.purpose()),vh.resume(t)})})},yh=function(n,e,o,t,r,i){return bh(n,e,o,t,r,i).fold(function(){return!0},function(t){return yh(n,e,o,t,r,i)},function(){return!1})},xh=function(t,n,e){var o,r,i=(o=n,r=Tt(!1),{stop:function(){r.set(!0)},cut:Q,isStopped:r.get,isCut:Z(!1),event:Z(o),setSource:u("Cannot set source of a broadcasted event"),getSource:u("Cannot get source of a broadcasted event")});return M(t,function(t){var n=t.descHandler();Ai(n)(i)}),i.isStopped()},wh=function(t,n,e,o,r){var i=hh(e,o);return yh(t,n,e,o,i,r)},Sh=Yn("element","descHandler"),Ch=function(t,n){return{id:Z(t),descHandler:Z(n)}};function kh(){var i={};return{registerId:function(o,r,t){nt(t,function(t,n){var e=i[n]!==undefined?i[n]:{};e[r]=Bi(t,o),i[n]=e})},unregisterId:function(e){nt(i,function(t,n){t.hasOwnProperty(e)&&delete t[e]})},filterByType:function(t){return St(i,t).map(function(t){return rt(t,function(t,n){return Ch(n,t)})}).getOr([])},find:function(t,n,e){var r=xt(n)(i);return Gr(e,function(t){return e=r,gi(o=t).fold(function(){return tt.none()},function(t){var n=xt(t);return e.bind(n).map(function(t){return Sh(o,t)})});var e,o},t)}}}function Oh(){var o=kh(),r={},i=function(o){var t=o.element();return gi(t).fold(function(){return t="uid-",n=o.element(),e=qe(li+t),mi(n,e),e;var t,n,e},function(t){return t})},u=function(t){gi(t.element()).each(function(t){r[t]=undefined,o.unregisterId(t)})};return{find:function(t,n,e){return o.find(t,n,e)},filter:function(t){return o.filterByType(t)},register:function(t){var n=i(t);Et(r,n)&&function(t,n){var e=r[n];if(e!==t)throw new Error('The tagId "'+n+'" is already used by: '+He(e.element())+"\nCannot use it for: "+He(t.element())+"\nThe conflicting element is"+(Pr(e.element())?" ":" not ")+"already in the DOM");u(t)}(t,n);var e=[t];o.registerId(e,n,t.events()),r[n]=t},unregister:u,getById:function(t){return xt(t)(r)}}}var Eh,Th,Bh=Nf({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=$e(n,["attributes"]);return{uid:t.uid,dom:Je({tag:"div",attributes:Je({role:"presentation"},e)},o),components:t.components,behaviours:Rs(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[vr("components",[]),Vs("containerBehaviours",[]),vr("events",{}),vr("domModification",{}),vr("eventOrder",{})]}),Ah=function(e){var o=function(n){return le(e.element()).fold(function(){return!0},function(t){return ce(n,t)})},r=Oh(),s=function(t,n){return r.find(o,t,n)},t=ph(e.element(),{triggerEvent:function(u,a){return Ue(u,a.target(),function(t){return n=s,e=u,r=t,i=(o=a).target(),wh(n,e,o,i,r);var n,e,o,r,i})}}),i={debugInfo:Z("real"),triggerEvent:function(n,e,o){Ue(n,e,function(t){wh(s,n,o,e,t)})},triggerFocus:function(a,c){gi(a).fold(function(){ml(a)},function(t){Ue(Sn(),a,function(t){var n,e,o,r,i,u;n=s,e=Sn(),o={originator:Z(c),kill:Q,prevent:Q,target:Z(a)},i=t,u=hh(o,r=a),bh(n,e,o,r,u,i)})})},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element(),n.event())},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:gu,addToGui:function(t){a(t)},removeFromGui:function(t){c(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){u(t)},broadcast:function(t){l(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:Z(!0)},n=function(t){t.connect(i),Te(t.element())||(r.register(t),M(t.components(),n),i.triggerEvent(Fn(),t.element(),{target:Z(t.element())}))},u=function(t){Te(t.element())||(M(t.components(),u),r.unregister(t)),t.disconnect()},a=function(t){ms(e,t)},c=function(t){hs(t)},f=function(e){var t=r.filter(On());M(t,function(t){var n=t.descHandler();Ai(n)(e)})},l=function(t){f({universal:Z(!0),data:Z(t)})},d=function(t,n){f({universal:Z(!1),channels:Z(t),data:Z(n)})},m=function(t,n){var e=r.filter(t);return xh(e,n)},g=function(t){return r.getById(t).fold(function(){return mt.error(new Error('Could not find component with uid: "'+t+'" in system.'))},mt.value)},p=function(t){var n=gi(t).getOr("not found");return g(n)};return n(e),{root:Z(e),element:e.element,destroy:function(){t.unbind(),we(e.element())},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:u,broadcast:l,broadcastOn:d,broadcastEvent:m}},Dh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),_h=tinymce.util.Tools.resolve("tinymce.EditorManager"),Mh=function(t){return tt.from(t.settings.min_width).filter(O)},Fh=function(t){return tt.from(t.settings.min_height).filter(O)},Ih=function(n){var t=J(n.settings),e=F(t,function(t){return/^toolbar([1-9])$/.test(t)}),o=_(e,function(t){return n.getParam(t,!1,"string")}),r=F(o,function(t){return"string"==typeof t});return 0<r.length?tt.some(r):tt.none()},Vh=function(t){return t.getParam("toolbar_drawer",!1,"boolean")},Rh=qe("form-component-change"),Hh=qe("form-close"),Nh=qe("form-cancel"),Ph=qe("form-action"),zh=qe("form-submit"),Lh=qe("form-block"),jh=qe("form-unblock"),Uh=qe("form-tabchange"),Wh=qe("form-resize"),Gh=Z([vr("prefix","form-field"),Vs("fieldBehaviours",[Uf,lm])]),Xh=Z([df({schema:[rr("dom")],name:"label"}),df({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[rr("text")],name:"aria-descriptor"}),ff({factory:{sketch:function(t){var n=yt(t,["factory"]);return t.factory.sketch(n)}},schema:[rr("factory")],name:"field"})]),Yh=Pf({name:"FormField",configFields:Gh(),partFields:Xh(),factory:function(r,t,n,e){var o=Hs(r.fieldBehaviours,[Uf.config({find:function(t){return kf(t,r,"field")}}),lm.config({store:{mode:"manual",getValue:function(t){return Uf.getCurrent(t).bind(lm.getValue)},setValue:function(t,n){Uf.getCurrent(t).each(function(t){lm.setValue(t,n)})}}})]),i=Xr([oi(function(t,n){var o=Ef(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=qe(r.prefix);o.label().each(function(t){De(t.element(),"for",n),De(e.element(),"id",n)}),o["aria-descriptor"]().each(function(t){var n=qe(r.prefix);De(t.element(),"id",n),De(e.element(),"aria-describedby",n)})})})]),u={getField:function(t){return kf(t,r,"field")},getLabel:function(t){return kf(t,r,"label")}};return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:u}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),qh=/* */Object.freeze({getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),Kh=[ir("others",Go(mt.value,$o()))],Jh=ju({fields:Kh,name:"coupling",apis:qh,state:/* */Object.freeze({init:function(t){var i={},n=Z({});return Oi({readState:n,getOrCreate:function(e,o,r){var t=J(o.others);if(t)return St(i,r).getOrThunk(function(){var t=St(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+Oo(t,null,2))}})}})}),$h=/* */Object.freeze({events:function(t,n){var e=t.stream.streams.setup(t,n);return Xr([Kr(t.event,e),ri(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[Kr(t,function(){return n.cancel()})]}).getOr([])))}}),Qh=function(t){var n=Tt(null);return Oi({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})},Zh=/* */Object.freeze({throttle:Qh,init:function(t){return t.stream.streams.state(t)}}),tv=[ir("stream",Jo("mode",{throttle:[rr("delay"),vr("stopEvent",!0),Mu("streams",{setup:function(t,n){var e=t.stream,o=cg(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:Qh})]})),vr("event","input"),dr("cancelEvent"),Du("onStream")],nv=ju({fields:tv,name:"streaming",active:$h,state:Zh}),ev=function(t){var e=tt.none(),n=[],o=function(t){r()?u(t):n.push(t)},r=function(){return e.isSome()},i=function(t){M(t,u)},u=function(n){e.each(function(t){setTimeout(function(){n(t)},0)})};return t(function(t){e=tt.some(t),i(n),n=[]}),{get:o,map:function(e){return ev(function(n){o(function(t){n(e(t))})})},isReady:r}},ov={nu:ev,pure:function(n){return ev(function(t){t(n)})}},rv=function(n){var t=function(t){var o;n((o=t,function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=this;setTimeout(function(){o.apply(e,t)},0)}))},e=function(){return ov.nu(t)};return{map:function(o){return rv(function(e){t(function(t){var n=o(t);e(n)})})},bind:function(e){return rv(function(n){t(function(t){e(t).get(n)})})},anonBind:function(e){return rv(function(n){t(function(t){e.get(n)})})},toLazy:e,toCached:function(){var n=null;return rv(function(t){null===n&&(n=e()),n.get(t)})},get:t}},iv={nu:rv,pure:function(n){return rv(function(t){t(n)})}},uv=Z("sink"),av=Z(df({name:uv(),overrides:Z({dom:{tag:"div"},behaviours:zu([fs.config({useFixed:!0})]),events:Xr([ti(zt()),ti(Ft()),ti(Wt())])})}));(Th=Eh||(Eh={}))[Th.HighlightFirst=0]="HighlightFirst",Th[Th.HighlightNone=1]="HighlightNone";var cv=function(t,n){var e=t.getHotspot(n).getOr(n);return t.layouts.fold(function(){return{anchor:"hotspot",hotspot:e}},function(t){return{anchor:"hotspot",hotspot:e,layouts:t}})},sv=function(t,n,e,o,r,i,u){var a,c,s,f,l,d,m,g,p,h,v=cv(t,e);return(c=v,f=o,l=r,d=u,m=n,g=s=e,p=(0,(a=t).fetch)(g).map(m),h=mv(s,a),p.map(function(t){return t.bind(function(t){return tt.from(Jm.sketch(Je({},l.menu(),{uid:pi(""),data:t,highlightImmediately:d===Eh.HighlightFirst,onOpenMenu:function(t,n){var e=h().getOrDie();fs.position(e,c,n),As.decloak(f)},onOpenSubmenu:function(t,n,e){var o=h().getOrDie();fs.position(o,{anchor:"submenu",item:n},e),As.decloak(f)},onEscape:function(){return hm.focus(s),As.close(f),tt.some(!0)}})))})})).map(function(t){return t.fold(function(){As.isOpen(o)&&As.close(o)},function(t){As.cloak(o),As.open(o,t),i(o)}),o})},fv=function(t,n,e,o,r,i,u){return As.close(o),iv.pure(o)},lv=function(t,n,e,o,r,i){var u=Jh.getCoupled(e,"sandbox");return(As.isOpen(u)?fv:sv)(t,n,e,u,o,r,i)},dv=function(t,n,e){var o,r,i=Uf.getCurrent(n).getOr(n),u=ua(t.element());e?Ki(i.element(),"min-width",u+"px"):(o=i.element(),r=u,ia.set(o,r))},mv=function(n,t){return n.getSystem().getByUid(t.uid+"-"+uv()).map(function(t){return function(){return mt.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return mt.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},gv=function(o,r,i){var n,u=(n=qe("aria-owns"),{id:Z(n),link:function(t){De(t,"aria-owns",n)},unlink:function(t){Ie(t,"aria-owns")}}),t=mv(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id()}},behaviours:Ps(o.sandboxBehaviours,[lm.config({store:{mode:"memory",initialValue:r}}),As.config({onOpen:function(t,n){var e=cv(o,r);u.link(r.element()),o.matchWidth&&dv(e.hotspot,n,o.useMinWidth),o.onOpen(e,t,n),i!==undefined&&i.onOpen!==undefined&&i.onOpen(t,n)},onClose:function(t,n){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(t,n)},isPartOf:function(t,n,e){return xu(n,e)||xu(r,e)},getAttachPoint:function(){return t().getOrDie()}}),Uf.config({find:function(t){return As.getState(t).bind(function(t){return Uf.getCurrent(t)})}}),Fs({isExtraPart:Z(!1)})])}},pv=function(t,n,e){var o=lm.getValue(e);lm.setValue(n,o),vv(n)},hv=function(t,n){var e=t.element(),o=ou(e),r=e.dom();"number"!==Me(e,"type")&&n(r,o)},vv=function(t){hv(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},bv=function(t,n,o){if(t.selectsOver){var e=lm.getValue(n),r=t.getDisplayText(e),i=lm.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?tt.some(function(){var t,e;pv(0,n,o),t=n,e=r.length,hv(t,function(t,n){return t.setSelectionRange(e,n.length)})}):tt.none()}return tt.none()},yv=Z([dr("data"),vr("inputAttributes",{}),vr("inputStyles",{}),vr("tag","input"),vr("inputClasses",[]),Bu("onSetValue"),vr("styles",{}),vr("eventOrder",{}),Vs("inputBehaviours",[lm,hm]),vr("selectOnFocus",!0)]),xv=function(t){return zu([hm.config({onFocus:!1===t.selectOnFocus?Q:function(t){var n=t.element(),e=ou(n);n.dom().setSelectionRange(0,e.length)}})])},wv=function(t){return{tag:t.tag,attributes:Je({type:"input"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},Sv=Z("alloy.typeahead.itemexecute"),Cv=function(){return[vr("sandboxClasses",[]),Ns("sandboxBehaviours",[Uf,qu,As,lm])]},kv=Z([dr("lazySink"),rr("fetch"),vr("minChars",5),vr("responseTime",1e3),Bu("onOpen"),vr("getHotspot",tt.some),vr("layouts",tt.none()),vr("eventOrder",{}),kr("model",{},[vr("getDisplayText",function(t){return t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.value}),vr("selectsOver",!0),vr("populateFromBrowse",!0)]),Bu("onSetValue"),Au("onExecute"),Bu("onItemExecute"),vr("inputClasses",[]),vr("inputAttributes",{}),vr("inputStyles",{}),vr("matchWidth",!0),vr("useMinWidth",!1),vr("dismissOnBlur",!0),Eu(["openClass"]),dr("initialData"),Vs("typeaheadBehaviours",[hm,lm,nv,Yd,Am,Jh]),Or("previewing",function(){return Tt(!0)})].concat(yv()).concat(Cv())),Ov=Z([lf({schema:[Ou()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){bv(o.model,t,e).fold(function(){return el.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&pv(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOption().map(function(t){return Dr(t,Sv(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&pv(o.model,t,n)})}}}})]),Ev=Pf({name:"Typeahead",configFields:kv(),partFields:Ov(),factory:function(r,t,n,i){var e=function(t,n,e){r.previewing.set(!1);var o=Jh.getCoupled(t,"sandbox");As.isOpen(o)?Uf.getCurrent(o).each(function(t){el.getHighlighted(t).fold(function(){e(t)},function(){Ir(o,t.element(),"keydown",n)})}):sv(r,u(t),t,o,i,function(t){Uf.getCurrent(t).each(e)},Eh.HighlightFirst).get(Q)},o=xv(r),u=function(o){return function(t){return t.map(function(t){var n=it(t.menus),e=L(n,function(t){return F(t.items,function(t){return"item"===t.type})});return lm.getState(o).update(_(e,function(t){return t.data})),t})}},a=[hm.config({}),lm.config({onSetValue:r.onSetValue,store:Je({mode:"dataset",getDataKey:function(t){return ou(t.element())},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){ru(t.element(),r.model.getDisplayText(n))}},r.initialData.map(function(t){return Ct("initialValue",t)}).getOr({}))}),nv.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e=Jh.getCoupled(t,"sandbox");if(hm.isFocused(t)&&ou(t.element()).length>=r.minChars){var o=Uf.getCurrent(e).bind(function(t){return el.getHighlighted(t).map(lm.getValue)});r.previewing.set(!0),sv(r,u(t),t,e,i,function(t){Uf.getCurrent(e).each(function(t){o.fold(function(){r.model.selectsOver&&el.highlightFirst(t)},function(n){el.highlightBy(t,function(t){return lm.getValue(t).value===n.value}),el.getHighlighted(t).orThunk(function(){return el.highlightFirst(t),tt.none()})})})},Eh.HighlightFirst).get(Q)}},cancelEvent:Mn()}),Yd.config({mode:"special",onDown:function(t,n){return e(t,n,el.highlightFirst),tt.some(!0)},onEscape:function(t){var n=Jh.getCoupled(t,"sandbox");return As.isOpen(n)?(As.close(n),tt.some(!0)):tt.none()},onUp:function(t,n){return e(t,n,el.highlightLast),tt.some(!0)},onEnter:function(n){var t=Jh.getCoupled(n,"sandbox"),e=As.isOpen(t);if(e&&!r.previewing.get())return Uf.getCurrent(t).bind(function(t){return el.getHighlighted(t)}).map(function(t){return Dr(n,Sv(),{item:t}),!0});var o=lm.getValue(n);return Ar(n,Mn()),r.onExecute(t,n,o),e&&As.close(t),tt.some(!0)}}),Am.config({toggleClass:r.markers.openClass,aria:{mode:"pressed",syncWithExpanded:!0}}),Jh.config({others:{sandbox:function(t){return gv(r,t,{onOpen:d,onClose:d})}}}),Rm("typeaheadevents",[ui(function(t){var n=Q;lv(r,u(t),t,i,n,Eh.HighlightFirst).get(Q)}),Kr(Sv(),function(t,n){var e=Jh.getCoupled(t,"sandbox");pv(r.model,t,n.event().item()),Ar(t,Mn()),r.onItemExecute(t,e,n.event().item(),lm.getValue(t)),As.close(e),vv(t)})].concat(r.dismissOnBlur?[Kr(Cn(),function(t){var n=Jh.getCoupled(t,"sandbox");pl(n.element()).isNone()&&As.close(n)})]:[]))];return{uid:r.uid,dom:wv(r),behaviours:Je({},o,Hs(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),Tv=function(t,n,e){var o=Av(t,n,e);return Yh.sketch(o)},Bv=function(t,n){return Tv(t,n,[])},Av=function(t,n,e){return{dom:Dv(e),components:t.toArray().concat([n])}},Dv=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},_v=function(t,n){return Yh.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},Mv=function(t){return"separator"===t.type},Fv={type:"separator"},Iv=function(t,e){var n=V(t,function(t,n){return w(n)?""===n?t:"|"===n?0<t.length&&!Mv(t[t.length-1])?t.concat([Fv]):t:at(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[]);return 0<n.length&&Mv(n[n.length-1])&&n.pop(),n},Vv=function(t,n){return at(t,"getSubmenuItems")?(o=n,r=(e=t).getSubmenuItems(),i=Rv(r,o),{item:e,menus:vt(i.menus,Ct(e.value,i.items)),expansions:vt(i.expansions,Ct(e.value,e.value))}):{item:t,menus:{},expansions:{}};var e,o,r,i},Rv=function(t,r){var n=Iv(w(t)?t.split(" "):t,r);return I(n,function(t,n){var e=function(t){if(Mv(t))return t;var n=St(t,"value").getOrThunk(function(){return qe("generated-menu-item")});return vt({value:n},t)}(n),o=Vv(e,r);return{menus:vt(t.menus,o.menus),items:[o.item].concat(t.items),expansions:vt(t.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},Hv=function(t,e,o){var n=qe("primary-menu"),r=Rv(t,o.menuItems());if(0===r.items.length)return tt.none();var i=th(n,r.items,e,o),u=et(r.menus,function(t,n){return th(n,t,e,o)}),a=vt(u,Ct(n,i));return tt.from(Jm.tieredData(n,a,r.expansions))},Nv=Nf({name:"Input",configFields:yv(),factory:function(t,n){return{uid:t.uid,dom:wv(t),components:[],behaviours:(e=t,Je({},xv(e),Hs(e.inputBehaviours,[lm.config({store:{mode:"manual",initialValue:e.data.getOr(undefined),getValue:function(t){return ou(t.element())},setValue:function(t,n){ou(t.element())!==n&&ru(t.element(),n)}},onSetValue:e.onSetValue})]))),eventOrder:t.eventOrder};var e}}),Pv=xn.detect().browser.isFirefox(),zv={position:"absolute",left:"-9999px"},Lv=function(t,n,e){var o,r,i,u=function(t,n){var e=Xn.fromTag("span",t.dom());De(e,"role","presentation");var o=Xn.fromText(n,t.dom());return be(e,o),e}(se(n),e);Pv&&(o=n,r=u,i=qe("ephox-alloy-aria-voice"),De(r,"id",i),De(o,"aria-describedby",i)),_e(u,t(e)),Ji(u,zv),be(n,u),h.setTimeout(function(){Ie(u,"aria-live"),we(u)},1e3)},jv=function(t){return{"aria-live":"assertive","aria-atomic":"true",role:"alert"}},Uv=["input","textarea"],Wv=function(t){var n=ke(t);return B(Uv,n)},Gv=function(t,n){var e=n.getRoot(t).getOr(t.element());ji(e,n.invalidClass),n.notify.each(function(n){Wv(t.element())&&Ie(e,"title"),n.getContainer(t).each(function(t){Ce(t,n.validHtml)}),n.onValid(t)})},Xv=function(e,t,n,o){var r=t.getRoot(e).getOr(e.element());zi(r,t.invalidClass),t.notify.each(function(t){var n;Wv(e.element())&&De(e.element(),"title",o),n=zr(),Lv(jv,n,o),t.getContainer(e).each(function(t){Ce(t,o)}),t.onInvalid(e,o)})},Yv=function(n,t,e){return t.validator.fold(function(){return iv.pure(mt.value(!0))},function(t){return t.validate(n)})},qv=function(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Yv(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return Xv(n,e,0,t),mt.error(t)},function(t){return Gv(n,e),mt.value(t)}):mt.error("No longer in system")})},Kv=/* */Object.freeze({markValid:Gv,markInvalid:Xv,query:Yv,run:qv,isInvalid:function(t,n){var e=n.getRoot(t).getOr(t.element());return Ui(e,n.invalidClass)}}),Jv=/* */Object.freeze({events:function(n,t){return n.validator.map(function(t){return Xr([Kr(t.onEvent,function(t){qv(t,n).get(d)})].concat(t.validateOnLoad?[oi(function(t){qv(t,n).get(Q)})]:[]))}).getOr({})}}),$v=[rr("invalidClass"),vr("getRoot",tt.none),hr("notify",[vr("aria","alert"),vr("getContainer",tt.none),vr("validHtml",""),Bu("onValid"),Bu("onInvalid"),Bu("onValidate")]),hr("validator",[rr("validate"),vr("onEvent","input"),vr("validateOnLoad",!0)])],Qv=ju({fields:$v,name:"invalidating",active:Jv,apis:Kv,extra:{validation:function(e){return function(t){var n=lm.getValue(t);return iv.pure(e(n))}}}}),Zv=/* */Object.freeze({exhibit:function(t,n){return Ti({attributes:kt([{key:n.tabAttr,value:"true"}])})}}),tb=[vr("tabAttr","data-alloy-tabstop")],nb=ju({fields:tb,name:"tabstopping",active:Zv}),eb=function(t){return{value:Z(t)}},ob=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,rb=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ib=function(t){return ob.test(t)||rb.test(t)},ub=function(t){var n,e=(n=t.value().replace(ob,function(t,n,e,o){return n+n+e+e+o+o}),{value:Z(n)});return rb.exec(e.value())},ab=function(t){var n=t.toString(16);return 1==n.length?"0"+n:n},cb=function(t){var n=ab(t.red())+ab(t.green())+ab(t.blue());return eb(n)},sb=Math.min,fb=Math.max,lb=Math.round,db=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,mb=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,gb=function(t,n,e,o){return{red:Z(t),green:Z(n),blue:Z(e),alpha:Z(o)}},pb=function(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255},hb=function(t){var n,e,o,r,i,u,a,c,s,f;if(i=(t.hue()||0)%360,u=t.saturation()/100,a=t.value()/100,u=fb(0,sb(u,1)),a=fb(0,sb(a,1)),0===u)return c=s=f=lb(255*a),gb(c,s,f,1);switch(n=i/60,o=(e=a*u)*(1-Math.abs(n%2-1)),r=a-e,Math.floor(n)){case 0:c=e,s=o,f=0;break;case 1:c=o,s=e,f=0;break;case 2:c=0,s=e,f=o;break;case 3:c=0,s=o,f=e;break;case 4:c=o,s=0,f=e;break;case 5:c=e,s=0,f=o;break;default:c=s=f=0}return c=lb(255*(c+r)),s=lb(255*(s+r)),f=lb(255*(f+r)),gb(c,s,f,1)},vb=function(t){var n=ub(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return gb(e,o,r,1)},bb=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return gb(r,i,u,a)},yb=function(t){return"rgba("+t.red()+","+t.green()+","+t.blue()+","+t.alpha()+")"},xb=Z(gb(255,0,0,1)),wb=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),Sb="tinymce-custom-colors",Cb="choiceitem",kb=[{type:Cb,text:"Turquoise",value:"#18BC9B"},{type:Cb,text:"Green",value:"#2FCC71"},{type:Cb,text:"Blue",value:"#3598DB"},{type:Cb,text:"Purple",value:"#9B59B6"},{type:Cb,text:"Navy Blue",value:"#34495E"},{type:Cb,text:"Dark Turquoise",value:"#18A085"},{type:Cb,text:"Dark Green",value:"#27AE60"},{type:Cb,text:"Medium Blue",value:"#2880B9"},{type:Cb,text:"Medium Purple",value:"#8E44AD"},{type:Cb,text:"Midnight Blue",value:"#2B3E50"},{type:Cb,text:"Yellow",value:"#F1C40F"},{type:Cb,text:"Orange",value:"#E67E23"},{type:Cb,text:"Red",value:"#E74C3C"},{type:Cb,text:"Light Gray",value:"#ECF0F1"},{type:Cb,text:"Gray",value:"#95A5A6"},{type:Cb,text:"Dark Yellow",value:"#F29D12"},{type:Cb,text:"Dark Orange",value:"#D35400"},{type:Cb,text:"Dark Red",value:"#E74C3C"},{type:Cb,text:"Medium Gray",value:"#BDC3C7"},{type:Cb,text:"Dark Gray",value:"#7E8C8D"},{type:Cb,text:"Black",value:"#000000"},{type:Cb,text:"White",value:"#ffffff"}],Ob=function PA(r){void 0===r&&(r=10);var t,n=wb.getItem(Sb),e=w(n)?JSON.parse(n):[],i=r-(t=e).length<0?t.slice(0,r):t,u=function(t){i.splice(t,1)};return{add:function(t){var n,e,o;(n=i,e=t,o=T(n,e),-1===o?tt.none():tt.some(o)).each(u),i.unshift(t),i.length>r&&i.pop(),wb.setItem(Sb,JSON.stringify(i))},state:function(){return i.slice(0)}}}(10),Eb=function(t){var n,e=[];for(n=0;n<t.length;n+=2)e.push({text:t[n+1],value:"#"+t[n],type:"choiceitem"});return e},Tb=function(t){return t.getParam("color_map")},Bb=function(t,n){return t.getParam("color_cols",n,"number")},Ab=function(t){return!1!==t.getParam("custom_colors")},Db=function(t){var n=Tb(t);return n!==undefined?Eb(n):kb},_b=function(){return _(Ob.state(),function(t){return{type:Cb,text:t,value:t}})},Mb=function(t){Ob.add(t)},Fb=function(t,e){var o;return t.dom.getParents(t.selection.getStart(),function(t){var n;(n=t.style["forecolor"===e?"color":"background-color"])&&(o=o||n)}),o},Ib=function(t){return Math.max(5,Math.ceil(Math.sqrt(t)))},Vb=function(t){var n=Db(t),e=Ib(n.length);return Bb(t,e)},Rb=function(n,e,t,o){"custom"===t?zb(n)(function(t){t.each(function(t){Mb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))},Hb=function(o,r){return function(t){var n,e;t(o.concat(_b().concat((e={type:n="choiceitem",text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},r?[e,{type:n,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]))))}},Nb=function(t,n,e){var o,r;o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,t.setIconFill(o,r),t.setIconStroke(o,r)},Pb=function(o,e,r,t,i){o.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return tt.from(Fb(o,r)).bind(function(t){return function(t){if("transparent"===t)return tt.some(gb(0,0,0,0));if(db.test(t)){var n=db.exec(t);return tt.some(bb(n[1],n[2],n[3],"1"))}if(mb.test(t)){var e=db.exec(t);return tt.some(bb(e[1],e[2],e[3],e[4]))}return tt.none()}(t).map(function(t){var n=cb(t).value();return mn(e.toLowerCase(),n)})}).getOr(!1)},columns:Vb(o),fetch:Hb(Db(o),Ab(o)),onAction:function(t){null!==i.get()&&Rb(o,r,i.get(),function(){})},onItemAction:function(n,t){Rb(o,r,t,function(t){i.set(t),Nb(n,e,t)})},onSetup:function(t){return null!==i.get()&&Nb(t,e,i.get()),function(){}}})},zb=function(i){return function(t,n){var e,o={colorpicker:n},r=(e=t,function(t){var n=t.getData();e(tt.from(n.colorpicker)),t.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(t,n){"hex-valid"===n.name&&(n.value?t.enable("ok"):t.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){t(tt.none())}})}},Lb={register:function(t){var i;(i=t).addCommand("mceApplyTextcolor",function(t,n){var e,o,r;o=t,r=n,(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e;e=t,(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})});var n=Tt(null),e=Tt(null);Pb(t,"forecolor","forecolor","Text color",n),Pb(t,"backcolor","hilitecolor","Background color",e)},getFetch:Hb,colorPickerDialog:zb,getCurrentColor:Fb,getColorCols:Vb,calcCols:Ib},jb=Z([rr("dom"),rr("fetch"),Bu("onOpen"),Au("onExecute"),vr("getHotspot",tt.some),vr("layouts",tt.none()),Vs("dropdownBehaviours",[Am,Jh,Yd,hm]),rr("toggleClass"),vr("eventOrder",{}),dr("lazySink"),vr("matchWidth",!1),vr("useMinWidth",!1),dr("role")].concat(Cv())),Ub=Z([lf({schema:[Ou()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),av()]),Wb=Pf({name:"Dropdown",configFields:jb(),partFields:Ub(),factory:function(n,t,e,o){var r,i,u=function(t){As.getState(t).each(function(t){Jm.highlightPrimary(t)})},a={expand:function(t){Am.isOn(t)||lv(n,function(t){return t},t,o,Q,Eh.HighlightNone).get(Q)},open:function(t){Am.isOn(t)||lv(n,function(t){return t},t,o,Q,Eh.HighlightFirst).get(Q)},isOpen:Am.isOn,close:function(t){Am.isOn(t)&&lv(n,function(t){return t},t,o,Q,Eh.HighlightFirst).get(Q)}},c=function(t,n){return _r(t),tt.some(!0)};return{uid:n.uid,dom:n.dom,components:t,behaviours:Hs(n.dropdownBehaviours,[Am.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),Jh.config({others:{sandbox:function(t){return gv(n,t,{onOpen:function(){Am.on(t)},onClose:function(){Am.off(t)}})}}}),Yd.config({mode:"special",onSpace:c,onEnter:c,onDown:function(t,n){if(Wb.isOpen(t)){var e=Jh.getCoupled(t,"sandbox");u(e)}else Wb.open(t);return tt.some(!0)},onEscape:function(t,n){return Wb.isOpen(t)?(Wb.close(t),tt.some(!0)):tt.none()}}),hm.config({})]),events:Qm(tt.some(function(t){lv(n,function(t){return t},t,o,u,Eh.HighlightFirst).get(Q)})),eventOrder:Je({},n.eventOrder,(r={},r[En()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:a,domModification:{attributes:Je({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}}),"button"===n.dom.tag?{type:(i="type",St(n.dom,"attributes").bind(function(t){return St(t,i)})).getOr("button")}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)}}}),Gb=ju({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(t){return Xr([Yr(Xt(),Z(!0))])},exhibit:function(t,n){return Ti({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Xb=qe("color-input-change"),Yb=qe("color-swatch-change"),qb=qe("color-picker-cancel"),Kb=function(t,n,o){var e,r,i=Yh.parts().field({factory:Nv,inputClasses:["tox-textfield"],onSetValue:function(t){return Qv.run(t).get(function(){})},inputBehaviours:zu([nb.config({}),Qv.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return le(t.element())},notify:{onValid:function(t){var n=lm.getValue(t);Dr(t,Xb,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=lm.getValue(t);if(0===n.length)return iv.pure(mt.value(!0));var e=Xn.fromTag("span");Ki(e,"background-color",n);var o=Zi(e,"background-color").fold(function(){return mt.error("blah")},function(t){return mt.value(n)});return iv.pure(o)}}})]),selectOnFocus:!1}),u=t.label.map(function(t){return _v(t,n.providers)}),a=function(t,n){Dr(t,Yb,{value:n})},c=tg((e={dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:tt.some({onRtl:function(){return[ec]},onLtr:function(){return[oc]}}),components:[],fetch:Lb.getFetch(o.getColors(),o.hasCustomColors()),onItemAction:function(e){n.getSink().each(function(t){c.getOpt(t).each(function(n){"custom"===e?o.colorPicker(function(t){t.fold(function(){return Ar(n,qb)},function(t){a(n,t),Mb(t)})},"#ffffff"):a(n,"remove"===e?"":e)})})}},r=n,Wb.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:zu([Gb.config({}),nb.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:r.getSink,fetch:function(){return iv.nu(function(t){return e.fetch(t)}).map(function(t){return tt.from(nh(vt(Zp(qe("menu-value"),t,function(t){e.onItemAction(t)},5,"color",Sp.CLOSE_ON_EXECUTE,function(){return!1},r.providers),{movement:Up(5,"color")})))})},parts:{menu:jp(0,0,"color")}})));return Yh.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:u.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[i,c.asSpec()]}]),fieldBehaviours:zu([Rm("form-field-events",[Kr(Xb,function(t,n){c.getOpt(t).each(function(t){Ki(t.element(),"background-color",n.event().color())})}),Kr(Yb,function(n,e){Yh.getField(n).each(function(t){lm.setValue(t,e.event().value()),Uf.getCurrent(n).each(hm.focus)})}),Kr(qb,function(n,t){Yh.getField(n).each(function(t){Uf.getCurrent(n).each(hm.focus)})})])])})},Jb=xn.detect().deviceType.isTouch(),$b=df({schema:[rr("dom")],name:"label"}),Qb=function(t){return df({name:t+"-edge",overrides:function(o){return o.model.manager.edgeActions[t].fold(function(){return{}},function(e){var t=Xr([Jr(Dt(),e,[o])]),n=Xr([Jr(Ft(),e,[o]),Jr(It(),function(t,n){n.mouseIsDown.get()&&e(t,n)},[o])]);return{events:Jb?t:n}})}})},Zb=Qb("top-left"),ty=Qb("top"),ny=Qb("top-right"),ey=Qb("right"),oy=Qb("bottom-right"),ry=Qb("bottom"),iy=Qb("bottom-left"),uy=[$b,Qb("left"),ey,ty,ry,Zb,ny,iy,oy,ff({name:"thumb",defaults:Z({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:Xr([Qr(Dt(),t,"spectrum"),Qr(_t(),t,"spectrum"),Qr(Mt(),t,"spectrum"),Qr(Ft(),t,"spectrum"),Qr(It(),t,"spectrum"),Qr(Rt(),t,"spectrum")])}}}),ff({schema:[Or("mouseIsDown",function(){return Tt(!1)})],name:"spectrum",overrides:function(e){var o=e.model.manager,r=function(n,t){return o.getValueFromEvent(t).map(function(t){return o.setValueFrom(n,e,t)})},t=Xr([Kr(Dt(),r),Kr(_t(),r)]),n=Xr([Kr(Ft(),r),Kr(It(),function(t,n){e.mouseIsDown.get()&&r(t,n)})]);return{behaviours:zu(Jb?[]:[Yd.config({mode:"special",onLeft:function(t){return o.onLeft(t,e)},onRight:function(t){return o.onRight(t,e)},onUp:function(t){return o.onUp(t,e)},onDown:function(t){return o.onDown(t,e)}}),hm.config({})]),events:Jb?t:n}}})],ay=xn.detect().deviceType.isTouch(),cy=Z("slider.change.value"),sy=function(t){var n=t.event().raw();if(ay){var e=n;return e.touches!==undefined&&1===e.touches.length?tt.some(e.touches[0]).map(function(t){return $u(t.clientX,t.clientY)}):tt.none()}var o=n;return o.clientX!==undefined?tt.some(o).map(function(t){return $u(t.clientX,t.clientY)}):tt.none()},fy=function(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)},ly=function(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)},dy=function(t,n,e){return Math.max(n,Math.min(e,t))},my=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,f=t.hasMaxEdge,l=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h,v,b,y,x,w,S,C=(x=r,w=l,S=d,Math.min(S,Math.max(x,w))-w),k=dy(C/m*o+n,g,p);return u&&n<=k&&k<=e?(h=k,v=n,b=e,y=i,a.fold(function(){var t=h-v,n=Math.round(t/y)*y;return dy(v+n,v-1,b+1)},function(t){var n=(h-t)%y,e=Math.round(n/y),o=Math.floor((h-t)/y),r=Math.floor((b-t)/y),i=t+Math.min(r,o+e)*y;return Math.max(t,i)})):c?Math.round(k):k},gy=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,f=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:f:(r-n)/o*c},py=function(t){return t.model.minX},hy=function(t){return t.model.minY},vy=function(t){return t.model.minX-1},by=function(t){return t.model.minY-1},yy=function(t){return t.model.maxX},xy=function(t){return t.model.maxY},wy=function(t){return t.model.maxX+1},Sy=function(t){return t.model.maxY+1},Cy=function(t,n,e){return n(t)-e(t)},ky=function(t){return Cy(t,yy,py)},Oy=function(t){return Cy(t,xy,hy)},Ey=function(t){return ky(t)/2},Ty=function(t){return Oy(t)/2},By=function(t){return t.stepSize},Ay=function(t){return t.snapToGrid},Dy=function(t){return t.snapStart},_y=function(t){return t.rounded},My=function(t,n){return t[n+"-edge"]!==undefined},Fy=function(t){return My(t,"left")},Iy=function(t){return My(t,"right")},Vy=function(t){return My(t,"top")},Ry=function(t){return My(t,"bottom")},Hy=function(t){return t.model.value.get()},Ny=function(t){return{x:Z(t)}},Py=function(t){return{y:Z(t)}},zy=function(t,n){return{x:Z(t),y:Z(n)}},Ly=function(t,n){Dr(t,cy(),{value:n})},jy="left",Uy=function(t){return t.element().dom().getBoundingClientRect()},Wy=function(t,n){return t[n]},Gy=function(t){var n=Uy(t);return Wy(n,jy)},Xy=function(t){var n=Uy(t);return Wy(n,"right")},Yy=function(t){var n=Uy(t);return Wy(n,"top")},qy=function(t){var n=Uy(t);return Wy(n,"bottom")},Ky=function(t){var n=Uy(t);return Wy(n,"width")},Jy=function(t){var n=Uy(t);return Wy(n,"height")},$y=function(t,n,e){return(t+n)/2-e},Qy=function(t,n){var e=Uy(t),o=Uy(n),r=Wy(e,jy),i=Wy(e,"right"),u=Wy(o,jy);return $y(r,i,u)},Zy=function(t,n){var e=Uy(t),o=Uy(n),r=Wy(e,"top"),i=Wy(e,"bottom"),u=Wy(o,"top");return $y(r,i,u)},tx=function(t,n){Dr(t,cy(),{value:n})},nx=function(t){return{x:Z(t)}},ex=function(t,n,e){var o={min:py(n),max:yy(n),range:ky(n),value:e,step:By(n),snap:Ay(n),snapStart:Dy(n),rounded:_y(n),hasMinEdge:Fy(n),hasMaxEdge:Iy(n),minBound:Gy(t),maxBound:Xy(t),screenRange:Ky(t)};return my(o)},ox=function(u){return function(t,n){return(e=u,o=t,r=n,i=(0<e?ly:fy)(Hy(r).x(),py(r),yy(r),By(r)),tx(o,nx(i)),tt.some(i)).map(function(){return!0});var e,o,r,i}},rx=function(t,n,e,o,r,i){var u,a,c,s,f,l,d,m,g,p=(a=i,c=e,s=o,f=r,l=Ky(u=n),d=s.bind(function(t){return tt.some(Qy(t,u))}).getOr(0),m=f.bind(function(t){return tt.some(Qy(t,u))}).getOr(l),g={min:py(a),max:yy(a),range:ky(a),value:c,hasMinEdge:Fy(a),hasMaxEdge:Iy(a),minBound:Gy(u),minOffset:0,maxBound:Xy(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},gy(g));return Gy(n)-Gy(t)+p},ix=ox(-1),ux=ox(1),ax=tt.none,cx=tt.none,sx={"top-left":tt.none(),top:tt.none(),"top-right":tt.none(),right:tt.some(function(t,n){Ly(t,Ny(wy(n)))}),"bottom-right":tt.none(),bottom:tt.none(),"bottom-left":tt.none(),left:tt.some(function(t,n){Ly(t,Ny(vy(n)))})},fx=/* */Object.freeze({setValueFrom:function(t,n,e){var o=ex(t,n,e),r=nx(o);return tx(t,r),o},setToMin:function(t,n){var e=py(n);tx(t,nx(e))},setToMax:function(t,n){var e=yy(n);tx(t,nx(e))},findValueOfOffset:ex,getValueFromEvent:function(t){return sy(t).map(function(t){return t.left()})},findPositionOfValue:rx,setPositionFromValue:function(t,n,e,o){var r=Hy(e),i=rx(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=ua(n.element())/2;Ki(n.element(),"left",i-u+"px")},onLeft:ix,onRight:ux,onUp:ax,onDown:cx,edgeActions:sx}),lx=function(t,n){Dr(t,cy(),{value:n})},dx=function(t){return{y:Z(t)}},mx=function(t,n,e){var o={min:hy(n),max:xy(n),range:Oy(n),value:e,step:By(n),snap:Ay(n),snapStart:Dy(n),rounded:_y(n),hasMinEdge:Vy(n),hasMaxEdge:Ry(n),minBound:Yy(t),maxBound:qy(t),screenRange:Jy(t)};return my(o)},gx=function(u){return function(t,n){return(e=u,o=t,r=n,i=(0<e?ly:fy)(Hy(r).y(),hy(r),xy(r),By(r)),lx(o,dx(i)),tt.some(i)).map(function(){return!0});var e,o,r,i}},px=function(t,n,e,o,r,i){var u,a,c,s,f,l,d,m,g,p=(a=i,c=e,s=o,f=r,l=Jy(u=n),d=s.bind(function(t){return tt.some(Zy(t,u))}).getOr(0),m=f.bind(function(t){return tt.some(Zy(t,u))}).getOr(l),g={min:hy(a),max:xy(a),range:Oy(a),value:c,hasMinEdge:Vy(a),hasMaxEdge:Ry(a),minBound:Yy(u),minOffset:0,maxBound:qy(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},gy(g));return Yy(n)-Yy(t)+p},hx=tt.none,vx=tt.none,bx=gx(-1),yx=gx(1),xx={"top-left":tt.none(),top:tt.some(function(t,n){Ly(t,Py(by(n)))}),"top-right":tt.none(),right:tt.none(),"bottom-right":tt.none(),bottom:tt.some(function(t,n){Ly(t,Py(Sy(n)))}),"bottom-left":tt.none(),left:tt.none()},wx=/* */Object.freeze({setValueFrom:function(t,n,e){var o=mx(t,n,e),r=dx(o);return lx(t,r),o},setToMin:function(t,n){var e=hy(n);lx(t,dx(e))},setToMax:function(t,n){var e=xy(n);lx(t,dx(e))},findValueOfOffset:mx,getValueFromEvent:function(t){return sy(t).map(function(t){return t.top()})},findPositionOfValue:px,setPositionFromValue:function(t,n,e,o){var r=Hy(e),i=px(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),u=sa(n.element())/2;Ki(n.element(),"top",i-u+"px")},onLeft:hx,onRight:vx,onUp:bx,onDown:yx,edgeActions:xx}),Sx=function(t,n){Dr(t,cy(),{value:n})},Cx=function(t,n){return{x:Z(t),y:Z(n)}},kx=function(s,f){return function(t,n){return(e=s,o=f,r=t,i=n,u=0<e?ly:fy,a=o?Hy(i).x():u(Hy(i).x(),py(i),yy(i),By(i)),c=o?u(Hy(i).y(),hy(i),xy(i),By(i)):Hy(i).y(),Sx(r,Cx(a,c)),tt.some(a)).map(function(){return!0});var e,o,r,i,u,a,c}},Ox=kx(-1,!1),Ex=kx(1,!1),Tx=kx(-1,!0),Bx=kx(1,!0),Ax={"top-left":tt.some(function(t,n){Ly(t,zy(vy(n),by(n)))}),top:tt.some(function(t,n){Ly(t,zy(Ey(n),by(n)))}),"top-right":tt.some(function(t,n){Ly(t,zy(wy(n),by(n)))}),right:tt.some(function(t,n){Ly(t,zy(wy(n),Ty(n)))}),"bottom-right":tt.some(function(t,n){Ly(t,zy(wy(n),Sy(n)))}),bottom:tt.some(function(t,n){Ly(t,zy(Ey(n),Sy(n)))}),"bottom-left":tt.some(function(t,n){Ly(t,zy(vy(n),Sy(n)))}),left:tt.some(function(t,n){Ly(t,zy(vy(n),Ty(n)))})},Dx=/* */Object.freeze({setValueFrom:function(t,n,e){var o=ex(t,n,e.left()),r=mx(t,n,e.top()),i=Cx(o,r);return Sx(t,i),i},setToMin:function(t,n){var e=py(n),o=hy(n);Sx(t,Cx(e,o))},setToMax:function(t,n){var e=yy(n),o=xy(n);Sx(t,Cx(e,o))},getValueFromEvent:function(t){return sy(t)},setPositionFromValue:function(t,n,e,o){var r=Hy(e),i=rx(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=px(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),a=ua(n.element())/2,c=sa(n.element())/2;Ki(n.element(),"left",i-a+"px"),Ki(n.element(),"top",u-c+"px")},onLeft:Ox,onRight:Ex,onUp:Tx,onDown:Bx,edgeActions:Ax}),_x=xn.detect().deviceType.isTouch(),Mx=[vr("stepSize",1),vr("onChange",Q),vr("onChoose",Q),vr("onInit",Q),vr("onDragStart",Q),vr("onDragEnd",Q),vr("snapToGrid",!1),vr("rounded",!0),dr("snapStart"),ir("model",Jo("mode",{x:[vr("minX",0),vr("maxX",100),Or("value",function(t){return Tt(t.mode.minX)}),rr("getInitialValue"),Mu("manager",fx)],y:[vr("minY",0),vr("maxY",100),Or("value",function(t){return Tt(t.mode.minY)}),rr("getInitialValue"),Mu("manager",wx)],xy:[vr("minX",0),vr("maxX",100),vr("minY",0),vr("maxY",100),Or("value",function(t){return Tt({x:Z(t.mode.minX),y:Z(t.mode.minY)})}),rr("getInitialValue"),Mu("manager",Dx)]})),Vs("sliderBehaviours",[Yd,lm])].concat(_x?[]:[Or("mouseIsDown",function(){return Tt(!1)})]),Fx=xn.detect().deviceType.isTouch(),Ix=Pf({name:"Slider",configFields:Mx,partFields:uy,factory:function(i,t,n,e){var u=function(t){return Of(t,i,"thumb")},a=function(t){return Of(t,i,"spectrum")},o=function(t){return kf(t,i,"left-edge")},r=function(t){return kf(t,i,"right-edge")},c=function(t){return kf(t,i,"top-edge")},s=function(t){return kf(t,i,"bottom-edge")},f=i.model,l=f.manager,d=function(t,n){l.setPositionFromValue(t,n,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})},m=function(t,n){f.value.set(n);var e=u(t);return d(t,e),i.onChange(t,e,n),tt.some(!0)},g=[Kr(Dt(),function(t,n){i.onDragStart(t,u(t))}),Kr(Mt(),function(t,n){i.onDragEnd(t,u(t))})],p=[Kr(Ft(),function(t,n){n.stop(),i.onDragStart(t,u(t)),i.mouseIsDown.set(!0)}),Kr(Rt(),function(t,n){i.onDragEnd(t,u(t))})],h=Fx?g:p;return{uid:i.uid,dom:i.dom,components:t,behaviours:Hs(i.sliderBehaviours,z([Fx?[]:[Yd.config({mode:"special",focusIn:function(t){return kf(t,i,"spectrum").map(Yd.focusIn).map(Z(!0))}})],[lm.config({store:{mode:"manual",getValue:function(t){return f.value.get()}}}),qu.config({channels:{"mouse.released":{onReceive:function(e,t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&kf(e,i,"thumb").each(function(t){var n=f.value.get();i.onChoose(e,t,n)})}}}})]])),events:Xr([Kr(cy(),function(t,n){m(t,n.event().value())}),oi(function(t,n){var e=f.getInitialValue();f.value.set(e);var o=u(t);d(t,o);var r=a(t);i.onInit(t,o,r,f.value.get())})].concat(h)),apis:{resetToMin:function(t){l.setToMin(t,i)},resetToMax:function(t){l.setToMax(t,i)},changeValue:m,refresh:d},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),Vx=Z(qe("rgb-hex-update")),Rx=Z(qe("slider-update")),Hx=Z(qe("palette-update")),Nx=function(t,o){var r=Ix.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),i=Ix.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}}),u=function(t,n){var e=t.width,o=t.height,r=t.getContext("2d");r.fillStyle=n,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)};return Nf({factory:function(t){var n=Z({x:Z(0),y:Z(0)}),e=zu([Uf.config({find:tt.some}),hm.config({})]);return Ix.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[r,i],onChange:function(t,n,e){Dr(t,Hx(),{value:e})},onInit:function(t,n,e,o){u(e.element().dom(),yb(xb()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(t,n,e){var o,r;o=e,r=n.components()[0].element().dom(),u(r,yb(o))}},extraApis:{}})},Px=function(t,n){var e=Ix.parts().spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=Ix.parts().thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}});return Ix.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:Z({y:Z(0)})},components:[e,o],sliderBehaviours:zu([hm.config({})]),onChange:function(t,n,e){Dr(t,Rx(),{value:e})}})},zx=[Vs("formBehaviours",[lm])],Lx=function(t){return"<alloy.field."+t+">"},jx=function(o,t,n){return{uid:o.uid,dom:o.dom,components:t,behaviours:Hs(o.formBehaviours,[lm.config({store:{mode:"manual",getValue:function(t){var n=Tf(t,o);return et(n,function(t,n){return t().bind(function(t){var n,e=Uf.getCurrent(t);return n="missing current",e.fold(function(){return mt.error(n)},mt.value)}).map(lm.getValue)})},setValue:function(e,t){nt(t,function(n,t){kf(e,o,t).each(function(t){Uf.getCurrent(t).each(function(t){lm.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return kf(t,o,n).bind(Uf.getCurrent)}}}},Ux={getField:Ci(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n=(e=[],{field:function(t,n){return e.push(t),yf("form",Lx(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=_(r,function(t){return ff({name:t,pname:Lx(t)})});return If("form",zx,i,jx,o)}},Wx=qe("valid-input"),Gx=qe("invalid-input"),Xx=qe("validating-input"),Yx="colorcustom.rgb.",qx=function(m,g,p,h){var v=function(t,n,e,o,r){var i,u,a=m(Yx+"range"),c=[Yh.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),Yh.parts().field({data:r,factory:Nv,inputAttributes:Je({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[g("textfield")],inputBehaviours:zu([(i=n,u=t,Qv.config({invalidClass:g("invalid"),notify:{onValidate:function(t){Dr(t,Xx,{type:i})},onValid:function(t){Dr(t,Wx,{type:i,value:lm.getValue(t)})},onInvalid:function(t){Dr(t,Gx,{type:i,value:lm.getValue(t)})}},validator:{validate:function(t){var n=lm.getValue(t),e=u(n)?mt.value(!0):mt.error(m("aria.input.invalid"));return iv.pure(e)},validateOnLoad:!1}})),nb.config({})]),onSetValue:function(t){Qv.isInvalid(t)&&Qv.run(t).get(Q)}})],s="hex"!==n?[Yh.parts()["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}},b=function(t,n){var e=n.red(),o=n.green(),r=n.blue();lm.setValue(t,{red:e,green:o,blue:r})},y=tg({dom:{tag:"div",classes:[g("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),x=function(t,n){y.getOpt(t).each(function(t){Ki(t.element(),"background-color","#"+n.value())})};return Nf({factory:function(t){var e={red:Z(Tt(tt.some(255))),green:Z(Tt(tt.some(255))),blue:Z(Tt(tt.some(255))),hex:Z(Tt(tt.some("ffffff")))},o=function(t){return e[t]().get()},i=function(t,n){e[t]().set(n)},r=function(t){var n=t.red(),e=t.green(),o=t.blue();i("red",tt.some(n)),i("green",tt.some(e)),i("blue",tt.some(o))},n=function(t,n){var e=n.event();"hex"!==e.type()?i(e.type(),tt.none()):h(t)},u=function(r,t,n){var e=parseInt(n,10);i(t,tt.some(e)),o("red").bind(function(e){return o("green").bind(function(n){return o("blue").map(function(t){return gb(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=cb(t),Ux.getField(n,"hex").each(function(t){hm.isFocused(t)||lm.setValue(n,{hex:e.value()})}),e);x(r,o)})},a=function(t,n){var e=n.event();"hex"===e.type()?function(t,n){p(t);var e=eb(n);i("hex",tt.some(n));var o=vb(e);b(t,o),r(o),Dr(t,Vx(),{hex:e}),x(t,e)}(t,e.value()):u(t,e.type(),e.value())},c=function(t){return{label:m(Yx+t+".label"),description:m(Yx+t+".description")}},s=c("red"),f=c("green"),l=c("blue"),d=c("hex");return vt(Ux.sketch(function(t){return{dom:{tag:"form",classes:[g("rgb-form")],attributes:{"aria-label":m("aria.color.picker")}},components:[t.field("red",Yh.sketch(v(pb,"red",s.label,s.description,255))),t.field("green",Yh.sketch(v(pb,"green",f.label,f.description,255))),t.field("blue",Yh.sketch(v(pb,"blue",l.label,l.description,255))),t.field("hex",Yh.sketch(v(ib,"hex",d.label,d.description,"ffffff"))),y.asSpec()],formBehaviours:zu([Qv.config({invalidClass:g("form-invalid")}),Rm("rgb-form-events",[Kr(Wx,a),Kr(Gx,n),Kr(Xx,n)])])}}),{apis:{updateHex:function(t,n){var e,o;lm.setValue(t,{hex:n.value()}),e=t,o=vb(n),b(e,o),r(o),x(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})},Kx=function(t,n,e){return{hue:Z(t),saturation:Z(n),value:Z(e)}},Jx=function(c,s){return Nf({name:"ColourPicker",configFields:[vr("onValidHex",Q),vr("onInvalidHex",Q),gr("formChangeEvent")],factory:function(t){var a,v,e=qx(c,s,t.onValidHex,t.onInvalidHex),o=Nx(c,s),b={paletteRgba:Z(Tt(xb()))},n=tg(o.sketch({})),r=tg(e.sketch({})),i=function(t,e){n.getOpt(t).each(function(t){var n=vb(e);b.paletteRgba().set(n),o.setRgba(t,n)})},u=function(t,n){r.getOpt(t).each(function(t){e.updateHex(t,n)})},y=function(n,e,t){M(t,function(t){t(n,e)})};return{uid:t.uid,dom:t.dom,components:[n.asSpec(),Px(c,s),r.asSpec()],behaviours:zu([Rm("colour-picker-events",[Kr(Hx(),(v=[u],function(t,n){var e,o,r,i,u,a,c,s,f,l=n.event().value(),d=b.paletteRgba().get(),m=(c=u=0,o=(e=d).red()/255,r=e.green()/255,i=e.blue()/255,(s=Math.min(o,Math.min(r,i)))===(f=Math.max(o,Math.max(r,i)))?Kx(0,0,100*(c=s)):(u=60*((u=o===s?3:i===s?1:5)-(o===s?r-i:i===s?o-r:i-o)/(f-s)),a=(f-s)/f,c=f,Kx(Math.round(u),Math.round(100*a),Math.round(100*c)))),g=Kx(m.hue(),l.x(),100-l.y()),p=hb(g),h=cb(p);y(t,h,v)})),Kr(Rx(),(a=[i,u],function(t,n){var e,o,r,i=n.event().value(),u=(e=i.y(),o=Kx(360*(100-e/100),100,100),r=hb(o),cb(r));y(t,u,a)}))]),Uf.config({find:function(t){return r.getOpt(t)}}),Yd.config({mode:"acyclic"})])}}})},$x=function(){return Uf.config({find:tt.some})},Qx=function(t){return Uf.config({find:t.getOpt})},Zx=function(t){return Uf.config({find:function(n){return ge(n.element(),t).bind(function(t){return n.getSystem().getByDom(t).toOption()})}})},tw={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},nw=function(t){return tw[t]},ew=Io([vr("preprocess",d),vr("postprocess",d)]),ow=function(t,n,e){return lm.config(vt({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))},rw=function(t,n,e){return ow(t,function(t){return n(t.element())},function(t,n){return e(t.element(),n)})},iw=function(r,t){var i=qo("RepresentingConfigs.memento processors",ew,t);return lm.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=lm.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);lm.setValue(o,e)}}})},uw=ow,aw=function(t){return rw(t,Se,Ce)},cw=function(t){return lm.config({store:{mode:"memory",initialValue:t}})},sw=function(r,n){var e=function(t,n){n.stop()},o=function(t){return function(n,e){M(t,function(t){t(n,e)})}},i=function(t,n){if(!mp.isDisabled(t)){var e=n.event().raw();a(t,e.dataTransfer.files)}},u=function(t,n){var e=n.event().raw().target.files;a(t,e)},a=function(t,n){var e,o;lm.setValue(t,(e=n,o=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i"),F(K(e),function(t){return o.test(t.name)}))),Dr(t,Rh,{name:r.name})},c=tg({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:zu([Rm("input-file-events",[ti(Wt())])])}),t=r.label.map(function(t){return _v(t,n)}),s=Yh.parts().field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:zu([cw([]),$x(),mp.config({}),Am.config({toggleClass:"dragenter",toggleOnExecute:!1}),Rm("dropzone-events",[Kr("dragenter",o([e,Am.toggle])),Kr("dragleave",o([e,Am.toggle])),Kr("dragover",e),Kr("drop",o([e,i])),Kr(Ut(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:n.translate("Drop an image here")}},Zm.sketch({dom:{tag:"button",innerHtml:n.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element().dom().click()},buttonBehaviours:zu([nb.config({})])})]}]}}}});return Tv(t,s,["tox-form__group--stretched"])},fw=qe("alloy-fake-before-tabstop"),lw=qe("alloy-fake-after-tabstop"),dw=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:zu([hm.config({ignore:!0}),nb.config({})])}},mw=function(t,n){Dr(t,zt(),{raw:{which:9,shiftKey:n}})},gw=function(t){return sh(t,["."+fw,"."+lw].join(","),Z(!1))},pw=function(t,n){var e=n.element();Ui(e,fw)?mw(t,!0):Ui(e,lw)&&mw(t,!1)},hw=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[dw([fw]),t,dw([lw])],behaviours:zu([Zx(1)])}},vw=!(xn.detect().browser.isIE()||xn.detect().browser.isEdge()),bw=function(t,n){var o,r,e=vw&&t.sandboxed,i=Je({},t.label.map(function(t){return{title:t}}).getOr({}),e?{sandbox:"allow-scripts"}:{}),u=(o=e,r=Tt(""),{getValue:function(t){return r.get()},setValue:function(t,n){if(o)De(t.element(),"src","data:text/html;charset=utf-8,"+encodeURIComponent(n));else{De(t.element(),"src","javascript:''");var e=t.element().dom().contentWindow.document;e.open(),e.write(n),e.close()}r.set(n)}}),a=t.label.map(function(t){return _v(t,n)}),c=Yh.parts().field({factory:{sketch:function(t){return hw({uid:t.uid,dom:{tag:"iframe",attributes:i},behaviours:zu([nb.config({}),hm.config({}),uw(tt.none(),u.getValue,u.setValue)])})}}});return Tv(a,c,["tox-form__group--stretched"])};function yw(t,n){return ww(h.document.createElement("canvas"),t,n)}function xw(t){return t.getContext("2d")}function ww(t,n,e){return t.width=n,t.height=e,t}var Sw={create:yw,clone:function zA(t){var n;return xw(n=yw(t.width,t.height)).drawImage(t,0,0),n},resize:ww,get2dContext:xw,get3dContext:function LA(t){var n=null;try{n=t.getContext("webgl")||t.getContext("experimental-webgl")}catch(e){}return n||(n=null),n}},Cw={getWidth:function jA(t){return t.naturalWidth||t.width},getHeight:function UA(t){return t.naturalHeight||t.height}},kw=window.Promise?window.Promise:function(){var t=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(t,o(r,this),o(u,this))},n=t.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(t){h.setTimeout(t,1)};function o(t,n){return function(){t.apply(n,arguments)}}var e=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function i(o){var r=this;null!==this._state?n(function(){var t=r._state?o.onFulfilled:o.onRejected;if(null!==t){var n;try{n=t(r._value)}catch(e){return void o.reject(e)}o.resolve(n)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function r(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void s(o(n,t),o(r,this),o(u,this))}this._state=!0,this._value=t,a.call(this)}catch(e){u.call(this,e)}}function u(t){this._state=!1,this._value=t,a.call(this)}function a(){for(var t=0,n=this._deferreds.length;t<n;t++)i.call(this,this._deferreds[t]);this._deferreds=null}function c(t,n,e,o){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.resolve=e,this.reject=o}function s(t,n,e){var o=!1;try{t(function(t){o||(o=!0,n(t))},function(t){o||(o=!0,e(t))})}catch(r){if(o)return;o=!0,e(r)}}return t.prototype["catch"]=function(t){return this.then(null,t)},t.prototype.then=function(e,o){var r=this;return new t(function(t,n){i.call(r,new c(e,o,t,n))})},t.all=function(){var c=Array.prototype.slice.call(1===arguments.length&&e(arguments[0])?arguments[0]:arguments);return new t(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}for(var t=0;t<c.length;t++)a(t,c[t])})},t.resolve=function(n){return n&&"object"==typeof n&&n.constructor===t?n:new t(function(t){t(n)})},t.reject=function(e){return new t(function(t,n){n(e)})},t.race=function(r){return new t(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},t}();function Ow(){return new(Zn.getOrDie("FileReader"))}var Ew={atob:function(t){return Zn.getOrDie("atob")(t)},requestAnimationFrame:function(t){Zn.getOrDie("requestAnimationFrame")(t)}};function Tw(a){return new kw(function(t,n){var e=h.URL.createObjectURL(a),o=new h.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),t(o)}function u(){r(),n("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}function Bw(o){return new kw(function(t,e){var n=new h.XMLHttpRequest;n.open("GET",o,!0),n.responseType="blob",n.onload=function(){200==this.status&&t(this.response)},n.onerror=function(){var t,n=this;e(0===this.status?((t=new Error("No access to download image")).code=18,t.name="SecurityError",t):new Error("Error "+n.status+" downloading image"))},n.send()})}function Aw(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return tt.none();for(var o,r=e[1],i=n[1],u=Ew.atob(i),a=u.length,c=Math.ceil(a/1024),s=new Array(c),f=0;f<c;++f){for(var l=1024*f,d=Math.min(l+1024,a),m=new Array(d-l),g=l,p=0;g<d;++p,++g)m[p]=u[g].charCodeAt(0);s[f]=(o=m,new(Zn.getOrDie("Uint8Array"))(o))}return tt.some(function h(t,n){return new(Zn.getOrDie("Blob"))(t,n)}(s,{type:r}))}function Dw(e){return new kw(function(t,n){Aw(e).fold(function(){n("uri is not base64: "+e)},t)})}function _w(e){return new kw(function(t){var n=Ow();n.onloadend=function(){t(n.result)},n.readAsDataURL(e)})}var Mw={blobToImage:Tw,imageToBlob:function WA(t){var n=t.src;return 0===n.indexOf("data:")?Dw(n):Bw(n)},blobToArrayBuffer:function GA(e){return new kw(function(t){var n=Ow();n.onloadend=function(){t(n.result)},n.readAsArrayBuffer(e)})},blobToDataUri:_w,blobToBase64:function XA(t){return _w(t).then(function(t){return t.split(",")[1]})},dataUriToBlobSync:Aw,canvasToBlob:function YA(t,e,o){return e=e||"image/png",h.HTMLCanvasElement.prototype.toBlob?new kw(function(n){t.toBlob(function(t){n(t)},e,o)}):Dw(t.toDataURL(e,o))},canvasToDataURL:function qA(t,n,e){return n=n||"image/png",t.then(function(t){return t.toDataURL(n,e)})},blobToCanvas:function KA(t){return Tw(t).then(function(t){var n;return function e(t){h.URL.revokeObjectURL(t.src)}(t),n=Sw.create(Cw.getWidth(t),Cw.getHeight(t)),Sw.get2dContext(n).drawImage(t,0,0),n})},uriToBlob:function JA(t){return 0===t.indexOf("blob:")?Bw(t):0===t.indexOf("data:")?Dw(t):null}};function Fw(t,n,e){var o=n.type;function r(n,e){return t.then(function(t){return Mw.canvasToDataURL(t,n,e)})}return{getType:Z(o),toBlob:function i(){return kw.resolve(n)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(n,e){return t.then(function(t){return Mw.canvasToBlob(t,n,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(t,n){return r(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function f(){return t.then(Sw.clone)}}}function Iw(n){return Mw.blobToDataUri(n).then(function(t){return Fw(Mw.blobToCanvas(n),n,t)})}var Vw={fromBlob:Iw,fromCanvas:function $A(n,t){return Mw.canvasToBlob(n,t).then(function(t){return Fw(kw.resolve(n),t,n.toDataURL())})},fromImage:function QA(t){return Mw.imageToBlob(t).then(function(t){return Iw(t)})},fromBlobAndUrlSync:function(t,n){return Fw(Mw.blobToCanvas(t),t,n)}},Rw=function(t){return Vw.fromBlob(t)};function Hw(t,n,e){return e<(t=parseFloat(t))?t=e:t<n&&(t=n),t}var Nw=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function Pw(t,n){var e,o,r,i,u=[],a=new Array(10);for(e=0;e<5;e++){for(o=0;o<5;o++)u[o]=n[o+5*e];for(o=0;o<5;o++){for(r=i=0;r<5;r++)i+=t[o+5*r]*u[r];a[o+5*e]=i}}return a}function zw(t,e){return e=Hw(e,0,1),t.map(function(t,n){return n%6==0?t=1-(1-t)*e:t*=e,Hw(t,0,1)})}var Lw={identity:function ZA(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]},adjust:zw,multiply:Pw,adjustContrast:function tD(t,n){var e;return n=Hw(n,-1,1),Pw(t,[(e=(n*=100)<0?127+n/100*127:127*(e=0==(e=n%1)?Nw[n]:Nw[Math.floor(n)]*(1-e)+Nw[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])},adjustBrightness:function nD(t,n){return Pw(t,[1,0,0,0,n=Hw(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])},adjustSaturation:function eD(t,n){var e;return Pw(t,[.3086*(1-(e=1+(0<(n=Hw(n,-1,1))?3*n:n)))+e,.6094*(1-e),.082*(1-e),0,0,.3086*(1-e),.6094*(1-e)+e,.082*(1-e),0,0,.3086*(1-e),.6094*(1-e),.082*(1-e)+e,0,0,0,0,0,1,0,0,0,0,0,1])},adjustHue:function oD(t,n){var e,o;return n=Hw(n,-180,180)/180*Math.PI,Pw(t,[.213+.787*(e=Math.cos(n))+-.213*(o=Math.sin(n)),.715+-.715*e+-.715*o,.072+-.072*e+.928*o,0,0,.213+-.213*e+.143*o,.715+e*(1-.715)+.14*o,.072+-.072*e+-.283*o,0,0,.213+-.213*e+-.787*o,.715+-.715*e+.715*o,.072+.928*e+.072*o,0,0,0,0,0,1,0,0,0,0,0,1])},adjustColors:function rD(t,n,e,o){return Pw(t,[n=Hw(n,0,2),0,0,0,0,0,e=Hw(e,0,2),0,0,0,0,0,o=Hw(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])},adjustSepia:function iD(t,n){return Pw(t,zw([.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0,0,0,0,0,1],n=Hw(n,0,1)))},adjustGrayscale:function uD(t,n){return Pw(t,zw([.33,.34,.33,0,0,.33,.34,.33,0,0,.33,.34,.33,0,0,0,0,0,1,0,0,0,0,0,1],n=Hw(n,0,1)))}};function jw(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o,r=Sw.get2dContext(t);return o=function B(t,n){var e,o,r,i,u,a=t.data,c=n[0],s=n[1],f=n[2],l=n[3],d=n[4],m=n[5],g=n[6],p=n[7],h=n[8],v=n[9],b=n[10],y=n[11],x=n[12],w=n[13],S=n[14],C=n[15],k=n[16],O=n[17],E=n[18],T=n[19];for(u=0;u<a.length;u+=4)e=a[u],o=a[u+1],r=a[u+2],i=a[u+3],a[u]=e*c+o*s+r*f+i*l+d,a[u+1]=e*m+o*g+r*p+i*h+v,a[u+2]=e*b+o*y+r*x+i*w+S,a[u+3]=e*C+o*k+r*O+i*E+T;return t}(r.getImageData(0,0,t.width,t.height),e),r.putImageData(o,0,0),Vw.fromCanvas(t,n)}(t,n.getType(),e)})}function Uw(n,e){return n.toCanvas().then(function(t){return function u(t,n,e){var o,r,i=Sw.get2dContext(t);return o=i.getImageData(0,0,t.width,t.height),r=i.getImageData(0,0,t.width,t.height),r=function w(t,n,e){var o,r,i,u,a,c,s,f,l,d,m,g,p,h,v,b,y;function x(t,n,e){return e<t?t=e:t<n&&(t=n),t}for(i=Math.round(Math.sqrt(e.length)),u=Math.floor(i/2),o=t.data,r=n.data,b=t.width,y=t.height,c=0;c<y;c++)for(a=0;a<b;a++){for(s=f=l=0,m=0;m<i;m++)for(d=0;d<i;d++)g=x(a+d-u,0,b-1),p=x(c+m-u,0,y-1),h=4*(p*b+g),v=e[m*i+d],s+=o[h]*v,f+=o[h+1]*v,l+=o[h+2]*v;r[h=4*(c*b+a)]=x(s,0,255),r[h+1]=x(f,0,255),r[h+2]=x(l,0,255)}return n}(o,r,e),i.putImageData(r,0,0),Vw.fromCanvas(t,n)}(t,n.getType(),e)})}function Ww(c){return function(n,e){return n.toCanvas().then(function(t){return function(t,n,e){var o,r,i=Sw.get2dContext(t),u=new Array(256);for(r=0;r<u.length;r++)u[r]=c(r,e);return o=function a(t,n){var e,o=t.data;for(e=0;e<o.length;e+=4)o[e]=n[o[e]],o[e+1]=n[o[e+1]],o[e+2]=n[o[e+2]];return t}(i.getImageData(0,0,t.width,t.height),u),i.putImageData(o,0,0),Vw.fromCanvas(t,n)}(t,n.getType(),e)})}}function Gw(e){return function(t,n){return jw(t,e(Lw.identity(),n))}}function Xw(n){return function(t){return Uw(t,n)}}var Yw={invert:function aD(n){return function(t){return jw(t,n)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0]),brightness:Gw(Lw.adjustBrightness),hue:Gw(Lw.adjustHue),saturate:Gw(Lw.adjustSaturation),contrast:Gw(Lw.adjustContrast),grayscale:Gw(Lw.adjustGrayscale),sepia:Gw(Lw.adjustSepia),colorize:function(t,n,e,o){return jw(t,Lw.adjustColors(Lw.identity(),n,e,o))},sharpen:Xw([0,-1,0,-1,5,-1,0,-1,0]),emboss:Xw([-2,-1,0,-1,1,1,0,1,2]),gamma:Ww(function(t,n){return 255*Math.pow(t/255,1-n)}),exposure:Ww(function(t,n){return 255*(1-Math.exp(-t/255*n))}),colorFilter:jw,convoluteFilter:Uw},qw={scale:function cD(t,n,e){var o=Cw.getWidth(t),r=Cw.getHeight(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function f(a,c,s){return new kw(function(t){var n=Cw.getWidth(a),e=Cw.getHeight(a),o=Math.floor(n*c),r=Math.floor(e*s),i=Sw.create(o,r),u=Sw.get2dContext(i);u.drawImage(a,0,0,n,e,0,0,o,r),t(i)})}(t,i,u);return a?c.then(function(t){return cD(t,n,e)}):c}},Kw={rotate:function sD(n,e){return n.toCanvas().then(function(t){return function a(t,n,e){var o=Sw.create(t.width,t.height),r=Sw.get2dContext(o),i=0,u=0;return 90!=(e=e<0?360+e:e)&&270!=e||Sw.resize(o,o.height,o.width),90!=e&&180!=e||(i=o.width),270!=e&&180!=e||(u=o.height),r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(t,0,0),Vw.fromCanvas(o,n)}(t,n.getType(),e)})},flip:function fD(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o=Sw.create(t.width,t.height),r=Sw.get2dContext(o);return"v"==e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),Vw.fromCanvas(o,n)}(t,n.getType(),e)})},crop:function lD(n,e,o,r,i){return n.toCanvas().then(function(t){return function a(t,n,e,o,r,i){var u=Sw.create(r,i);return Sw.get2dContext(u).drawImage(t,-e,-o),Vw.fromCanvas(u,n)}(t,n.getType(),e,o,r,i)})},resize:function dD(n,e,o){return n.toCanvas().then(function(t){return qw.scale(t,e,o).then(function(t){return Vw.fromCanvas(t,n.getType())})})}},Jw=(function(){function t(t){this.littleEndian=!1,this._dv=new DataView(t)}t.prototype.readByteAt=function(t){return this._dv.getUint8(t)},t.prototype.read=function(t,n){if(t+n>this.length())return null;for(var e=this.littleEndian?0:-8*(n-1),o=0,r=0;o<n;o++)r|=this.readByteAt(t+o)<<Math.abs(e+8*o);return r},t.prototype.BYTE=function(t){return this.read(t,1)},t.prototype.SHORT=function(t){return this.read(t,2)},t.prototype.LONG=function(t){return this.read(t,4)},t.prototype.SLONG=function(t){var n=this.read(t,4);return 2147483647<n?n-4294967296:n},t.prototype.CHAR=function(t){return String.fromCharCode(this.read(t,1))},t.prototype.STRING=function(t,n){return this.asArray("CHAR",t,n).join("")},t.prototype.SEGMENT=function(t,n){var e=this._dv.buffer;switch(arguments.length){case 2:return e.slice(t,t+n);case 1:return e.slice(t);default:return e}},t.prototype.asArray=function(t,n,e){for(var o=[],r=0;r<e;r++)o[r]=this[t](n+r);return o},t.prototype.length=function(){return this._dv?this._dv.byteLength:0}}(),function(t,n){return Kw.rotate(t,n)}),$w=function(t){return Yw.invert(t)},Qw=function(t){return Yw.sharpen(t)},Zw=function(t,n){return Yw.brightness(t,n)},tS=function(t,n){return Yw.contrast(t,n)},nS=function(t,n,e,o){return Yw.colorize(t,n,e,o)},eS=function(t,n){return Yw.gamma(t,n)},oS=function(t,n){return Kw.flip(t,n)},rS=function(t,n,e,o,r){return Kw.crop(t,n,e,o,r)},iS=function(t,n,e){return Kw.resize(t,n,e)},uS=Jw,aS=function(t,n){return Je({dom:{tag:"span",innerHtml:t,classes:["tox-icon","tox-tbtn__icon-wrap"]}},n)},cS=function(t,n){return aS(eg(t,n),{})},sS=function(t,n){return aS(eg(t,n),{behaviours:zu([Qd.config({})])})},fS=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:zu([Qd.config({})])}},lS=function(t,n,e,o,r){void 0===e&&(e=[]);var i=n.fold(function(){return{}},function(t){return{action:t}}),u=Je({buttonBehaviours:zu([pp(t.disabled),nb.config({}),Rm("button press",[qr("click"),qr("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=vt(u,{dom:o});return vt(a,{components:r})},dS=function(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return cS(t,e.icons)}),u=kp([i]);return lS(t,n,o,r,u)},mS=function(t,n,e,o){void 0===o&&(o=[]);var r=dS(t,tt.some(n),e,o);return Zm.sketch(r)},gS=function(t,n,e,o){void 0===o&&(o=[]);var r=e.translate(t.text),i=t.icon?t.icon.map(function(t){return cS(t,e.icons)}):tt.none(),u=i.isSome()?kp([i]):[],a=i.isSome()?{}:{innerHtml:r},c=(t.primary?["tox-button"]:["tox-button","tox-button--secondary"]).concat(i.isSome()?["tox-button--icon"]:[]);return function(t,n,e,o,r){void 0===e&&(e=[]);var i=lS(t,tt.some(n),e,o,r);return Zm.sketch(i)}(t,n,o,Je({tag:"button",classes:c},a,{attributes:{title:r}}),u)},pS=function(n,e){return function(t){"custom"===e?Dr(t,Ph,{name:n,value:{}}):"submit"===e?Ar(t,zh):"cancel"===e?Ar(t,Nh):console.error("Unknown button type: ",e)}},hS=function(t,n,e){var o=pS(t.name,n);return gS(t,o,e,[])},vS=Z([vr("field1Name","field1"),vr("field2Name","field2"),Du("onLockedChange"),Eu(["lockClass"]),vr("locked",!1),Ns("coupledFieldBehaviours",[Uf,lm])]),bS=function(t,i){return ff({factory:Yh,name:t,overrides:function(r){return{fieldBehaviours:zu([Rm("coupled-input-behaviour",[Kr(jt(),function(e){var t,n,o;(t=e,n=r,o=i,kf(t,n,o).bind(Uf.getCurrent)).each(function(n){kf(e,r,"lock").each(function(t){Am.isOn(t)&&r.onLockedChange(e,n,t)})})})])])}}})},yS=Z([bS("field1","field2"),bS("field2","field1"),ff({factory:Zm,schema:[rr("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:zu([Am.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),xS=Pf({name:"FormCoupledInputs",configFields:vS(),partFields:yS(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Ps(o.coupledFieldBehaviours,[Uf.config({find:tt.some}),lm.config({store:{mode:"manual",getValue:function(t){var n,e=Af(t,o,["field1","field2"]);return(n={})[o.field1Name]=lm.getValue(e.field1()),n[o.field2Name]=lm.getValue(e.field2()),n},setValue:function(t,n){var e=Af(t,o,["field1","field2"]);Et(n,o.field1Name)&&lm.setValue(e.field1(),n[o.field1Name]),Et(n,o.field2Name)&&lm.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return kf(t,o,"field1")},getField2:function(t){return kf(t,o,"field2")},getLock:function(t){return kf(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),wS=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return mt.error(t);var e=parseFloat(n[1]),o=n[2];return mt.value({value:e,unit:o})},SS=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1},o=function(t){return Object.prototype.hasOwnProperty.call(e,t)};return t.unit===n?tt.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?tt.some(t.value):tt.some(t.value/e[t.unit]*e[n]):tt.none()},CS=function(t){return tt.none()},kS=function(t,n){return function(t,n){for(var e=[],o=0;o<t.length;o++){var r=t[o];if(!r.isSome())return tt.none();e.push(r.getOrDie())}return tt.some(n.apply(null,e))}([wS(t).toOption(),wS(n).toOption()],function(t,o){return SS(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return SS(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(CS)}).getOr(CS)},OS=function(o,n){var a=CS,r=qe("ratio-event"),t=xS.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:eg("lock",n.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:eg("unlock",n.icons)}}],buttonBehaviours:zu([nb.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},i=function(e){return Yh.parts().field({factory:Nv,inputClasses:["tox-textfield"],inputBehaviours:zu([nb.config({}),Rm("size-input-events",[Kr(Nt(),function(t,n){Dr(t,r,{isField1:e})}),Kr(Ut(),function(t,n){Dr(t,Rh,{name:o.name})})])]),selectOnFocus:!1})},u=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},c=xS.parts().field1(e([Yh.parts().label(u("Width")),i(!0)])),s=xS.parts().field2(e([Yh.parts().label(u("Height")),i(!1)]));return xS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([u("&nbsp;"),t])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){wS(lm.getValue(t)).each(function(t){a(t).each(function(t){var n,e,o,r;lm.setValue(i,(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},-1!==(r=(n=t).value.toFixed((e=n.unit)in o?o[e]:1)).indexOf(".")&&(r=r.replace(/\.?0*$/,"")),r+n.unit))})})},coupledFieldBehaviours:zu([mp.config({}),Rm("size-input-events2",[Kr(r,function(t,n){var e=n.event().isField1(),o=e?xS.getField1(t):xS.getField2(t),r=e?xS.getField2(t):xS.getField1(t),i=o.map(lm.getValue).getOr(""),u=r.map(lm.getValue).getOr("");a=kS(i,u)})])])})},ES={undo:Z(qe("undo")),redo:Z(qe("redo")),zoom:Z(qe("zoom")),back:Z(qe("back")),apply:Z(qe("apply")),swap:Z(qe("swap")),transform:Z(qe("transform")),tempTransform:Z(qe("temp-transform")),transformApply:Z(qe("transform-apply"))},TS=Z("save-state"),BS=Z("disable"),AS=Z("enable"),DS={formActionEvent:Ph,saveState:TS,disable:BS,enable:AS},_S=function(r,c){var t=function(t,n,e,o){return tg(gS({name:t,text:t,disabled:e,primary:o},n,c))},n=function(t,n,e,o){return tg(mS({name:t,icon:tt.some(t),tooltip:tt.some(n),disabled:o},e,c))},u=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(mp)&&mp.disable(n)})},a=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(mp)&&mp.enable(n)})},s={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},e=tt.none(),o=Q,i=function(t,n,e){Dr(t,n,e)},f=function(t){return Ar(t,DS.disable())},l=function(t){return Ar(t,DS.enable())},d=function(t,n){f(t),i(t,ES.transform(),{transform:n}),l(t)},m=function(t){return function(){$.getOpt(t).each(function(t){Qd.set(t,[K])})}},g=function(t,n){f(t),i(t,ES.transformApply(),{transform:n,swap:m(t)}),l(t)},p=function(){return t("Back",function(t){return i(t,ES.back(),{swap:m(t)})},!1,!1)},h=function(){return tg({dom:{tag:"div",classes:["tox-spacer"]},behaviours:zu([mp.config({})])})},v=function(){return t("Apply",function(t){return i(t,ES.apply(),{swap:m(t)})},!0,!0)},b=[p(),h(),t("Apply",function(t){g(t,function(t){var n=r.getRect();return rS(t,n.x,n.y,n.w,n.h)}),r.hideCrop()},!1,!0)],y=Bh.sketch({dom:s,components:b.map(function(t){return t.asSpec()}),containerBehaviours:zu([Rm("image-tools-crop-buttons-events",[Kr(DS.disable(),function(t,n){u(b,t)}),Kr(DS.enable(),function(t,n){a(b,t)})])])}),x=tg(OS({name:"size",label:e,type:"sizeinput",constrain:!0},c)),w=[p(),h(),x,h(),t("Apply",function(a){x.getOpt(a).each(function(t){var n,e,o=lm.getValue(t),r=parseInt(o.width,10),i=parseInt(o.height,10),u=(n=r,e=i,function(t){return iS(t,n,e)});g(a,u)})},!1,!0)],S=Bh.sketch({dom:s,components:w.map(function(t){return t.asSpec()}),containerBehaviours:zu([Rm("image-tools-resize-buttons-events",[Kr(DS.disable(),function(t,n){u(w,t)}),Kr(DS.enable(),function(t,n){a(w,t)})])])}),C=function(n,e){return function(t){return n(t,e)}},k=C(oS,"h"),O=C(oS,"v"),E=C(uS,-90),T=C(uS,90),B=function(t,n){var e,o;o=n,f(e=t),i(e,ES.tempTransform(),{transform:o}),l(e)},A=[p(),h(),n("flip-horizontally","Flip horizontally",function(t){B(t,k)},!1),n("flip-vertically","Flip vertically",function(t){B(t,O)},!1),n("rotate-left","Rotate counterclockwise",function(t){B(t,E)},!1),n("rotate-right","Rotate clockwise",function(t){B(t,T)},!1),h(),v()],D=Bh.sketch({dom:s,components:A.map(function(t){return t.asSpec()}),containerBehaviours:zu([Rm("image-tools-fliprotate-buttons-events",[Kr(DS.disable(),function(t,n){u(A,t)}),Kr(DS.enable(),function(t,n){a(A,t)})])])}),_=function(t,n,e,o,r){var i=Ix.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=Ix.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=Ix.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return tg(Ix.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:Z({x:Z(o)})},components:[i,u,a],sliderBehaviours:zu([hm.config({})]),onChoose:n}))},M=function(t,n,e,o,r){return[p(),(i=t,u=n,a=e,c=o,s=r,_(i,function(t,n,e){var o=C(u,e.x()/100);d(t,o)},a,c,s)),v()];var i,u,a,c,s},F=function(t,n,e,o,r){var i=M(t,n,e,o,r);return Bh.sketch({dom:s,components:i.map(function(t){return t.asSpec()}),containerBehaviours:zu([Rm("image-tools-filter-panel-buttons-events",[Kr(DS.disable(),function(t,n){u(i,t)}),Kr(DS.enable(),function(t,n){a(i,t)})])])})},I=[p(),h(),v()],V=Bh.sketch({dom:s,components:I.map(function(t){return t.asSpec()})}),R=F("Brightness",Zw,-100,0,100),H=F("Contrast",tS,-100,0,100),N=F("Gamma",eS,-100,0,100),P=function(t){return _(t,function(f,t,n){var e=z.getOpt(f),o=j.getOpt(f),r=L.getOpt(f);e.each(function(s){o.each(function(c){r.each(function(t){var n,e,o,r=lm.getValue(s).x()/100,i=lm.getValue(t).x()/100,u=lm.getValue(c).x()/100,a=(n=r,e=i,o=u,function(t){return nS(t,n,e,o)});d(f,a)})})})},0,100,200)},z=P("R"),L=P("G"),j=P("B"),U=[p(),z,L,j,v()],W=Bh.sketch({dom:s,components:U.map(function(t){return t.asSpec()})}),G=function(n,e,o){return function(t){i(t,ES.swap(),{transform:e,swap:function(){$.getOpt(t).each(function(t){Qd.set(t,[n]),o(t)})}})}},X=tt.some(Qw),Y=tt.some($w),q=[n("crop","Crop",G(y,e,function(t){r.showCrop()}),!1),n("resize","Resize",G(S,e,function(t){x.getOpt(t).each(function(t){var n=r.getMeasurements(),e=n.width,o=n.height;lm.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",G(D,e,o),!1),n("brightness","Brightness",G(R,e,o),!1),n("sharpen","Sharpen",G(V,X,o),!1),n("contrast","Contrast",G(H,e,o),!1),n("color-levels","Color levels",G(W,e,o),!1),n("gamma","Gamma",G(N,e,o),!1),n("invert","Invert",G(V,Y,o),!1)],K=Bh.sketch({dom:s,components:q.map(function(t){return t.asSpec()})}),J=Bh.sketch({dom:{tag:"div"},components:[K],containerBehaviours:zu([Qd.config({})])}),$=tg(J);return{memContainer:$,getApplyButton:function(t){return $.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}},MS=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),FS=tinymce.util.Tools.resolve("tinymce.geom.Rect"),IS=tinymce.util.Tools.resolve("tinymce.util.Observable"),VS=tinymce.util.Tools.resolve("tinymce.util.Tools"),RS=tinymce.util.Tools.resolve("tinymce.util.VK");function HS(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}function NS(t,r){var i,u,n,a,c,f,l,d=r.document||h.document;r=r||{};var m=d.getElementById(r.handle||t);n=function(t){var n,e,o=function s(t){var n,e,o,r,i,u,a,c=Math.max;return n=t.documentElement,e=t.body,o=c(n.scrollWidth,e.scrollWidth),r=c(n.clientWidth,e.clientWidth),i=c(n.offsetWidth,e.offsetWidth),u=c(n.scrollHeight,e.scrollHeight),a=c(n.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(n.offsetHeight,e.offsetHeight)?a:u}}(d);HS(t),t.preventDefault(),u=t.button,n=m,f=t.screenX,l=t.screenY,e=h.window.getComputedStyle?h.window.getComputedStyle(n,null).getPropertyValue("cursor"):n.runtimeStyle.cursor,i=MS("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),MS(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(t)},c=function(t){if(HS(t),t.button!==u)return a(t);t.deltaX=t.screenX-f,t.deltaY=t.screenY-l,t.preventDefault(),r.drag(t)},a=function(t){HS(t),MS(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(t)},this.destroy=function(){MS(m).off()},MS(m).on("mousedown touchstart",n)}var PS,zS,LS,jS=0,US=function(n){var f=tg({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=Tt(1),d=Tt(tt.none()),m=Tt({x:0,y:0,w:1,h:1}),c=Tt({x:0,y:0,w:1,h:1}),s=function(t,s){g.getOpt(t).each(function(t){var e=l.get(),o=ua(t.element()),r=sa(t.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};Ji(s,n),f.getOpt(t).each(function(t){Ji(t.element(),n)}),d.get().each(function(t){var n=m.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})},e=function(t,n){var e,a=Xn.fromTag("img");return De(a,"src",n),(e=a.dom(),new dg(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)})).then(function(){return g.getOpt(t).map(function(t){var n=du({element:a});Qd.replaceAt(t,1,tt.some(n));var e=c.get(),o={x:0,y:0,w:a.dom().naturalWidth,h:a.dom().naturalHeight};c.set(o);var r,u,i=FS.inflate(o,-20,-20);return m.set(i),e.w===o.w&&e.h===o.h||(r=t,u=a,g.getOpt(r).each(function(t){var n=ua(t.element()),e=sa(t.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(n/o,e/r);1<=i?l.set(1):l.set(i)})),s(t,a),a})})},t=Bh.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:n}}},{dom:{tag:"div"},behaviours:zu([Rm("image-panel-crop-events",[oi(function(t){g.getOpt(t).each(function(t){var n=function S(s,e,f,o,r){var l,u,n,i,a="tox-",c="tox-crid-"+jS++;function d(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}}function m(t,n,e,o){var r,i,u,a,c;r=n.x,i=n.y,u=n.w,a=n.h,r+=e*t.deltaX,i+=o*t.deltaY,(u+=e*t.deltaW)<20&&(u=20),(a+=o*t.deltaH)<20&&(a=20),c=s=FS.clamp({x:r,y:i,w:u,h:a},f,"move"===t.name),c=d(f,c),l.fire("updateRect",{rect:c}),h(c)}function g(n){function t(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),MS("#"+c+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})}VS.each(u,function(t){MS("#"+c+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}function p(t){g(s=t)}function h(t){p(function e(t,n){return{x:n.x+t.x,y:n.y+t.y,w:n.w,h:n.h}}(f,t))}return u=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],i=["top","right","bottom","left"],function v(){MS('<div id="'+c+'" class="'+a+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),VS.each(i,function(t){MS("#"+c,o).append('<div id="'+c+"-"+t+'"class="'+a+'croprect-block" style="display: none" data-mce-bogus="all">')}),VS.each(u,function(t){MS("#"+c,o).append('<div id="'+c+"-"+t.name+'" class="'+a+"croprect-handle "+a+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),n=VS.map(u,function t(n){var e;return new NS(c,{document:o.ownerDocument,handle:c+"-"+n.name,start:function(){e=s},drag:function(t){m(n,e,t.deltaX,t.deltaY)}})}),g(s),MS(o).on("focusin focusout",function(t){MS(t.target).attr("aria-grabbed","focus"===t.type)}),MS(o).on("keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),m(i,e,o,r)}switch(VS.each(u,function(t){if(n.target.id===c+"-"+t.name)return i=t,!1}),n.keyCode){case RS.LEFT:t(n,0,s,-10,0);break;case RS.RIGHT:t(n,0,s,10,0);break;case RS.UP:t(n,0,s,0,-10);break;case RS.DOWN:t(n,0,s,0,10);break;case RS.ENTER:case RS.SPACEBAR:n.preventDefault(),r()}})}(),l=VS.extend({toggleVisibility:function b(t){var n;n=VS.map(u,function(t){return"#"+c+"-"+t.name}).concat(VS.map(i,function(t){return"#"+c+"-"+t})).join(","),t?MS(n,o).show():MS(n,o).hide()},setClampRect:function y(t){f=t,g(s)},setRect:p,getInnerRect:function t(){return d(f,s)},setInnerRect:h,setViewPortRect:function x(t){e=t,g(s)},destroy:function w(){VS.each(n,function(t){t.destroy()}),n=[]}},IS)}({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t.element().dom(),function(){});n.toggleVisibility(!1),n.on("updateRect",function(t){var n=t.rect,e=l.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};m.set(o)}),d.set(tt.some(n))})})])])}],containerBehaviours:zu([Qd.config({}),Rm("image-panel-events",[oi(function(t){e(t,n)})])])}),g=tg(t);return{memContainer:g,updateSrc:e,zoom:function(t,n){var e=l.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),g.getOpt(t).each(function(t){var n=t.components()[1].element();s(t,n)})},showCrop:function(){d.get().each(function(t){t.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(t){t.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var t=c.get();return{width:t.w,height:t.h}}}},WS=function(t,n,e,o,r){return mS({name:t,icon:tt.some(n),disabled:e,tooltip:tt.some(t)},o,r)},GS=function(t,n){n?mp.enable(t):mp.disable(t)},XS=function(){return Zn.getOrDie("URL")},YS=function(t){return XS().createObjectURL(t)},qS=function(t){XS().revokeObjectURL(t)},KS=function(t){var n=Tt(t),e=Tt(tt.none()),r=function s(){var e=[],o=-1;function t(){return 0<o}function n(){return-1!==o&&o<e.length-1}return{data:e,add:function r(t){var n;return n=e.splice(++o),e.push(t),{state:t,removed:n}},undo:function i(){if(t())return e[--o]},redo:function u(){if(n())return e[++o]},canUndo:t,canRedo:n}}();r.add(t);var i=function(t){n.set(t)},u=function(t){return{blob:t,url:YS(t)}},a=function(t){qS(t.url)},o=function(){e.get().each(a),e.set(tt.none())},c=function(t){var n=u(t);i(n);var e,o=r.add(n).removed;return e=o,VS.each(e,a),n.url};return{getBlobState:function(){return n.get()},setBlobState:i,addBlobState:c,getTempState:function(){return e.get().fold(function(){return n.get()},function(t){return t})},updateTempState:function(t){var n=u(t);return o(),e.set(tt.some(n)),n.url},addTempState:function(t){var n=u(t);return e.set(tt.some(n)),n.url},applyTempState:function(n){return e.get().fold(function(){},function(t){c(t.blob),n()})},destroyTempState:o,undo:function(){var t=r.undo();return i(t),t.url},redo:function(){var t=r.redo();return i(t),t.url},getHistoryStates:function(){return{undoEnabled:r.canUndo(),redoEnabled:r.canRedo()}}}},JS=function(t,n){var e,o,r,u=KS(t.currentState),i=function(t){var n=u.getHistoryStates();p.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),Dr(t,DS.formActionEvent,{name:DS.saveState(),value:n.undoEnabled})},a=function(t){return t.toBlob()},c=function(t){Dr(t,DS.formActionEvent,{name:DS.disable(),value:{}})},s=function(t){h.getApplyButton(t).each(function(t){mp.enable(t)}),Dr(t,DS.formActionEvent,{name:DS.enable(),value:{}})},f=function(t,n){return c(t),g.updateSrc(t,n)},l=function(n,t,e,o,r){return c(n),Rw(t).then(e).then(a).then(o).then(function(t){return f(n,t).then(function(t){return i(n),r(),s(n),t})})["catch"](function(t){console.log(t),s(n)})},d=function(t,n,e){var o=u.getBlobState().blob;l(t,o,n,function(t){return u.updateTempState(t)},e)},m=function(t){var n=u.getBlobState().url;return u.destroyTempState(),i(t),n},g=US(t.currentState.url),p=(o=tg(WS("Undo","undo",!0,function(t){Dr(t,ES.undo(),{direction:1})},e=n)),r=tg(WS("Redo","redo",!0,function(t){Dr(t,ES.redo(),{direction:1})},e)),{container:Bh.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),WS("Zoom in","zoom-in",!1,function(t){Dr(t,ES.zoom(),{direction:1})},e),WS("Zoom out","zoom-out",!1,function(t){Dr(t,ES.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){GS(t,n)}),r.getOpt(t).each(function(t){GS(t,e)})}}),h=_S(g,n);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[h.memContainer.asSpec(),g.memContainer.asSpec(),p.container],behaviours:zu([lm.config({store:{mode:"manual",getValue:function(){return u.getBlobState()}}}),Rm("image-tools-events",[Kr(ES.undo(),function(n,t){var e=u.undo();f(n,e).then(function(t){s(n),i(n)})}),Kr(ES.redo(),function(n,t){var e=u.redo();f(n,e).then(function(t){s(n),i(n)})}),Kr(ES.zoom(),function(t,n){var e=n.event().direction();g.zoom(t,e)}),Kr(ES.back(),function(t,n){var e,o;o=m(e=t),f(e,o).then(function(t){s(e)}),n.event().swap()(),g.hideCrop()}),Kr(ES.apply(),function(t,n){u.applyTempState(function(){m(t),n.event().swap()()})}),Kr(ES.transform(),function(t,n){return d(t,n.event().transform(),Q)}),Kr(ES.tempTransform(),function(t,n){return e=t,o=n.event().transform(),r=u.getTempState().blob,void l(e,r,o,function(t){return u.addTempState(t)},Q);var e,o,r}),Kr(ES.transformApply(),function(t,n){return e=t,o=n.event().transform(),r=n.event().swap(),i=u.getBlobState().blob,void l(e,i,o,function(t){var n=u.addBlobState(t);return m(e),n},r);var e,o,r,i}),Kr(ES.swap(),function(n,t){var e;e=n,p.updateButtonUndoStates(e,!1,!1);var o=t.event().transform(),r=t.event().swap();o.fold(function(){r()},function(t){d(n,t,r)})})]),$x()])}},$S=Nf({name:"HtmlSelect",configFields:[rr("options"),Vs("selectBehaviours",[hm,lm]),vr("selectClasses",[]),vr("selectAttributes",{}),dr("data")],factory:function(e,t){var n=_(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return Ct("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:Hs(e.selectBehaviours,[hm.config({}),lm.config({store:Je({mode:"manual",getValue:function(t){return ou(t.element())},setValue:function(t,n){R(e.options,function(t){return t.value===n}).isSome()&&ru(t.element(),n)}},o)})])}}}),QS=function(e,n){var t=e.label.map(function(t){return _v(t,n)}),o=[Yd.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return Ar(t,zh),tt.some(!0)}}),Rm("textfield-change",[Kr(jt(),function(t,n){Dr(t,Rh,{name:e.name})}),Kr(kn(),function(t,n){Dr(t,Rh,{name:e.name})})]),nb.config({})],r=e.validation.map(function(o){return Qv.config({getRoot:function(t){return le(t.element())},invalidClass:"tox-invalid",validator:{validate:function(t){var n=lm.getValue(t),e=o.validator(n);return iv.pure(!0===e?mt.value(n):mt.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=Yh.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:e.placeholder.fold(function(){},function(t){return{placeholder:n.translate(t)}}),inputClasses:[e.classname],inputBehaviours:zu(z([o,r])),selectOnFocus:!1,factory:Nv}),u=e.flex?["tox-form__group--stretched"]:[];return Tv(t,i,u)},ZS=function(i){return Je({},i,{toCached:function(){return ZS(i.toCached())},bindFuture:function(n){return ZS(i.bind(function(t){return t.fold(function(t){return iv.pure(mt.error(t))},function(t){return n(t)})}))},bindResult:function(n){return ZS(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return ZS(i.map(function(t){return t.map(n)}))},mapError:function(n){return ZS(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return ZS(iv.nu(function(n){var e=!1,o=window.setTimeout(function(){e=!0,n(mt.error(r()))},t);i.get(function(t){e||(window.clearTimeout(o),n(t))})}))}})},tC=function(t){return ZS(iv.nu(t))},nC=tC,eC={type:"separator"},oC=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:function(){}}},rC=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:Q},onAction:function(){}}},iC=function(t,n){return o=t,e=F(n,function(t){return t.type===o}),_(e,oC);var e,o},uC=function(t,n){var e=t.toLowerCase();return F(n,function(t){var n=t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.text;return mn(n.toLowerCase(),e)||mn(t.value.toLowerCase(),e)})},aC=function(c,t,s){var n=lm.getValue(t),f=n.meta.text!==undefined?n.meta.text:n.value;return s.getLinkInformation().fold(function(){return[]},function(t){var n,e,o,r,i,u,a=uC(f,(n=s.getHistory(c),_(n,function(t){return rC(t,t)})));return"file"===c?(e=[a,uC(f,(u=t,iC("header",u.targets))),uC(f,z([(i=t,i.anchorTop.map(function(t){return rC("<top>",t)}).toArray()),(r=t,iC("anchor",r.targets)),(o=t,o.anchorBottom.map(function(t){return rC("<bottom>",t)}).toArray())]))],V(e,function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(eC,n)},[])):a})},cC=function(i,u,o){var t,r=function(t){var n=lm.getValue(t);o.addToHistory(n.value,i.filetype)},n=Yh.parts().field({factory:Ev,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],minChars:0,responseTime:0,fetch:function(t){var n=aC(i.filetype,t,o),e=Hv(n,Sp.BUBBLE_TO_SANDBOX,u.providers);return iv.pure(e)},getHotspot:function(t){return l.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Qv)&&Qv.run(t).get(Q)},typeaheadBehaviours:zu(z([o.getValidationHandler().map(function(o){return Qv.config({getRoot:function(t){return le(t.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{},validator:{validate:function(n){var e=lm.getValue(n);return nC(function(t){o({type:i.filetype,url:e.value},function(e){l.getOpt(n).each(function(t){var n=function(t,n,e){(e?zi:ji)(t.element(),n)};n(t,"tox-control-wrap--status-valid","valid"===e.status),n(t,"tox-control-wrap--status-unknown","unknown"===e.status)}),t(("invalid"===e.status?mt.error:mt.value)(e.message))})})},validateOnLoad:!1}})}).toArray(),[nb.config({}),Rm("urlinput-events",z(["file"===i.filetype?[Kr(jt(),function(t){Dr(t,Rh,{name:i.name})})]:[],[Kr(Ut(),function(t){Dr(t,Rh,{name:i.name}),r(t)}),Kr(kn(),function(t){Dr(t,Rh,{name:i.name}),r(t)})]]))]])),eventOrder:(t={},t[jt()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"dog"},lazySink:u.getSink,parts:{menu:jp(0,0,"normal")},onExecute:function(t,n,e){Dr(n,zh,{})},onItemExecute:function(t,n,e,o){r(t),Dr(t,Rh,{name:i.name})}}),e=i.label.map(function(t){return _v(t,u.providers)}),a=function(t,n,e){return void 0===n&&(n=t),void 0===e&&(e=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:eg(n,u.providers.icons),attributes:{title:u.providers.translate(e)}}}},c=tg({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[a("valid","checkmark","valid"),a("unknown","warning"),a("invalid","warning")]}),s=o.getUrlPicker(i.filetype),f=qe("browser.url.event"),l=tg({dom:{tag:"div",classes:["tox-control-wrap"]},components:[n,c.asSpec()]});return Yh.sketch({dom:Dv([]),components:e.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:z([[l.asSpec()],s.map(function(){return t=i.label,n=f,e="tox-browse-url",o="browse",r=u.providers,Zm.sketch({dom:{tag:"button",classes:["tox-tbtn",e],innerHtml:eg(o,r.icons),attributes:{title:r.translate(t.getOr(""))}},buttonBehaviours:zu([nb.config({})]),action:function(t){Ar(t,n)}});var t,n,e,o,r}).toArray()])}]),fieldBehaviours:zu([Rm("url-input-events",[Kr(f,function(o){Uf.getCurrent(o).each(function(n){var e=lm.getValue(n);s.each(function(t){t(e).get(function(t){lm.setValue(n,t),Dr(o,Rh,{name:i.name})})})})})])])})},sC=function(u,n){var t,e,o=u.label.map(function(t){return _v(t,n)}),r=function(e){return function(n,t){bu(t.event().target(),"[data-collection-item-value]").each(function(t){e(n,t,Me(t,"data-collection-item-value"))})}},i=[Kr(Ht(),r(function(t,n){ml(n)})),Kr(Wt(),r(function(t,n,e){Dr(t,Ph,{name:u.name,value:e})})),Kr(Nt(),r(function(t,n,e){vu(t.element(),"."+Gg).each(function(t){ji(t,Gg)}),zi(n,Gg)})),Kr(Pt(),r(function(t,n,e){vu(t.element(),"."+Gg).each(function(t){ji(t,Gg)})})),ui(r(function(t,n,e){Dr(t,Ph,{name:u.name,value:e})}))],a=Yh.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:d},behaviours:zu([Qd.config({}),lm.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){var n,e,r,i;n=o,e=_(t,function(t){var n,e=1===u.columns?t.text.map(function(t){return'<span class="tox-collection__item-label">'+t+"</span>"}).getOr(""):"",o=t.icon.map(function(t){return'<span class="tox-collection__item-icon">'+t+"</span>"}).getOr(""),r={_:" "," - ":" ","-":" "},i=t.text.getOr("").replace(/\_| \- |\-/g,function(t){return r[t]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+('"'===(n=t.value)?"&quot;":n)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),r=1<u.columns&&"auto"!==u.columns?D(e,u.columns):[e],i=_(r,function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"}),Ce(n.element(),i.join("")),"auto"===u.columns&&Og(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;Yd.setGridSize(o,n,e)}),Ar(o,Wh)}}),nb.config({}),Yd.config((t=u.columns,e="normal",1===t?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===t?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===e?".tox-swatches__row":".tox-collection__group",cell:"color"===e?"."+zg:"."+Pg}})),Rm("collection-events",i)])});return Tv(o,a,["tox-form__group--collection"])},fC=function(r){return function(n,e,o){return St(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}},lC={bar:fC(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-bar"]},components:_(e.items,o.interpreter)};var e,o}),collection:fC(function(t,n){return sC(t,n.shared.providers)}),alloy:fC(d),alertbanner:fC(function(t,n){return e=t,o=n.shared.providers,Bh.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+e.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Zm.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:eg(e.icon,o.icons),attributes:{title:o.translate(e.actionLabel)}},action:function(t){Dr(t,Ph,{name:"alert-banner",value:e.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:fC(function(t,n){return e=t,o=n.shared.providers,QS({name:e.name,multiline:!1,label:e.label,placeholder:e.placeholder,flex:!1,classname:"tox-textfield",validation:tt.none()},o);var e,o}),textarea:fC(function(t,n){return e=t,o=n.shared.providers,QS({name:e.name,multiline:!0,label:e.label,placeholder:e.placeholder,flex:!0,classname:"tox-textarea",validation:tt.none()},o);var e,o}),listbox:fC(function(t,n){return e=t,o=n.shared.providers,r=_v(e.label,o),i=Yh.parts().field({factory:$S,dom:{classes:["mce-select-field"]},selectBehaviours:zu([nb.config({})]),options:e.values,data:e.initialValue.getOr(undefined)}),Bv(tt.some(r),i);var e,o,r,i}),label:fC(function(t,n){return e=t,o=n.shared,r={dom:{tag:"label",innerHtml:o.providers.translate(e.label),classes:["tox-label"]}},i=_(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:zu([$x(),Qd.config({}),aw(tt.none()),Yd.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(PS=function(t,n){return bw(t,n.shared.providers)},function(t,n,e){var o=vt(n,{source:"dynamic"});return fC(PS)(t,o,e)}),autocomplete:fC(function(t,n){return r=t,i=n.shared,e=_v(r.label.getOr("?"),i.providers),o=Yh.parts().field({factory:Ev,dismissOnBlur:!1,inputClasses:["tox-textfield"],minChars:1,fetch:function(t){var n=lm.getValue(t),e=r.getItems(n),o=Hv(e,Sp.BUBBLE_TO_SANDBOX,i.providers);return iv.pure(o)},markers:{openClass:"dog"},lazySink:i.getSink,parts:{menu:jp(0,0,"normal")}}),Bv(tt.some(e),o);var r,i,e,o}),button:fC(function(t,n){return e=t,o=n.shared.providers,r=pS(e.name,"custom"),gS(e,r,o,[cw(""),$x()]);var e,o,r}),checkbox:fC(function(t,n){return e=t,o=n.shared.providers,r=lm.config({store:{mode:"manual",getValue:function(t){return t.element().dom().checked},setValue:function(t,n){t.element().dom().checked=n}}}),i=function(t){return t.element().dom().click(),tt.some(!0)},u=Yh.parts().field({factory:{sketch:d},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:zu([$x(),nb.config({}),hm.config({}),r,Yd.config({mode:"special",onEnter:i,onSpace:i,stopSpaceKeyup:!0}),Rm("checkbox-events",[Kr(Ut(),function(t,n){Dr(t,Rh,{name:e.name})})])])}),a=Yh.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:o.translate(e.label)},behaviours:zu([Gb.config({})])}),s=tg({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[(c=function(t){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t],innerHtml:eg("checked"===t?"selected":"unselected",o.icons)}}})("checked"),c("unchecked")]}),Yh.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[u,s.asSpec(),a]});var e,o,r,i,u,a,c,s}),colorinput:fC(function(t,n){return Kb(t,n.shared,n.colorinput)}),colorpicker:fC(function(t){var n=function(t){return"tox-"+t},e=Jx(nw,n),r=tg(e.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(t){Dr(t,Ph,{name:"hex-valid",value:!0})},onInvalidHex:function(t){Dr(t,Ph,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:zu([lm.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return Uf.getCurrent(n).bind(function(t){return lm.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);Uf.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){lm.setValue(t,{hex:tt.from(e[1]).getOr("")}),Ux.getField(t,"hex").each(function(t){Ar(t,jt())})})}}}),$x()])}}),dropzone:fC(function(t,n){return sw(t,n.shared.providers)}),grid:fC(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+e.columns+"col"]},components:_(e.items,o.interpreter)};var e,o}),selectbox:fC(function(t,n){return e=t,o=n.shared.providers,r=e.label.map(function(t){return _v(t,o)}),i=Yh.parts().field({dom:{},selectAttributes:{size:e.size},options:e.items,factory:$S,selectBehaviours:zu([nb.config({}),Rm("selectbox-change",[Kr(Ut(),function(t,n){Dr(t,Rh,{name:e.name})})])])}),u=1<e.size?tt.none():tt.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:eg("chevron-down",o.icons)}}),a={dom:{tag:"div",classes:["tox-selectfield"]},components:z([[i],u.toArray()])},Yh.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:z([r.toArray(),[a]])});var e,o,r,i,u,a}),sizeinput:fC(function(t,n){return OS(t,n.shared.providers)}),urlinput:fC(function(t,n){return cC(t,n.shared,n.urlinput)}),customeditor:fC(function(n){var e=Tt(tt.none()),o=tg({dom:{tag:n.tag}}),r=Tt(tt.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:zu([Rm("editor-foo-events",[oi(function(t){o.getOpt(t).each(function(t){n.init(t.element().dom()).then(function(n){r.get().each(function(t){n.setValue(t)}),r.set(tt.none()),e.set(tt.some(n))})})})]),lm.config({store:{mode:"manual",getValue:function(){return e.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){e.get().fold(function(){r.set(tt.some(n))},function(t){return t.setValue(n)})}}}),$x()]),components:[o.asSpec()]}}),htmlpanel:fC(function(t){return Bh.sketch({dom:{tag:"div",innerHtml:t.html},containerBehaviours:zu([nb.config({}),hm.config({})])})}),imagetools:fC(function(t,n){return JS(t,n.shared.providers)}),table:fC(function(t,n){return e=t,o=n.shared.providers,u=function(t){return{dom:{tag:"th",innerHtml:o.translate(t)}}},a=function(t){return{dom:{tag:"td",innerHtml:o.translate(t)}}},c=function(t){return{dom:{tag:"tr"},components:_(t,a)}},{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(i=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:_(i,u)}]}),(r=e.cells,{dom:{tag:"tbody"},components:_(r,c)})],behaviours:zu([nb.config({}),hm.config({})])};var e,o,r,i,u,a,c})},dC={field:function(t,n){return n}},mC=function(n,t,e){var o=vt(e,{shared:{interpreter:function(t){return gC(n,t,o)}}});return gC(n,t,o)},gC=function(n,e,o){return St(lC,e.type).fold(function(){return console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},pC=function(t){return{colorPicker:(o=t,function(t,n){Lb.colorPickerDialog(o)(t,n)}),hasCustomColors:(e=t,function(){return Ab(e)}),getColors:(n=t,function(){return Db(n)})};var n,e,o},hC=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],vC=function(t){return V(t,function(t,n){if(at(n,"items")){var e=vC(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(at(n,"inline")||at(n,"block")||at(n,"selector")){var o="custom-"+n.title.toLowerCase();return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return Je({},t,{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},bC=function(i){return(t=i,tt.from(t.getParam("style_formats")).filter(E)).map(function(t){var n,e,o,r=(n=i,e=vC(t),o=function(t){M(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})},n.formatter?o(e.customFormats):n.on("init",function(){o(e.customFormats)}),e.formats);return i.getParam("style_formats_merge",!1,"boolean")?hC.concat(r):r}).getOr(hC);var t},yC=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return vt(t,o)},xC=function(s,t,f,l){var d=function(t){return _(t,function(t){var n,e,o,r,i,u,a=J(t);if(Et(t,"items")){var c=d(t.items);return vt((i=t,u={type:"submenu",isSelected:Z(!1),getStylePreview:function(){return tt.none()}},vt(i,u)),{getStyleItems:function(){return c}})}return Et(t,"format")?yC(t,f,l):1===a.length&&B(a,"title")?vt(t,{type:"separator"}):(e=qe((n=t).title),o={type:"formatter",format:e,isSelected:f(e),getStylePreview:l(e)},r=vt(n,o),s.formatter.register(e,r),r)})};return d(t)},wC=VS.trim,SC=function(n){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===n)return!0;if(t.getAttribute("data-mce-contenteditable")===n)return!0}return!1}},CC=SC("true"),kC=SC("false"),OC=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},EC=function(t){return t.innerText||t.textContent},TC=function(t){return(n=t)&&"A"===n.nodeName&&(n.id||n.name)!==undefined&&AC(t);var n},BC=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},AC=function(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return CC(t)}return!1}(t)&&!kC(t)},DC=function(t){return BC(t)&&AC(t)},_C=function(t){var n,e,o=(n=t).id?n.id:qe("h");return OC("header",EC(t),"#"+o,BC(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=o})},MC=function(t){var n=t.id||t.name,e=EC(t);return OC("anchor",e||"#"+n,"#"+n,0,Q)},FC=function(t){var n,e;return n="h1,h2,h3,h4,h5,h6,a:not([href])",e=t,_(_c(Xn.fromDom(e),n),function(t){return t.dom()})},IC=function(t){return 0<wC(t.title).length},VC=function(t){var n,e=FC(t);return F((n=e,_(F(n,DC),_C)).concat(_(F(e,TC),MC)),IC)},RC="tinymce-url-history",HC=function(t){return w(t)&&/^https?/.test(t)},NC=function(t){return S(t)&&function(t,n){for(var e=J(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return tt.some(u)}return tt.none()}(t,function(t){return!(E(n=t)&&n.length<=5&&j(n,HC));var n}).isNone()},PC=function(){var t,n=h.localStorage.getItem(RC);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+RC+" was not valid JSON",e),{};throw e}return NC(t)?t:(console.log("Local storage "+RC+" was not valid format",t),{})},zC=function(t){var n=PC();return Object.prototype.hasOwnProperty.call(n,t)?n[t]:[]},LC=function(n,t){if(HC(n)){var e=PC(),o=Object.prototype.hasOwnProperty.call(e,t)?e[t]:[],r=F(o,function(t){return t!==n});e[t]=[n].concat(r).slice(0,5),function(t){if(!NC(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));h.localStorage.setItem(RC,JSON.stringify(t))}(e)}},jC=Object.prototype.hasOwnProperty,UC=function(t){return!!t},WC=function(t){return et(VS.makeMap(t,/[, ]/),UC)},GC=function(t,n){return jC.call(t,n)?tt.some(t[n]):tt.none()},XC=function(t,n,e){var o=GC(t,n).getOr(e);return w(o)?tt.some(o):tt.none()},YC=function(a){return function(r){return(t=a.settings,n=r,e=tt.some(t.file_picker_types).filter(UC),o=tt.some(t.file_browser_callback_types).filter(UC),i=e.or(o).map(WC).fold(function(){return!0},function(t){return GC(t,n).getOr(!1)}),u=tt.some(t.file_picker_callback).filter(k),i?u:tt.none()).map(function(o){return function(n){return iv.nu(function(e){var t=VS.extend({filetype:r},tt.from(n.meta).getOr({}));o.call(a,function(t,n){if(!w(t))throw new Error("Expected value to be string");if(n!==undefined&&!S(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var t,n,e,o,i,u}},qC=function(t){return{getHistory:zC,addToHistory:LC,getLinkInformation:(e=t,function(){return!1===e.settings.typeahead_urls?tt.none():tt.some({targets:VC(e.getBody()),anchorTop:XC(e.settings,"anchor_top","#top"),anchorBottom:XC(e.settings,"anchor_bottom","#bottom")})}),getValidationHandler:(n=t,function(){var t=n.settings.filepicker_validator_handler;return k(t)?tt.some(t):tt.none()}),getUrlPicker:YC(t)};var n,e},KC={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},JC=function(t,n,e){var o,r,i,u,a,c,s,f,l,d={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:Hg.translate},interpreter:function(t){return gC(dC,t,d)},anchors:{toolbar:function(){return{anchor:"hotspot",hotspot:e(),bubble:Ya(-12,12,KC),layouts:{onRtl:function(){return[ec]},onLtr:function(){return[oc]}}}},banner:function(){return{anchor:"hotspot",hotspot:e(),layouts:{onRtl:function(){return[ac]},onLtr:function(){return[ac]}}}},cursor:function(){return{anchor:"selection",root:Xn.fromDom(n.getBody()),getSelection:function(){var t=n.selection.getRng();return tt.some(xc(Xn.fromDom(t.startContainer),t.startOffset,Xn.fromDom(t.endContainer),t.endOffset))}}},node:function(t){return{anchor:"node",root:Xn.fromDom(n.getBody()),node:t}}},getSink:function(){return mt.value(t)}},urlinput:qC(n),styleselect:(o=n,r=function(t){return function(){return o.formatter.match(t)}},i=function(n){return function(){var t=o.formatter.get(n);return t!==undefined?tt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styleAttr:o.formatter.getCssText(n)}):tt.none()}},u=function(t){var n=t.items;return n!==undefined&&0<n.length?L(n,u):[t.format]},a=Tt([]),c=Tt([]),s=Tt([]),f=Tt([]),l=Tt(!1),o.on("init",function(){var t=bC(o),n=xC(o,t,r,i);a.set(n),c.set(L(n,u))}),o.on("addStyleModifications",function(t){var n=xC(o,t.items,r,i);s.set(n),l.set(t.replace),f.set(L(n,u))}),{getData:function(){var t=l.get()?[]:a.get(),n=s.get();return t.concat(n)},getFlattenedKeys:function(){var t=l.get()?[]:c.get(),n=f.get();return t.concat(n)}}),colorinput:pC(n)};return d},$C=tinymce.util.Tools.resolve("tinymce.util.Delay"),QC=Yn("within","extra","withinWidth"),ZC=function(t,n,o){var e,r=(e=function(t,n){var e=o(t);return tt.some({element:Z(t),start:Z(n),finish:Z(n+e),width:Z(e)})},V(t,function(n,t){return e(t,n.len).fold(Z(n),function(t){return{len:t.finish(),list:n.list.concat([t])}})},{len:0,list:[]}).list),i=F(r,function(t){return t.finish()<=n}),u=I(i,function(t,n){return t+n.width()},0),a=r.slice(i.length);return{within:Z(i),extra:Z(a),withinWidth:Z(u)}},tk=function(t){return _(t,function(t){return t.element()})},nk=function(t,n,e,o){var r,i,u,a,c,s,f,l,d,m,g,p,h=(r=t,i=n,u=e,a=ZC(i,r,u),0===a.extra().length?tt.some(a):tt.none()).getOrThunk(function(){return ZC(n,t-e(o),e)}),v=h.within(),b=h.extra(),y=h.withinWidth();return 1===b.length&&b[0].width()<=e(o)?(m=b,g=y,p=tk(v.concat(m)),QC(p,[],g)):1<=b.length?(s=b,f=o,l=y,d=tk(v).concat([f]),QC(d,tk(s),l)):(c=y,QC(tk(v),[],c))},ek=function(n,t){return t.getAnimationRoot.fold(function(){return n.element()},function(t){return t(n)})},ok=function(t){return t.dimension.property},rk=function(t,n){return t.dimension.getDimension(n)},ik=function(t,n){var e=ek(t,n);Gi(e,[n.shrinkingClass,n.growingClass])},uk=function(t,n){ji(t.element(),n.openClass),zi(t.element(),n.closedClass),Ki(t.element(),ok(n),"0px"),eu(t.element())},ak=function(t,n){ji(t.element(),n.closedClass),zi(t.element(),n.openClass),nu(t.element(),ok(n))},ck=function(t,n,e,o){e.setCollapsed(),Ki(t.element(),ok(n),rk(n,t.element())),eu(t.element()),ik(t,n),uk(t,n),n.onStartShrink(t),n.onShrunk(t)},sk=function(t,n,e,o){var r=o.getOrThunk(function(){return rk(n,t.element())});e.setCollapsed(),Ki(t.element(),ok(n),r),eu(t.element());var i=ek(t,n);ji(i,n.growingClass),zi(i,n.shrinkingClass),uk(t,n),n.onStartShrink(t)},fk=function(t,n,e){var o=rk(n,t.element());("0px"===o?ck:sk)(t,n,e,tt.some(o))},lk=function(t,n,e){var o=ek(t,n),r=Ui(o,n.shrinkingClass),i=rk(n,t.element());ak(t,n);var u=rk(n,t.element());(r?function(){Ki(t.element(),ok(n),i),eu(t.element())}:function(){uk(t,n)})(),ji(o,n.shrinkingClass),zi(o,n.growingClass),ak(t,n),Ki(t.element(),ok(n),u),e.setExpanded(),n.onStartGrow(t)},dk=function(t,n,e){var o=ek(t,n);return!0===Ui(o,n.growingClass)},mk=function(t,n,e){var o=ek(t,n);return!0===Ui(o,n.shrinkingClass)},gk=/* */Object.freeze({refresh:function(t,n,e){if(e.isExpanded()){nu(t.element(),ok(n));var o=rk(n,t.element());Ki(t.element(),ok(n),o)}},grow:function(t,n,e){e.isExpanded()||lk(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&fk(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&ck(t,n,e,tt.none())},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:dk,isShrinking:mk,isTransitioning:function(t,n,e){return!0===dk(t,n)||!0===mk(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?fk:lk)(t,n,e)},disableTransitions:ik}),pk=/* */Object.freeze({exhibit:function(t,n){var e=n.expanded;return Ti(e?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:Ct(n.dimension.property,"0px")})},events:function(e,o){return Xr([ei(Gt(),function(t,n){n.event().raw().propertyName===e.dimension.property&&(ik(t,e),o.isExpanded()&&nu(t.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),hk=[rr("closedClass"),rr("openClass"),rr("shrinkingClass"),rr("growingClass"),dr("getAnimationRoot"),Bu("onShrunk"),Bu("onStartShrink"),Bu("onGrown"),Bu("onStartGrow"),vr("expanded",!1),ir("dimension",Jo("property",{width:[Mu("property","width"),Mu("getDimension",function(t){return ua(t)+"px"})],height:[Mu("property","height"),Mu("getDimension",function(t){return sa(t)+"px"})]}))],vk=ju({fields:hk,name:"sliding",active:pk,apis:gk,state:/* */Object.freeze({init:function(t){var n=Tt(t.expanded);return Oi({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:v(n.set,!1),setExpanded:v(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),bk=Z([vr("shell",!0),Vs("toolbarBehaviours",[Qd])]),yk=Z([df({name:"groups",overrides:function(t){return{behaviours:zu([Qd.config({})])}}})]),xk=Pf({name:"Toolbar",configFields:bk(),partFields:yk(),factory:function(n,t,e,o){var r=function(t){return n.shell?tt.some(t):kf(t,n,"groups")},i=n.shell?{behaviours:[Qd.config({})],components:[]}:{behaviours:[],components:t};return{uid:n.uid,dom:n.dom,components:i.components,behaviours:Hs(n.toolbarBehaviours,i.behaviours),apis:{setGroups:function(t,n){r(t).fold(function(){throw h.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Qd.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),wk=Z([Eu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Vs("splitToolbarBehaviours",[]),Or("builtGroups",function(){return Tt([])})]),Sk=[rr("dom")],Ck=Z([ff({factory:xk,schema:Sk,name:"primary"}),ff({factory:xk,schema:Sk,name:"overflow",overrides:function(t){return{toolbarBehaviours:zu([vk.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass})])}}}),lf({name:"overflow-button",overrides:function(t){return{buttonBehaviours:zu([Am.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"}})])}}}),lf({name:"overflow-group"})]),kk=Z([rr("items"),Eu(["itemSelector"]),Vs("tgroupBehaviours",[Yd])]),Ok=Z([mf({name:"items",unit:"item"})]),Ek=Pf({name:"ToolbarGroup",configFields:kk(),partFields:Ok(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:Hs(t.tgroupBehaviours,[Yd.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),Tk=function(t,n){var e=_(n,function(t){return pu(t)});xk.setGroups(t,e)},Bk=function(t,n,e){var o=Af(t,n,["primary","overflow"]),r=o.primary(),i=o.overflow();Ki(r.element(),"visibility","hidden"),xk.setGroups(i,[]);var u=n.builtGroups.get(),a=Ek.sketch(Je({},e["overflow-group"](),{items:[Zm.sketch(Je({},e["overflow-button"](),{action:function(t){vk.toggleGrow(o.overflow())}}))]})),c=t.getSystem().build(a);Tk(r,u.concat([c]));var s=ua(r.element()),f=nk(s,u,function(t){return ua(t.element())},c);0===f.extra().length?(Qd.remove(r,c),xk.setGroups(i,[])):(Tk(r,f.within()),Tk(i,f.extra())),nu(r.element(),"visibility"),eu(r.element()),vk.refresh(i),kf(t,n,"overflow-button").each(function(t){Am.set(t,vk.hasGrown(i))})},Ak=Pf({name:"SplitToolbar",configFields:wk(),partFields:Ck(),factory:function(o,t,n,r){return{uid:o.uid,dom:o.dom,components:t,behaviours:Hs(o.splitToolbarBehaviours,[]),apis:{setGroups:function(t,n){var e;e=_(n,t.getSystem().build),o.builtGroups.set(e),Bk(t,o,r)},refresh:function(t){Bk(t,o,r)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)}}}),Dk=function(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:Je({tag:"div",classes:["tox-toolbar__group"]},n),components:[Ek.parts().items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:zu([nb.config({}),hm.config({})])}},_k=function(t){return Ek.sketch(Dk(t))},Mk=function(e,t){return zu([Yd.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Rm("toolbar-events",[oi(function(t){var n=_(e.initGroups,_k);xk.setGroups(t,n)})])])},Fk=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return Ak.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":Dk({title:tt.none(),items:[]}),"overflow-button":dS({name:"more",icon:tt.some("more-drawer"),disabled:!1,tooltip:tt.some("More...")},tt.none(),t.backstage.shared.providers)},components:[Ak.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),Ak.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}})],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},splitToolbarBehaviours:Mk(t,n)})},Ik=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return xk.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"]},components:[xk.parts().groups({})],toolbarBehaviours:Mk(t,n)})},Vk=[Sr("disabled",!1),gr("tooltip"),gr("icon"),gr("text"),Cr("onSetup",function(){return Q})],Rk=Io([ur("type"),cr("onAction")].concat(Vk)),Hk=function(t){return Xo("toolbarbutton",Rk,t)},Nk=Io([ur("type"),gr("tooltip"),gr("icon"),gr("text"),cr("fetch"),Cr("onSetup",function(){return Q})]),Pk=Io([ur("type"),gr("tooltip"),gr("icon"),gr("text"),pr("select"),cr("fetch"),Cr("onSetup",function(){return Q}),wr("presets","normal",["normal","color","listpreview"]),vr("columns",1),cr("onAction"),cr("onItemAction")]),zk=[Sr("active",!1)].concat(Vk),Lk=Io(zk.concat([ur("type"),cr("onAction")])),jk=function(t){return Xo("ToggleButton",Lk,t)},Uk=[Cr("predicate",function(){return!1}),wr("scope","node",["node","editor"]),wr("position","selection",["node","selection","line"])],Wk=Vk.concat([vr("type","contextformbutton"),vr("primary",!1),cr("onAction"),Or("original",d)]),Gk=zk.concat([vr("type","contextformbutton"),vr("primary",!1),cr("onAction"),Or("original",d)]),Xk=Vk.concat([vr("type","contextformbutton")]),Yk=zk.concat([vr("type","contextformtogglebutton")]),qk=Jo("type",{contextformbutton:Wk,contextformtogglebutton:Gk}),Kk=Io([vr("type","contextform"),Cr("initValue",function(){return""}),gr("label"),lr("commands",qk),mr("launch",Jo("type",{contextformbutton:Xk,contextformtogglebutton:Yk}))].concat(Uk)),Jk=Io([vr("type","contexttoolbar"),ur("items")].concat(Uk)),$k=qe("toolbar.button.execute"),Qk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},Zk=/* */Object.freeze({getState:function(t,n,e){return e}}),tO=/* */Object.freeze({events:function(r,i){var o=function(e,o){r.updateState.each(function(t){var n=t(e,o);i.set(n)}),r.renderComponents.each(function(t){var n=t(o,i.get());vs(e),M(n,function(t){ms(e,e.getSystem().build(t))})})};return Xr([Kr(On(),function(t,n){var e=r.channel;B(n.channels(),e)&&o(t,n.data())}),oi(function(n,t){r.initialData.each(function(t){o(n,t)})})])}}),nO=/* */Object.freeze({init:function(t){var n=Tt(tt.none());return{readState:function(){return n.get().getOr("none")},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(tt.none())}}}}),eO=[rr("channel"),dr("renderComponents"),dr("updateState"),dr("initialData")],oO=ju({fields:eO,name:"reflecting",active:tO,apis:Zk,state:nO}),rO=Z([rr("toggleClass"),rr("fetch"),Du("onExecute"),vr("getHotspot",tt.some),vr("layouts",tt.none()),Du("onItemExecute"),dr("lazySink"),rr("dom"),Bu("onOpen"),Vs("splitDropdownBehaviours",[Jh,Yd,hm]),vr("matchWidth",!1),vr("useMinWidth",!1),vr("eventOrder",{}),dr("role")].concat(Cv())),iO=ff({factory:Zm,schema:[rr("dom")],name:"arrow",defaults:function(t){return{buttonBehaviours:zu([hm.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(_r)},buttonBehaviours:zu([Am.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),uO=ff({factory:Zm,schema:[rr("dom")],name:"button",defaults:function(t){return{buttonBehaviours:zu([hm.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),aO=Z([iO,uO,df({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[rr("text")],name:"aria-descriptor"}),lf({schema:[Ou()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),av()]),cO=Pf({name:"SplitDropdown",configFields:rO(),partFields:aO(),factory:function(o,t,n,e){var r=function(t){Uf.getCurrent(t).each(function(t){el.highlightFirst(t),Yd.focusIn(t)})},i=function(t){lv(o,function(t){return t},t,e,r,Eh.HighlightFirst).get(Q)},u=function(t){var n=Of(t,o,"button");return _r(n),tt.some(!0)},a=bt(Xr([oi(function(e,t){kf(e,o,"aria-descriptor").each(function(t){var n=qe("aria");De(t.element(),"id",n),De(e.element(),"aria-describedby",n)})})]),Qm(tt.some(i)));return{uid:o.uid,dom:o.dom,components:t,eventOrder:Je({},o.eventOrder,{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Hs(o.splitDropdownBehaviours,[Jh.config({others:{sandbox:function(t){var n=Of(t,o,"arrow");return gv(o,t,{onOpen:function(){Am.on(n),Am.on(t)},onClose:function(){Am.off(n),Am.off(t)}})}}}),Yd.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),tt.some(!0)}}),hm.config({}),Am.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}}}),sO=function(n){return{isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},fO=function(n){return{setActive:function(t){Am.set(n,t)},isActive:function(){return Am.isOn(n)},isDisabled:function(){return mp.isDisabled(n)},setDisabled:function(t){return t?mp.disable(n):mp.enable(n)}}},lO=function(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},dO=qe("focus-button"),mO=function(n,e,t,o,r,i){var u;return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]),attributes:lO(t,i)},components:kp([n.map(function(t){return cS(t,i.icons)}),e.map(function(t){return fS(t,"tox-tbtn",i)})]),eventOrder:(u={},u[Ft()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:zu([Rm("common-button-display-events",[Kr(Ft(),function(t,n){n.event().prevent(),Ar(t,dO)})])].concat(o.map(function(t){return oO.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return kp([t.icon.map(function(t){return cS(t,i.icons)}),t.text.map(function(t){return fS(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}},gO=function(t,n,e){var o,r=Tt(Q),i=mO(t.icon,t.text,t.tooltip,tt.none(),tt.none(),e);return Zm.sketch({dom:i.dom,components:i.components,eventOrder:Qk,buttonBehaviours:zu([Rm("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},ui(function(n,t){vp(o,n)(function(t){Dr(n,$k,{buttonApi:t}),o.onAction(t)})})),bp(n,r),yp(n,r)]),pp(t.disabled)].concat(n.toolbarButtonBehaviours))})},pO=function(t,n,e){return gO(t,{toolbarButtonBehaviours:[].concat(0<e.length?[Rm("toolbarButtonWith",e)]:[]),getApi:sO,onSetup:t.onSetup},n)},hO=function(t,n,e){return vt(gO(t,{toolbarButtonBehaviours:[Qd.config({}),Am.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[Rm("toolbarToggleButtonWith",e)]:[]),getApi:fO,onSetup:t.onSetup},n))},vO=function(n,t){var e,o,r,i,u=qe("channel-update-split-dropdown-display"),a=function(e){return{isDisabled:function(){return mp.isDisabled(e)},setDisabled:function(t){return t?mp.disable(e):mp.enable(e)},setIconFill:function(t,n){vu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){De(t,"fill",n)})},setIconStroke:function(t,n){vu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){De(t,"stroke",n)})},setActive:function(n){De(e.element(),"aria-pressed",n),vu(e.element(),"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return Am.set(t,n)})})},isActive:function(){return vu(e.element(),"span").exists(function(t){return e.getSystem().getByDom(t).exists(Am.isOn)})}}},c=Tt(Q),s={getApi:a,onSetup:n.onSetup};return cO.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:bt({"aria-pressed":!1},lO(n.tooltip,t.providers))},onExecute:function(t){n.onAction(a(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:zu([hp(!1),Rm("split-dropdown-events",[Kr(dO,hm.focus),bp(s,c),yp(s,c)])]),eventOrder:(e={},e[Rn()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:(o=a,r=n,i=t.providers,function(n){return iv.nu(function(t){return r.fetch(t)}).map(function(t){return tt.from(nh(vt(Zp(qe("menu-value"),t,function(t){r.onItemAction(o(n),t)},r.columns,r.presets,Sp.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),i),{movement:Up(r.columns,r.presets),menuBehaviours:Tg("auto"!==r.columns?[]:[oi(function(o,t){Og(o,4,Xg(r.presets)).each(function(t){var n=t.numRows,e=t.numColumns;Yd.setGridSize(o,n,e)})})])})))})}),parts:{menu:jp(0,n.columns,n.presets)},components:[cO.parts().button(mO(n.icon,n.text,tt.none(),tt.some(u),tt.some([Am.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),cO.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:eg("chevron-down",t.providers.icons)}}),cO.parts()["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})},bO=function(i,u){return Kr($k,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return Ar(e,_n())},getValue:function(){return lm.getValue(e)}});u.onAction(r,n.event().buttonApi())})},yO=function(t,n,e){var o,r,i,u,a,c,s,f,l,d,m,g,p={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?(s=t,l=p,(d=(f=n).original).primary,m=$e(d,["primary"]),g=Yo(jk(Je({},m,{type:"togglebutton",onAction:function(){}}))),hO(g,l.backstage.shared.providers,[bO(s,f)])):(o=t,i=p,(u=(r=n).original).primary,a=$e(u,["primary"]),c=Yo(Hk(Je({},a,{type:"button",onAction:function(){}}))),pO(c,i.backstage.shared.providers,[bO(o,r)]))},xO=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=tg(Nv.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:zu([Yd.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return _r(t),!0})},onLeft:function(t,n){return n.cut(),tt.none()},onRight:function(t,n){return n.cut(),tt.none()}})])})),c=(e=a,o=t.commands,r=n.shared.providers,i=_(o,function(t){return tg(yO(e,t,r))}),{asSpecs:function(){return _(i,function(t){return t.asSpec()})},findPrimary:function(e){return At(o,function(t,n){return t.primary?tt.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(m(mp.isDisabled)):tt.none()})}});return Ik({uid:qe("context-toolbar"),initGroups:[{title:tt.none(),items:[a.asSpec()]},{title:tt.none(),items:c.asSpecs()}],onEscape:tt.none,cyclicKeying:!0,backstage:n})},wO=qe("forward-slide"),SO=qe("backward-slide"),CO=qe("change-slide-event"),kO="tox-pop--resizing",OO=function(n,t){return At(t,function(t){return t.predicate(n.dom())?tt.some({toolbarApi:t,elem:n}):tt.none()})},EO=function(n,e){var t=function(t){return t.dom()===e.getBody()},o=Xn.fromDom(e.selection.getNode());return OO(o,n.inNodeScope).orThunk(function(){return OO(o,n.inEditorScope).orThunk(function(){return function(t,n,e){for(var o=t.dom(),r=k(e)?e:Z(!1);o.parentNode;){o=o.parentNode;var i=Xn.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return tt.none()}(o,function(t){return OO(t,n.inNodeScope)},t)})})},TO=function(e,r){var t={},i=[],u=[],a={},c={},o=function(n,e){var o=Yo(Xo("ContextForm",Kk,e));(t[n]=o).launch.map(function(t){a["form:"+n]=Je({},e.launch,{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?u.push(o):i.push(o),c[n]=o},s=function(n,e){var t;(t=e,Xo("ContextToolbar",Jk,t)).each(function(t){"editor"===e.scope?u.push(t):i.push(t),c[n]=t})},n=J(e);return M(n,function(t){var n=e[t];"contextform"===n.type?o(t,n):"contexttoolbar"===n.type&&s(t,n)}),{forms:t,inNodeScope:i,inEditorScope:u,lookupTable:c,formNavigators:a}},BO=qe("update-menu-text"),AO=qe("update-menu-icon"),DO=function(t,n,o){var r=t.text.map(function(t){return tg(fS(t,n,o.providers))}),i=t.icon.map(function(t){return tg(sS(t,o.providers.icons))}),e=function(t,n){var e=lm.getValue(t);return hm.focus(e),Dr(e,"keydown",{raw:n.event().raw()}),Wb.close(e),tt.some(!0)},u=t.role.fold(function(){return{}},function(t){return{role:t}}),a=t.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}});return tg(Wb.sketch(Je({},u,{dom:{tag:"button",classes:[n,n+"--select"].concat(_(t.classes,function(t){return n+"--"+t})),attributes:Je({},a)},components:kp([i.map(function(t){return t.asSpec()}),r.map(function(t){return t.asSpec()}),tt.some({dom:{tag:"div",classes:[n+"__select-chevron"],innerHtml:eg("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:zu([pp(t.disabled),Gb.config({}),Qd.config({}),Rm("menubutton-update-display-text",[oi(t.onAttach),ri(t.onDetach),Kr(BO,function(n,e){r.bind(function(t){return t.getOpt(n)}).each(function(t){Qd.set(t,[lu(o.providers.translate(e.event().text()))])})}),Kr(AO,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){Qd.set(t,[sS(e.event().icon(),o.providers.icons)])})})])]),eventOrder:vt(Qk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:zu([Yd.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:n+"--active",parts:{menu:jp(0,t.columns,t.presets)},fetch:function(){return iv.nu(t.fetch)}}))).asSpec()},_O=function(t,n,e,o){var r,i,u,a,c,s="basic"===e.type?function(){return _(e.data,function(t){return yC(t,o.isSelectedFor,o.getPreviewFor)})}:e.getData;return{items:(r=n,i=o,u=function(t,n,e){var o=r.shared.providers.translate(t.title);return"separator"===t.type?{type:"separator",text:o}:"submenu"===t.type?{type:"nestedmenuitem",text:o,disabled:e,getSubmenuItems:function(){return L(t.getStyleItems(),function(t){return a(t,n)})}}:Je({type:"togglemenuitem",text:o,active:t.isSelected(),disabled:e,onAction:i.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}}))},a=function(t,n){var e="formatter"===t.type&&i.isInvalid(t);return 0===n?e?[]:[u(t,n,!1)]:[u(t,n,e)]},c=function(t){var n=i.shouldHide?0:1;return L(t,function(t){return a(t,n)})},{validateItems:c,getFetch:function(o,r){return function(t){var n=r(),e=c(n);t(Hv(e,Sp.CLOSE_ON_EXECUTE,o.shared.providers))}}}),getStyleItems:s}},MO=function(e,t,n,o){var r=_O(0,t,n,o),i=r.items,u=r.getStyleItems;return DO({text:o.icon.isSome()?tt.none():tt.some(""),icon:o.icon,tooltip:tt.from(o.tooltip),role:tt.none(),fetch:i.getFetch(t,u),onAttach:o.nodeChangeHandler.map(function(n){return function(t){return e.on("nodeChange",n(t))}}).getOr(function(){}),onDetach:o.nodeChangeHandler.map(function(n){return function(t){return e.off("nodeChange",n(t))}}).getOr(function(){}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"]},"tox-tbtn",t.shared)};(LS=zS||(zS={}))[LS.SemiColon=0]="SemiColon",LS[LS.Space=1]="Space";var FO,IO,VO=function(t,n,e,o){var r,i,u=St(t.settings,n).getOr(e);return{type:"basic",data:(i=u,r=o===zS.SemiColon?i.replace(/;$/,"").split(";"):i.split(" "),_(r,function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}},RO=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],HO=function(e){var t=tt.some(function(n){return function(){var t=R(RO,function(t){return e.formatter.match(t.format)}).fold(function(){return"left"},function(t){return t.title.toLowerCase()});Dr(n,AO,{icon:"align-"+t})}}),n={type:"basic",data:RO};return{tooltip:"Align",icon:tt.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getPreviewFor:function(t){return function(){return tt.none()}},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.formatter.match(t.format)?e.formatter.remove(t.format):e.formatter.apply(t.format)})}},nodeChangeHandler:t,dataset:n,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},NO=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],PO=function(r){var o=function(){var e=function(t){return t?t.split(",")[0]:""},t=r.queryCommandValue("FontName"),n=i.data,o=t?t.toLowerCase():"";return R(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return 0===(t=o).indexOf("-apple-system")&&(n=t.toLowerCase().split(/['"]?\s*,\s*['"]?/),j(NO,function(t){return-1<n.indexOf(t.toLowerCase())}))?tt.from({title:"System Font",format:o}):tt.none();var t,n})},t=tt.some(function(e){return function(){var t=r.queryCommandValue("FontName"),n=o().fold(function(){return t},function(t){return t.title});Dr(e,BO,{text:n})}}),i=VO(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",zS.SemiColon);return{tooltip:"Fonts",icon:tt.none(),isSelectedFor:function(n){return function(){return o().exists(function(t){return t.format===n})}},getPreviewFor:function(t){return function(){return tt.some({tag:"div",styleAttr:-1===t.indexOf("dings")?"font-family:"+t:""})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},nodeChangeHandler:t,dataset:i,shouldHide:!1,isInvalid:function(){return!1}}},zO=function(t,n){return/[0-9.]+px$/.test(t)?(e=72*parseInt(t,10)/96,o=n||0,r=Math.pow(10,o),Math.round(e*r)/r+"pt"):t;var e,o,r},LO=function(i){var u=function(){var e=tt.none(),o=a.data,r=i.queryCommandValue("FontSize");if(r)for(var t=function(t){var n=zO(r,t);e=R(o,function(t){return t.format===r||t.format===n})},n=3;e.isNone()&&0<=n;n--)t(n);return{matchOpt:e,px:r}},t=tt.some(function(r){return function(){var t=u(),n=t.matchOpt,e=t.px,o=n.fold(function(){return e},function(t){return t.title});Dr(r,BO,{text:o})}}),a=VO(i,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",zS.Space);return{tooltip:"Font sizes",icon:tt.none(),isSelectedFor:function(n){return function(){return u().matchOpt.exists(function(t){return t.format===n})}},getPreviewFor:function(){return function(){return tt.none()}},onAction:function(t){return function(){i.undoManager.transact(function(){i.focus(),i.execCommand("FontSize",!1,t.format)})}},nodeChangeHandler:t,dataset:a,shouldHide:!1,isInvalid:function(){return!1}}},jO=function(e,t,n){var o=n.parents,r=t();return At(o,function(n){return R(r,function(t){return e.formatter.matchNode(n,t.format)})}).orThunk(function(){return e.formatter.match("p")?tt.some({title:"Paragraph",format:"p"}):tt.none()})},UO=function(o){var t=tt.some(function(e){return function(t){var n=jO(o,function(){return r.data},t).fold(function(){return"Paragraph"},function(t){return t.title});Dr(e,BO,{text:n})}}),r=VO(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",zS.SemiColon);return{tooltip:"Blocks",icon:tt.none(),isSelectedFor:function(t){return function(){return o.formatter.match(t)}},getPreviewFor:function(n){return function(){var t=o.formatter.get(n);return tt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styleAttr:o.formatter.getCssText(n)})}},onAction:function(t){return function(){o.undoManager.transact(function(){o.focus(),o.formatter.match(t.format)?o.formatter.remove(t.format):o.formatter.apply(t.format)})}},nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!o.formatter.canApply(t.format)}}},WO=function(i){var t=tt.some(function(e){var o=function(t){var n=t.items;return n!==undefined&&0<n.length?L(n,o):[{title:t.title,format:t.format}]},r=L(bC(i),o);return function(t){var n=jO(i,function(){return r},t).fold(function(){return"Paragraph"},function(t){return t.title});Dr(e,BO,{text:n})}});return{tooltip:"Formats",icon:tt.none(),isSelectedFor:function(t){return function(){return i.formatter.match(t)}},getPreviewFor:function(n){return function(){var t=i.formatter.get(n);return t!==undefined?tt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styleAttr:i.formatter.getCssText(n)}):tt.none()}},onAction:function(t){return function(){i.undoManager.transact(function(){i.focus(),i.formatter.match(t.format)?i.formatter.remove(t.format):i.formatter.apply(t.format)})}},nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!i.formatter.canApply(t.format)}}},GO={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable tableprops deletetable row column cell"},help:{title:"Help",items:"help"}},XO=function(t,n,e,o){return DO({text:t.text,icon:t.icon,tooltip:t.tooltip,role:o,fetch:function(n){t.fetch(function(t){n(Hv(t,Sp.CLOSE_ON_EXECUTE,e.providers))})},onAttach:function(){},onDetach:function(){},columns:1,presets:"normal",classes:[]},n,e)},YO=function(t){return"string"==typeof t?t.split(" "):t},qO=function(u,a){var c=bt(GO,a.menus),n=0<J(a.menus).length,t=a.menubar===undefined||!0===a.menubar?YO("file edit view insert format tools table help"):YO(!1===a.menubar?"":a.menubar),e=F(t,function(t){return n&&a.menus.hasOwnProperty(t)&&a.menus[t].hasOwnProperty("items")||GO.hasOwnProperty(t)}),o=_(e,function(t){var n,e,o,r,i=c[t];return n={title:i.title,items:YO(i.items)},e=a,r=(o=u,o.getParam("removed_menuitems","")).split(/[ ,]/),{text:n.title,getItems:function(){return L(n.items,function(t){var n=t.toLowerCase();return 0===n.trim().length?[]:A(r,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:e.menuItems[n]?[e.menuItems[n]]:[]})}}});return F(o,function(t){return 0<t.getItems().length&&A(t.getItems(),function(t){return"separator"!==t.type})})},KO=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],JO=function(o,r){return function(t,n){var e=o(t).fold(p(mt.error,Ko),mt.value).getOrDie();return r(e,n)}},$O={button:JO(Hk,function(t,n){return e=t,o=n.backstage.shared.providers,pO(e,o,[]);var e,o}),togglebutton:JO(jk,function(t,n){return e=t,o=n.backstage.shared.providers,hO(e,o,[]);var e,o}),menubutton:JO(function(t){return Xo("menubutton",Nk,t)},function(t,n){return XO(t,"tox-tbtn",n.backstage.shared,tt.none())}),splitbutton:JO(function(t){return Xo("SplitButton",Pk,t)},function(t,n){return vO(t,n.backstage.shared)}),styleSelectButton:function(t,n){return e=t,o=n.backstage,r=o.styleselect,MO(e,o,r,WO(e));var e,o,r},fontsizeSelectButton:function(t,n){return e=t,o=n.backstage,r=LO(e),MO(e,o,r.dataset,r);var e,o,r},fontSelectButton:function(t,n){return e=t,o=n.backstage,r=PO(e),MO(e,o,r.dataset,r);var e,o,r},formatButton:function(t,n){return e=t,o=n.backstage,r=UO(e),MO(e,o,r.dataset,r);var e,o,r},alignMenuButton:function(t,n){return e=t,o=n.backstage,r=HO(e),MO(e,o,r.dataset,r);var e,o,r}},QO={styleselect:$O.styleSelectButton,fontsizeselect:$O.fontsizeSelectButton,fontselect:$O.fontSelectButton,formatselect:$O.formatButton,align:$O.alignMenuButton},ZO=function(t){var n=t.split("|");return _(n,function(t){return{items:t.trim().split(" ")}})},tE=function(t){return!1===t.toolbar?[]:t.toolbar===undefined||!0===t.toolbar?(e=t.buttons,n=_(KO,function(t){var n=F(t.items,function(t){return at(e,t)||at(QO,t)});return{name:t.name,items:n}}),F(n,function(t){return 0<t.items.length})):w(t.toolbar)?ZO(t.toolbar):E(t.toolbar)&&w(t.toolbar[0])?ZO(t.toolbar.join(" | ")):t.toolbar;var e,n},nE=function(e,o,r){var t=tE(o),n=_(t,function(t){var n=L(t.items,function(t){return 0===t.trim().length?[]:St(o.buttons,t.toLowerCase()).fold(function(){return St(QO,t.toLowerCase()).map(function(t){return t(e,r)}).orThunk(function(){return tt.none()})},function(t){return e=r,St($O,(n=t).type).fold(function(){return console.error("skipping button defined by",n),tt.none()},function(t){return tt.some(t(n,e))});var n,e}).toArray()});return{title:tt.from(e.translate(t.name)),items:n}});return F(n,function(t){return 0<t.items.length})},eE=function(i,t,u,c){var e,o,s=gu((e={sink:u,onEscape:function(){return i.focus(),tt.some(!0)}},o=Tt([]),$m.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){o.set([]),$m.getContent(t).each(function(t){nu(t.element(),"visibility")}),ji(t.element(),kO),nu(t.element(),"width")},inlineBehaviours:zu([Rm("context-toolbar-events",[ei(Gt(),function(t,n){$m.getContent(t).each(function(t){}),ji(t.element(),kO),nu(t.element(),"width")}),Kr(CO,function(n,e){nu(n.element(),"width");var t=ua(n.element());$m.setContent(n,e.event().contents()),zi(n.element(),kO);var o=ua(n.element());Ki(n.element(),"width",t+"px"),$m.getContent(n).each(function(t){e.event().focus().bind(function(t){return ml(t),pl(n.element())}).orThunk(function(){return Yd.focusIn(t),gl()})}),setTimeout(function(){Ki(n.element(),"width",o+"px")},0)}),Kr(wO,function(t,n){$m.getContent(t).each(function(t){o.set(o.get().concat([{bar:t,focus:gl()}]))}),Dr(t,CO,{contents:n.event().forwardContents(),focus:tt.none()})}),Kr(SO,function(n,t){q(o.get()).each(function(t){o.set(o.get().slice(0,o.get().length-1)),Dr(n,CO,{contents:pu(t.bar),focus:t.focus})})})]),Yd.config({mode:"special",onEscape:function(n){return q(o.get()).fold(function(){return e.onEscape()},function(t){return Ar(n,SO),tt.some(!0)})}})]),lazySink:function(){return mt.value(e.sink)}}))),f=function(){return tt.some(Xn.fromDom(i.contentAreaContainer))};i.on("init",function(){var t=i.getBody().ownerDocument.defaultView,n=ah(Xn.fromDom(t),"scroll",function(){l.get().each(function(t){var n=d.get().getOr(i.selection.getNode()).getBoundingClientRect(),e=i.contentAreaContainer.getBoundingClientRect(),o=n.bottom<0,r=n.top>e.height;o||r?Ki(s.element(),"display","none"):(nu(s.element(),"display"),fs.positionWithin(u,t,s,f()))})});i.on("remove",function(){n.unbind()})});var l=Tt(tt.none()),d=Tt(tt.none()),n=Tt(null),m=function(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:zu([Yd.config({mode:"acyclic"}),Rm("pop-dialog-wrap-events",[oi(function(t){i.shortcuts.add("ctrl+F9","focus statusbar",function(){return Yd.focusIn(t)})}),ri(function(t){i.shortcuts.remove("ctrl+F9")})])])}},a=Yt(function(){return TO(t,function(t){var n=g(t);Dr(s,wO,{forwardContents:m(n)})})}),g=function(t){var n,e,o=i.ui.registry.getAll().buttons,r=a();return"contexttoolbar"===t.type?(n=bt(o,r.formNavigators),e=nE(i,{buttons:n,toolbar:t.items},c),Ik({uid:qe("context-toolbar"),initGroups:e,onEscape:tt.none,cyclicKeying:!0,backstage:c.backstage})):xO(t,c.backstage)};i.on("contexttoolbar-show",function(n){var t=a();St(t.lookupTable,n.toolbarKey).each(function(t){b(t,n.target===i?tt.none():tt.some(n)),$m.getContent(s).each(Yd.focusIn)})});var r={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},p={maxHeightFunction:ja()},h={bubble:Ya(12,0,r),layouts:{onLtr:function(){return[cc]},onRtl:function(){return[sc]}},overrides:p},v={bubble:Ya(0,12,r),layouts:{onLtr:function(){return[uc,ac,rc,ec,ic,oc]},onRtl:function(){return[uc,ac,ic,oc,rc,ec]}},overrides:p},b=function(t,n){x();var e,o,r,i=g(t),u=n.map(Xn.fromDom),a=(e=t.position,o=u,r="node"===e?c.backstage.shared.anchors.node(o):c.backstage.shared.anchors.cursor(),vt(r,"line"===e?h:v));l.set(tt.some(a)),d.set(n),$m.showWithin(s,a,m(i),f()),nu(s.element(),"display")},y=function(){var t=a();EO(t,i).fold(function(){l.set(tt.none()),$m.hide(s)},function(t){b(t.toolbarApi,tt.some(t.elem.dom()))})},x=function(){var t=n.get();null!==t&&(clearTimeout(t),n.set(null))},w=function(t){x(),n.set(t)};i.on("init",function(){i.on("click keyup setContent ObjectResized ResizeEditor",function(t){w($C.setEditorTimeout(i,y,0))}),i.on("focusout",function(t){$C.setEditorTimeout(i,function(){pl(u.element()).isNone()&&pl(s.element()).isNone()&&(l.set(tt.none()),$m.hide(s))},0)}),i.on("nodeChange",function(t){pl(s.element()).fold(function(){w($C.setEditorTimeout(i,y,0))},function(t){})})})},oE=function(t,e,o){var n=ah(Xn.fromDom(h.document),"mousedown",function(n){M([e,o],function(t){t.broadcastOn([Ds()],{target:n.target()})})}),r=ah(Xn.fromDom(h.document),"touchstart",function(n){M([e,o],function(t){t.broadcastOn([Ds()],{target:n.target()})})}),i=ah(Xn.fromDom(h.document),"mouseup",function(n){0===n.raw().button&&M([e,o],function(t){t.broadcastOn([_s()],{target:n.target()})})}),u=function(n){M([e,o],function(t){t.broadcastOn([Ds()],{target:Xn.fromDom(n.target)})})};t.on("mousedown",u),t.on("touchstart",u);var a=function(n){0===n.button&&M([e,o],function(t){t.broadcastOn([_s()],{target:Xn.fromDom(n.target)})})};t.on("mouseup",a);var c=function(n){M([e,o],function(t){t.broadcastEvent(In(),n)})};t.on("ScrollWindow",c);var s=function(n){M([e,o],function(t){t.broadcastEvent(Vn(),n)})};t.on("ResizeWindow",s),t.on("remove",function(){t.off("mousedown",u),t.off("touchstart",u),t.off("mouseup",a),t.off("ResizeWindow",s),t.off("ScrollWindow",c),n.unbind(),r.unbind(),i.unbind()}),t.on("detach",function(){xs(e),xs(o),e.destroy(),o.destroy()})},rE=Mf,iE=pf,uE=Nf({factory:function(o,t){var n={focus:Yd.focusIn,setMenus:function(t,n){var e=_(n,function(n){var t={text:tt.some(n.text),icon:tt.none(),tooltip:tt.none(),fetch:function(t){t(n.getItems())}};return XO(t,"tox-mbtn",{getSink:o.getSink,providers:o.providers},tt.some("menuitem"))});Qd.set(t,e)}};return{uid:o.uid,dom:o.dom,components:[],behaviours:zu([Qd.config({}),Rm("menubar-events",[oi(function(t){o.onSetup(t)}),Kr(Ht(),function(e,t){vu(e.element(),".tox-mbtn--active").each(function(n){bu(t.event().target(),".tox-mbtn").each(function(t){ce(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){Wb.expand(t),Wb.close(n),hm.focus(t)})})})})}),Kr(Pn(),function(e,t){t.event().prevFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(n){t.event().newFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(t){Wb.isOpen(n)&&(Wb.expand(t),Wb.close(n))})})})]),Yd.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return o.onEscape(t),tt.some(!0)}}),nb.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[rr("dom"),rr("uid"),rr("onEscape"),rr("getSink"),rr("providers"),vr("onSetup",Q)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),aE="container",cE=[Vs("slotBehaviours",[])],sE=function(t){return"<alloy.field."+t+">"},fE=function(r,t,n){var e,o=function(t){return Bf(r)},i=function(e,o){return void 0===o&&(o=undefined),function(t,n){return kf(t,r,n).map(function(t){return e(t,n)}).getOr(o)}},u=function(t,n){return"true"!==Me(t.element(),"aria-hidden")},a=i(u,!1),c=i(function(t,n){if(u(t)){var e=t.element();Ki(e,"display","none"),De(e,"aria-hidden","true"),Dr(t,zn(),{name:n,visible:!1})}}),s=(e=c,function(n,t){M(t,function(t){return e(n,t)})}),f=i(function(t,n){if(!u(t)){var e=t.element();nu(e,"display"),Ie(e,"aria-hidden"),Dr(t,zn(),{name:n,visible:!0})}}),l={getSlotNames:o,getSlot:function(t,n){return kf(t,r,n)},isShowing:a,hideSlot:c,hideAllSlots:function(t){return s(t,o())},showSlot:f};return{uid:r.uid,dom:r.dom,components:t,behaviours:Rs(r.slotBehaviours),apis:l}},lE=et({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},Ci),dE=Je({},lE,{sketch:function(t){var e,n=(e=[],{slot:function(t,n){return e.push(t),yf(aE,sE(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=_(r,function(t){return ff({name:t,pname:sE(t)})});return If(aE,cE,i,fE,o)}}),mE=Io([gr("icon"),gr("tooltip"),Cr("onShow",Q),Cr("onHide",Q),Cr("onSetup",function(){return Q})]),gE=function(t){return{element:function(){return t.element().dom()}}},pE=function(e,o){var r=_(J(o),function(t){var n=o[t],e=Yo(Xo("sidebar",mE,n));return{name:t,getApi:gE,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return _(r,function(t){var n=Tt(Q);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Tg([bp(t,n),yp(t,n),Kr(zn(),function(n,t){var e=t.event();R(r,function(t){return t.name===e.name()}).each(function(t){(e.visible()?t.onShow:t.onHide)(t.getApi(n))})})])})})},hE=function(t,e){Uf.getCurrent(t).each(function(t){return Qd.set(t,[(n=e,dE.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:pE(t,n),slotBehaviours:Tg([oi(function(t){return dE.hideAllSlots(t)})])}}))]);var n})},vE=function(t){return Uf.getCurrent(t).bind(function(t){return vk.isGrowing(t)||vk.hasGrown(t)?Uf.getCurrent(t).bind(function(n){return R(dE.getSlotNames(n),function(t){return dE.isShowing(n,t)})}):tt.none()})},bE=qe("FixSizeEvent"),yE=qe("AutoSizeEvent"),xE=iE.optional({factory:uE,name:"menubar",schema:[rr("dom"),rr("getSink")]}),wE=iE.optional({factory:{sketch:function(t){return(t.split?Fk:Ik)({uid:t.uid,onEscape:function(){return t.onEscape(),tt.some(!0)},cyclicKeying:!1,initGroups:[],backstage:t.backstage})}},name:"toolbar",schema:[rr("dom"),rr("onEscape")]}),SE=iE.optional({name:"socket",schema:[rr("dom")]}),CE=iE.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:zu([nb.config({}),hm.config({}),vk.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){Uf.getCurrent(t).each(dE.hideAllSlots),Ar(t,yE)},onGrown:function(t){Ar(t,yE)},onStartGrow:function(t){Dr(t,bE,{width:Zi(t.element(),"width").getOr("")})},onStartShrink:function(t){Dr(t,bE,{width:ua(t.element())+"px"})}}),Qd.config({}),Uf.config({find:function(t){var n=Qd.contents(t);return Y(n)}})])}],behaviours:zu([Zx(0),Rm("sidebar-sliding-events",[Kr(bE,function(t,n){Ki(t.element(),"width",n.event().width())}),Kr(yE,function(t,n){nu(t.element(),"width")})])])}}},name:"sidebar",schema:[rr("dom")]}),kE=Pf({name:"OuterContainer",factory:function(e,t,n){var o={getSocket:function(t){return rE.getPart(t,e,"socket")},setSidebar:function(t,n){rE.getPart(t,e,"sidebar").each(function(t){return hE(t,n)})},toggleSidebar:function(t,o){rE.getPart(t,e,"sidebar").each(function(t){return n=t,e=o,void Uf.getCurrent(n).each(function(n){Uf.getCurrent(n).each(function(t){vk.hasGrown(n)?dE.isShowing(t,e)?vk.shrink(n):(dE.hideAllSlots(t),dE.showSlot(t,e)):(dE.hideAllSlots(t),dE.showSlot(t,e),vk.grow(n))})});var n,e})},whichSidebar:function(t){return rE.getPart(t,e,"sidebar").bind(vE).getOrNull()},getToolbar:function(t){return rE.getPart(t,e,"toolbar")},setToolbar:function(t,n){rE.getPart(t,e,"toolbar").each(function(t){xk.setGroups(t,n)})},focusToolbar:function(t){rE.getPart(t,e,"toolbar").each(function(t){Yd.focusIn(t)})},setMenubar:function(t,n){rE.getPart(t,e,"menubar").each(function(t){uE.setMenus(t,n)})},focusMenubar:function(t){rE.getPart(t,e,"menubar").each(function(t){uE.focus(t)})}};return{uid:e.uid,dom:e.dom,components:t,apis:o,behaviours:e.behaviours}},configFields:[rr("dom"),rr("behaviours")],partFields:[xE,wE,SE,CE],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=_(e,function(t){return _k(t)});t.setToolbar(n,o)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),OE=function(t){return t.fire("SkinLoaded")},EE=function(t){return t.fire("ResizeEditor")},TE=function(t){return t.fire("ResizeContent")},BE=function(t){var n=function(){t._skinLoaded=!0,OE(t)};return function(){t.initialized?n():t.on("init",n)}},AE=function(t,n){var e,o=function(t){var n=t.settings,e=n.skin,o=n.skin_url;if(!1!==e){var r=e||"oxide";o=o?t.documentBaseURI.toAbsolute(o):_h.baseURL+"/skins/ui/"+r}return o}(n);o&&(e=o+"/skin.min.css",n.contentCSS.push(o+(t?"/content.inline":"/content")+".min.css")),0==(!1===n.getParam("skin"))&&e?Dh.DOM.styleSheetLoader.load(e,BE(n)):BE(n)()},DE=v(AE,!1),_E=v(AE,!0),ME=Dh.DOM,FE=function(t){return function(n){var e=t.outerContainer;ae("*",e.element()).forEach(function(t){e.getSystem().getByDom(t).each(function(t){t.hasConfigured(mp)&&("readonly"===n.mode?mp.disable(t):mp.enable(t))})})}},IE={render:function(r,i,n,u,t){var e,o;DE(r),e=Xn.fromDom(t.targetNode),o=i.mothership,ys(e,o,he),bs(zr(),i.uiMothership),r.on("init",function(){kE.setToolbar(i.outerContainer,nE(r,n,{backstage:u})),kE.setMenubar(i.outerContainer,qO(r,n)),kE.setSidebar(i.outerContainer,n.sidebar),r.readonly&&FE(i)({mode:"readonly"});var e=Tt($u(0,0)),o=r.contentWindow,t=function(){var t=e.get();if(t.left()!==o.innerWidth||t.top()!==o.innerHeight){var n=$u(o.innerWidth,o.innerHeight);e.set(n),TE(r)}};ME.bind(o,"resize",t),r.on("remove",function(){ME.unbind(o,"resize",t)})});var a=kE.getSocket(i.outerContainer).getOrDie("Could not find expected socket element");r.on("SwitchMode",FE(i)),r.getParam("readonly",!1,"boolean")&&r.setMode("readonly"),r.addCommand("ToggleSidebar",function(t,n){kE.toggleSidebar(i.outerContainer,n),r.fire("ToggleSidebar")}),r.addQueryValueHandler("ToggleSidebar",function(){return kE.whichSidebar(i.outerContainer)});var c=Vh(r);return r.on("ResizeContent",function(){c&&kE.getToolbar(i.outerContainer).each(Ak.refresh)}),{iframeContainer:a.element().dom(),editorContainer:i.outerContainer.element().dom()}},getBehaviours:function(t){return[]}},VE=function(e,n){return de(e).orThunk(function(){var t=Xn.fromTag("span");pe(e,t);var n=de(t);return we(t),n}).map(function(t){return Zu(t).translate(-n.left(),-n.top())}).getOrThunk(function(){return $u(0,0)})},RE=gt([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),HE=function(n){return function(t){return t.translate(-n.left(),-n.top())}},NE=function(n){return function(t){return t.translate(n.left(),n.top())}},PE=function(e){return function(t,n){return V(e,function(t,n){return n(t)},$u(t,n))}},zE=function(t,n,e){return t.fold(PE([NE(e),HE(n)]),PE([HE(n)]),PE([]))},LE=function(t,n,e){return t.fold(PE([NE(e)]),PE([]),PE([NE(n)]))},jE=function(t,n,e){return t.fold(PE([]),PE([HE(e)]),PE([NE(n),HE(e)]))},UE=function(t,n,e){return t.fold(function(t,n){return{position:"absolute",left:t+"px",top:n+"px"}},function(t,n){return{position:"absolute",left:t-e.left()+"px",top:n-e.top()+"px"}},function(t,n){return{position:"fixed",left:t+"px",top:n+"px"}})},WE=RE.offset,GE=RE.absolute,XE=RE.fixed,YE=function(t,n){zi(t.element(),n.transitionClass),ji(t.element(),n.fadeOutClass),zi(t.element(),n.fadeInClass)},qE=function(t,n){zi(t.element(),n.transitionClass),ji(t.element(),n.fadeInClass),zi(t.element(),n.fadeOutClass)},KE=function(t,n){return t.y()>=n.y()&&t.bottom()<=n.bottom()},JE=function(t,n){return Fe(t,n)?tt.some(parseInt(Me(t,n),10)):tt.none()},$E=function(o,r,i){return(u=o,t=r,n=u.element(),JE(n,t.leftAttr).bind(function(o){return JE(n,t.topAttr).map(function(t){var n=ua(u.element()),e=sa(u.element());return ka(o,t,n,e)})})).bind(function(t){return KE(t,i)?(n=r,e=o.element(),Ie(e,n.leftAttr),Ie(e,n.topAttr),tt.some(GE(t.x(),t.y()))):tt.none();var n,e});var u,t,n},QE=function(t,n,e,o,r){var i=Zu(t.element()),u=ka(i.left(),i.top(),ua(t.element()),sa(t.element()));if(KE(u,e))return tt.none();a=t,c=n,s=i.left(),f=i.top(),l=a.element(),De(l,c.leftAttr,s),De(l,c.topAttr,f);var a,c,s,f,l,d=GE(i.left(),i.top()),m=zE(d,o,r),g=GE(e.x(),e.y()),p=zE(g,o,r),h=u.y()<=e.y()?p.top():p.top()+e.height()-u.height();return tt.some(XE(m.left(),h))},ZE=function(i,t,n){var u=t.lazyViewport(i);t.contextual.each(function(r){r.lazyContext(i).each(function(t){var n,e,o=Oa(t);e=u,((n=o).y()<e.bottom()&&n.bottom()>e.y()?YE:qE)(i,r)})});var e,o,r,a,c,s=se(i.element()),f=na(s),l=VE(i.element(),f);(e=i,o=t,r=u,a=f,c=l,Zi(e.element(),"position").is("fixed")?$E(e,o,r):QE(e,o,r,a,c)).each(function(t){var n=UE(t,0,l);Ji(i.element(),n)})},tT=/* */Object.freeze({refresh:ZE}),nT=/* */Object.freeze({events:function(o,t){return Xr([Kr(Gt(),function(n,e){o.contextual.each(function(t){ce(n.element(),e.event().target())&&(ji(n.element(),t.transitionClass),e.stop())})}),Kr(In(),function(t,n){ZE(t,o)})])}}),eT=[hr("contextual",[rr("fadeInClass"),rr("fadeOutClass"),rr("transitionClass"),rr("lazyContext")]),vr("lazyViewport",function(t){var n=na();return ka(n.left(),n.top(),h.window.innerWidth,h.window.innerHeight)}),rr("leftAttr"),rr("topAttr")],oT=ju({fields:eT,name:"docking",active:nT,apis:tT}),rT={render:function(e,o,t,n,r){var i,u=Dh.DOM;_E(e);var a=Vh(e),c=function(){var t=kE.getToolbar(o.outerContainer);if(a&&t.each(Ak.refresh),!Zi(i.element(),"position").is("fixed")){var n=a?t.fold(function(){return 0},function(t){return sa(t.components()[1].element())}):0;Ji(i.element(),function(t){void 0===t&&(t=0);var n=Zu(Xn.fromDom(e.getBody()));return{top:Math.round(n.top()-sa(i.element()))+t+"px",left:Math.round(n.left())+"px"}}(n))}oT.refresh(i)},s=function(){Ki(o.outerContainer.element(),"display","flex"),u.addClass(e.getBody(),"mce-edit-focus"),c(),oT.refresh(i)},f=function(){o.outerContainer&&(Ki(o.outerContainer.element(),"display","none"),u.removeClass(e.getBody(),"mce-edit-focus"))},l=function(){i?s():(i=o.outerContainer,bs(zr(),o.mothership),bs(zr(),o.uiMothership),kE.setToolbar(o.outerContainer,nE(e,t,{backstage:n})),kE.setMenubar(o.outerContainer,qO(e,t)),Ki(i.element(),"position","absolute"),c(),s(),e.on("nodeChange ResizeWindow",c),e.on("activate",s),e.on("deactivate",f),e.nodeChanged())};return e.on("focus",l),e.on("blur hide",f),e.on("init",function(){e.hasFocus()&&l()}),{editorContainer:o.outerContainer.element().dom()}},getBehaviours:function(n){return[oT.config({leftAttr:"data-dock-left",topAttr:"data-dock-top",contextual:{lazyContext:function(t){return tt.from(n).map(function(t){return Xn.fromDom(t.getBody())})},fadeInClass:"tox-toolbar-dock-fadein",fadeOutClass:"tox-toolbar-dock-fadeout",transitionClass:"tox-toolbar-dock-transition"}}),hm.config({})]}},iT=function(t,n){return{anchor:"makeshift",x:t,y:n}},uT=function(t,n){var e,o,r,i=Dh.DOM.getPos(t);return e=n,o=i.x,r=i.y,iT(e.x+o,e.y+r)},aT=function(t,n){return"contextmenu"===n.type?t.inline?iT((o=n).pageX,o.pageY):uT(t.getContentAreaContainer(),iT((e=n).clientX,e.clientY)):cT(t);var e,o},cT=function(t){return{anchor:"selection",root:Xn.fromDom(t.selection.getNode())}},sT=function(t){return"string"==typeof t?t.split(/[ ,]/):t},fT=function(t){return t.settings.contextmenu_never_use_native||!1},lT=function(t){return e="contextmenu",o="link image imagetools table spellchecker configurepermanentpen",r=(n=t).ui.registry.getAll().contextMenus,ut(n.settings,e).map(sT).getOrThunk(function(){return F(sT(o),function(t){return at(r,t)})});var n,e,o,r},dT=function(t){return w(t)?"|"===t:"separator"===t.type},mT={type:"separator"},gT=function(n){if(w(n))return n;switch(n.type){case"separator":return mT;case"submenu":return{type:"nestedmenuitem",text:n.text,icon:n.icon,getSubmenuItems:function(){var t=n.getSubmenuItems();return w(t)?t:_(t,gT)}};default:return{type:"menuitem",text:n.text,icon:n.icon,onAction:(e=n.onAction,function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e()})}}var e},pT=function(t,n){if(0===n.length)return t;var e=q(t).filter(function(t){return!dT(t)}).fold(function(){return[]},function(t){return[mT]});return t.concat(e).concat(n).concat([mT])},hT=function(d,t,m){var g=gu($m.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return d.focus()},fireDismissalEventInstead:{},inlineBehaviours:zu([Rm("dismissContextMenu",[Kr(Nn(),function(t,n){As.close(t),d.focus()})])])}));d.on("init",function(){d.on("contextmenu",function(n){if(t=d,!n.ctrlKey||fT(t)){var t,e,r,i,o,u=2!==n.button||n.target===d.getBody(),a=u?(e=d,{anchor:"node",node:tt.some(Xn.fromDom(e.selection.getNode())),root:Xn.fromDom(e.getBody())}):aT(d,n),c=d.ui.registry.getAll(),s=lT(d),f=u?d.selection.getStart(!0):n.target,l=(r=c.contextMenus,i=f,0<(o=V(s,function(t,n){if(at(r,n)){var e=r[n].update(i);if(w(e))return pT(t,e.split(" "));if(0<e.length){var o=_(e,gT);return pT(t,o)}return t}return t.concat([n])},[])).length&&dT(o[o.length-1])&&o.pop(),o);Hv(l,Sp.CLOSE_ON_EXECUTE,m.providers).map(function(t){n.preventDefault(),$m.showMenuAt(g,a,{menu:{markers:zp("normal")},data:t})})}})})},vT=function(t){return/^[0-9\.]+(|px)$/i.test(""+t)?tt.some(parseInt(t,10)):tt.none()},bT=function(t){return O(t)?t+"px":t},yT="data-initial-z-index",xT=function(t,n){var e;t.getSystem().addToGui(n),le((e=n).element()).each(function(n){Zi(n,"z-index").each(function(t){De(n,yT,t)}),Ki(n,"z-index",$i(e.element(),"z-index"))})},wT=function(t){le(t.element()).each(function(t){var n=Me(t,yT);Fe(t,yT)?Ki(t,"z-index",n):nu(t,"z-index"),Ie(t,yT)}),t.getSystem().removeFromGui(t)},ST=function(t,n,e,o){return(r=t,i=n,u=r.element(),a=parseInt(Me(u,i.leftAttr),10),c=parseInt(Me(u,i.topAttr),10),isNaN(a)||isNaN(c)?tt.none():tt.some($u(a,c))).fold(function(){return e},function(t){return XE(t.left()+o.left(),t.top()+o.top())});var r,i,u,a,c},CT=function(t,n,e,o,r,i){var u,a,c,s=ST(t,n,e,o),f=OT(t,n,s,r,i),l=zE(s,r,i);return u=n,a=l,c=t.element(),De(c,u.leftAttr,a.left()+"px"),De(c,u.topAttr,a.top()+"px"),f.fold(function(){return{coord:XE(l.left(),l.top()),extra:tt.none()}},function(t){return{coord:t.output(),extra:t.extra()}})},kT=function(t,n){var e,o;e=n,o=t.element(),Ie(o,e.leftAttr),Ie(o,e.topAttr)},OT=function(t,n,p,h,v){var e=n.getSnapPoints(t);return At(e,function(t){var n,e,o,r,i,u,a,c,s,f,l,d,m,g=t.sensor();return n=p,e=g,o=t.range().left(),r=t.range().top(),a=LE(n,i=h,u=v),c=LE(e,i,u),Math.abs(a.left()-c.left())<=o&&Math.abs(a.top()-c.top())<=r?tt.some({output:Z((s=t.output(),f=p,l=h,d=v,m=function(o,r){return function(t,n){var e=o(f,l,d);return r(t.getOr(e.left()),n.getOr(e.top()))}},s.fold(m(jE,RE.offset),m(LE,RE.absolute),m(zE,RE.fixed)))),extra:t.extra}):tt.none()})},ET=function(e,t,i,u,a,c){return t.fold(function(){var t,e,o,n=(t=i,e=c.left(),o=c.top(),t.fold(function(t,n){return RE.offset(t+e,n+o)},function(t,n){return RE.absolute(t+e,n+o)},function(t,n){return RE.fixed(t+e,n+o)})),r=zE(n,u,a);return XE(r.left(),r.top())},function(n){var t=CT(e,n,i,c,u,a);return t.extra.each(function(t){n.onSensor(e,t)}),t.coord})},TT=function(t,n,e){var o,r=n.getTarget(t.element());if(n.repositionTarget){var i=se(t.element()),u=na(i),a=VE(r,u),c=Zi(o=r,"left").bind(function(e){return Zi(o,"top").bind(function(n){return Zi(o,"position").map(function(t){return("fixed"===t?XE:WE)(parseInt(e,10),parseInt(n,10))})})}).getOrThunk(function(){var t=Zu(o);return GE(t.left(),t.top())}),s=ET(t,n.snaps,c,u,a,e),f=UE(s,0,a);Ji(r,f)}n.onDrag(t,r,e)},BT=hr("snaps",[rr("getSnapPoints"),Bu("onSensor"),rr("leftAttr"),rr("topAttr"),vr("lazyViewport",function(){var t=na();return{x:t.left,y:t.top,width:Z(h.window.innerWidth),height:Z(h.window.innerHeight),bottom:Z(t.top()+h.window.innerHeight),right:Z(t.left()+h.window.innerWidth)}})]),AT=/* */Object.freeze({getData:function(t){return tt.from($u(t.x(),t.y()))},getDelta:function(t,n){return $u(n.left()-t.left(),n.top()-t.top())}}),DT=[vr("useFixed",!1),rr("blockerClass"),vr("getTarget",d),vr("onDrag",Q),vr("repositionTarget",!0),Bu("onDrop"),BT,Mu("dragger",{handlers:function(a,c){return Xr([Kr(Ft(),function(n,t){if(0===t.event().raw().button){t.stop();var e,o={drop:function(){i()},delayDrop:function(){u.schedule()},forceDrop:function(){i()},move:function(t){u.cancel(),c.update(AT,t).each(function(t){TT(n,a,t)})}},r=n.getSystem().build(Bh.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[a.blockerClass]},events:(e=o,Xr([Kr(Ft(),e.forceDrop),Kr(Rt(),e.drop),Kr(It(),function(t,n){e.move(n.event())}),Kr(Vt(),e.delayDrop)]))})),i=function(){wT(r),a.snaps.each(function(t){kT(n,t)});var t=a.getTarget(n.element());a.onDrop(n,t)},u=fh(i,200);c.reset(),xT(n,r)}})])}})],_T=/* */Object.freeze({getData:function(t){var n,e=t.raw().touches;return 1===e.length?(n=e[0],tt.some($u(n.clientX,n.clientY))):tt.none()},getDelta:function(t,n){return $u(n.left()-t.left(),n.top()-t.top())}}),MT=DT,FT=[vr("useFixed",!1),vr("getTarget",d),vr("onDrag",Q),vr("repositionTarget",!0),vr("onDrop",Q),BT,Mu("dragger",{handlers:function(o,r){return Xr([ni(Dt()),Kr(_t(),function(n,t){t.stop(),r.update(_T,t.event()).each(function(t){TT(n,o,t)})}),Kr(Mt(),function(n,t){o.snaps.each(function(t){kT(n,t)});var e=o.getTarget(n.element());r.reset(),o.onDrop(n,e)})])}})],IT=/* */Object.freeze({mouse:MT,touch:FT}),VT=/* */Object.freeze({init:function(){var i=tt.none(),t=Z({});return Oi({readState:t,reset:function(){i=tt.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=tt.some(e),o;var n,e,o})}})}}),RT=Wu({branchKey:"mode",branches:IT,name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:Jn(["sensor","range","output"],["extra"])},state:VT});(IO=FO||(FO={}))[IO.None=0]="None",IO[IO.Both=1]="Both",IO[IO.Vertical=2]="Vertical";var HT,NT,PT,zT,LT,jT=function(t,n,e,o){var r=t+n,i=e.filter(function(t){return r<t}),u=o.filter(function(t){return t<r});return i.or(u).getOr(r)},UT=function(t,n,e,o,r){var i,u,a={};return a.height=jT(o,n.top(),Fh(t),(i=t,tt.from(i.getParam("max_height")).filter(O))),e===FO.Both&&(a.width=jT(r,n.left(),Mh(t),(u=t,tt.from(u.getParam("max_width")).filter(O)))),a},WT=function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1},GT=function(i,u){return u.delimiter||(u.delimiter="\xbb"),{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:zu([Yd.config({mode:"flow",selector:"div[role=button]"}),nb.config({}),Qd.config({}),Rm("elementPathEvents",[oi(function(r,t){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Yd.focusIn(r)}),i.on("nodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!WT(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length&&Qd.set(r,(n=_(e||[],function(n,t){return Zm.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){i.focus(),i.selection.select(n.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+u.delimiter+" "}},V(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]])))})})])]),components:[]}},XT=function(s,t){var n,e,o,r,i,u,a,c,f,l,d,m=function(c){return{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},innerHtml:eg("resize-handle",t.icons)},behaviours:zu([RT.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){var o,r,i,u,a;o=s,r=e,i=c,u=Xn.fromDom(o.getContainer()),a=UT(o,r,i,sa(u),ua(u)),nt(a,function(t,n){return Ki(u,n,bT(t))}),EE(o)},blockerClass:"tox-blocker"})])}};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(d=[],s.getParam("elementpath",!0,"boolean")&&d.push(GT(s,{})),mn(s.settings.plugins,"wordcount")&&d.push((u=s,a=t,f=function(t,n,e){return Qd.set(t,[lu(a.translate(["{0} "+e,n[e]]))])},Zm.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:zu([nb.config({}),Qd.config({}),lm.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Rm("wordcount-events",[Kr(Wt(),function(t){var n=lm.getValue(t),e="words"===n.mode?"characters":"words";lm.setValue(t,{mode:e,count:n.count}),f(t,n.count,e)}),oi(function(e){u.on("wordCountUpdate",function(t){var n=lm.getValue(e).mode;lm.setValue(e,{mode:n,count:t.wordCount}),f(e,t.wordCount,n)})})])]),eventOrder:(c={},c[Wt()]=["wordcount-events","alloy.base.behaviour"],c)}))),s.getParam("branding",!0,"boolean")&&d.push((l='<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+Hg.translate(["Powered by {0}","Tiny"])+'">Tiny</a>',{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:Hg.translate(["Powered by {0}",l])}})),r=0<d.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:d}]:[],e=!mn((n=s).settings.plugins,"autoresize"),i=!1===(o=n.getParam("resize",e))?FO.None:"both"===o?FO.Both:FO.Vertical,i!==FO.None&&r.push(m(i)),r)}},YT=function(d){var t,n,e=d.getParam("inline",!1,"boolean"),m=e?rT:IE,o=tt.none(),r=Hg.isRtl()?{attributes:{dir:"rtl"}}:{},g=gu({dom:Je({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"]},r),behaviours:zu([fs.config({useFixed:!1})])}),i=tg({dom:{tag:"div",classes:["tox-anchorbar"]}}),p=JC(g,d,function(){return o.bind(function(t){return i.getOpt(t)}).getOrDie("Could not find a toolbar element")}),h=function(){return mt.value(g)},u=kE.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},getSink:h,providers:p.shared.providers,onEscape:function(){d.focus()}}),a=kE.parts().toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:h,backstage:p,onEscape:function(){d.focus()},split:Vh(d)}),c=kE.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),s=kE.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),f=d.getParam("statusbar",!0,"boolean")&&!e?tt.some(XT(d,p.shared.providers)):tt.none(),l={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[c,s]},v=(n=(t=d).getParam("toolbar"),(E(n)?0<n.length:!1!==t.getParam("toolbar",!0,"boolean"))||Ih(d).isSome()),b=!1!==d.getParam("menubar",!0,"boolean"),y=z([b?[u]:[],v?[a]:[],[i.asSpec()],e?[]:[l]]),x=z([[{dom:{tag:"div",classes:["tox-editor-container"]},components:y}],e?[]:f.toArray()]),w=Je({role:"application"},Hg.isRtl()?{dir:"rtl"}:{}),S=gu(kE.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(e?["tox-tinymce-inline"]:[]),styles:{visibility:"hidden"},attributes:w},components:x,behaviours:zu(m.getBehaviours(d).concat([Yd.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-sidebar--sliding-open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})]))}));o=tt.some(S),d.shortcuts.add("alt+F9","focus menubar",function(){kE.focusMenubar(S)}),d.shortcuts.add("alt+F10","focus toolbar",function(){kE.focusToolbar(S)});var C=Ah(S),k=Ah(g);oE(d,C,k);var O=function(t){var n,e=Dh.DOM,o=d.getParam("width",e.getStyle(t,"width")),r=(n=d).getParam("height",Math.max(n.getElement().offsetHeight,200)),i=Mh(d),u=Fh(d),a=vT(o).bind(function(n){return bT(i.map(function(t){return Math.max(n,t)}))}).getOr(bT(o)),c=vT(r).bind(function(n){return u.map(function(t){return Math.max(n,t)})}).getOr(r),s=bT(a);if(tu("div","width",s)&&Ki(S.element(),"width",s),!d.inline){var f=bT(c);tu("div","height",f)?Ki(S.element(),"height",f):Ki(S.element(),"height","200px")}return c};return{mothership:C,uiMothership:k,backstage:p,renderUI:function(){var o,r;hT(d,h,p.shared),r=(o=d).ui.registry.getAll().sidebars,M(J(r),function(n){var t=r[n],e=function(){return tt.from(o.queryCommandValue("ToggleSidebar")).is(n)};o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){var n=function(){return t.setActive(e())};return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})});var t=d.ui.registry.getAll(),n=t.buttons,e=t.menuItems,i=t.contextToolbars,u=t.sidebars,a={menuItems:e,buttons:n,menus:d.settings.menu?et(d.settings.menu,function(t){return bt(t,{items:t.items})}):{},menubar:d.settings.menubar,toolbar:Ih(d).getOr(d.getParam("toolbar",!0)),sidebar:u};eE(d,i,g,{backstage:p});var c=d.getElement(),s=O(c),f={mothership:C,uiMothership:k,outerContainer:S},l={targetNode:c,height:s};return m.render(d,f,a,p,l)},getUi:function(){return{channels:{broadcastAll:k.broadcast,broadcastOn:k.broadcastOn,register:function(){}}}}}},qT=function(o){VS.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){var e;o.ui.registry.addToggleButton(t.name,{tooltip:t.text,onAction:function(){return o.execCommand(t.cmd)},icon:t.icon,onSetup:(e=t,function(n){var t=function(t){n.setActive(t)};return o.formatter?(n.setActive(o.formatter.match(e.name)),o.formatter.formatChanged(e.name,t)):o.on("init",function(){n.setActive(o.formatter.match(e.name)),o.formatter.formatChanged(e.name,t)}),function(){}})})});var t="alignnone",n="No alignment",e="JustifyNone",r="align-none";o.ui.registry.addButton(t,{tooltip:n,onAction:function(){return o.execCommand(e)},icon:r})},KT=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},JT=function(e,o){return function(n){var t=function(t){n.setActive(t)};return e.formatter?(n.setActive(e.formatter.match(o)),e.formatter.formatChanged(o,t)):e.on("init",function(){n.setActive(e.formatter.match(o)),e.formatter.formatChanged(o,t)}),function(){}}},$T=function(t){var n,e;!function(n){VS.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:JT(n,t.name),onAction:KT(n,t.name)})});for(var t=1;t<=6;t++){var e="h"+t;n.ui.registry.addToggleButton(e,{text:e.toUpperCase(),tooltip:"Heading "+t,onSetup:JT(n,e),onAction:KT(n,e)})}}(t),n=t,VS.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return n.execCommand(t.action)}})}),e=t,VS.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return e.execCommand(t.action)},onSetup:JT(e,t.name)})})},QT=function(t){var n;$T(t),n=t,VS.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){n.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:function(){return n.execCommand(t.action)}})}),n.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:KT(n,"code")})},ZT=function(t,n,e){var o=function(){return!!n.undoManager&&n.undoManager[e]()},r=function(){t.setDisabled(n.readonly||!o())};return t.setDisabled(!o()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return n.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}},tB=function(t){var n,e;(n=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(t){return ZT(t,n,"hasUndo")},onAction:function(){return n.execCommand("undo")}}),n.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(t){return ZT(t,n,"hasRedo")},onAction:function(){return n.execCommand("redo")}}),(e=t).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(t){return ZT(t,e,"hasUndo")},onAction:function(){return e.execCommand("undo")}}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(t){return ZT(t,e,"hasRedo")},onAction:function(){return e.execCommand("redo")}})},nB=function(t){var n,e;(n=t).ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}}),(e=t).ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(t){return function(n,t){n.setActive(t.hasVisual);var e=function(t){n.setActive(t.hasVisual)};return t.on("VisualAid",e),function(){return t.off("VisualAid",e)}}(t,e)},onAction:function(){e.execCommand("mceToggleVisualAid")}})},eB=function(t){var n;(n=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(t){return function(t,n){t.setDisabled(!n.queryCommandState("outdent"));var e=function(){t.setDisabled(!n.queryCommandState("outdent"))};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}(t,n)},onAction:function(){return n.execCommand("outdent")}}),n.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return n.execCommand("indent")}})},oB=function(t,n){var e,o,r,i,u,a,c,s,f,l,d,m,g,p,h,v,b,y,x,w;o=n,r=HO(e=t),i=_O(0,o,r.dataset,r),e.ui.registry.addNestedMenuItem("align",{text:o.shared.providers.translate("Align"),getSubmenuItems:function(){return i.items.validateItems(i.getStyleItems())}}),a=n,c=PO(u=t),s=_O(0,a,c.dataset,c),u.ui.registry.addNestedMenuItem("fontformats",{text:a.shared.providers.translate("Fonts"),getSubmenuItems:function(){return s.items.validateItems(s.getStyleItems())}}),f=t,d=(l=n).styleselect,m=_O(0,l,d,WO(f)),f.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return m.items.validateItems(m.getStyleItems())}}),p=n,h=UO(g=t),v=_O(0,p,h.dataset,h),g.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return v.items.validateItems(v.getStyleItems())}}),y=n,x=LO(b=t),w=_O(0,y,x.dataset,x),b.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return w.items.validateItems(w.getStyleItems())}})},rB=function(t,n){qT(t),QT(t),oB(t,n),tB(t),Lb.register(t),nB(t),eB(t)},iB=function(t,n){var e=tt.from(Me(t,"id")).fold(function(){var t=qe("dialog-label");return De(n,"id",t),t},d);De(t,"aria-labelledby",e)},uB=Z([rr("lazySink"),dr("dragBlockClass"),vr("useTabstopAt",Z(!0)),vr("eventOrder",{}),Vs("modalBehaviours",[Yd]),Au("onExecute"),_u("onEscape")]),aB={sketch:d},cB=Z([df({name:"draghandle",overrides:function(t,n){return{behaviours:zu([RT.config({mode:"mouse",getTarget:function(t){return hu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+Oo(n,null,2)).message)})])}}}),ff({schema:[rr("dom")],name:"title"}),ff({factory:aB,schema:[rr("dom")],name:"close"}),ff({factory:aB,schema:[rr("dom")],name:"body"}),ff({factory:aB,schema:[rr("dom")],name:"footer"}),lf({factory:{sketch:function(t,n){return Je({},t,{dom:n.dom,components:n.components})}},schema:[vr("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),vr("components",[])],name:"blocker"})]),sB=Pf({name:"ModalDialog",configFields:uB(),partFields:cB(),factory:function(r,t,n,o){var a=qe("alloy.dialog.busy"),c=qe("alloy.dialog.idle"),s=zu([Yd.config({mode:"special",onTab:function(){return tt.some(!0)},onShiftTab:function(){return tt.some(!0)}}),hm.config({})]),e=qe("modal-events"),i=Je({},r.eventOrder,{"alloy.system.attached":[e].concat(r.eventOrder["alloy.system.attached"]||[])});return{uid:r.uid,dom:r.dom,components:t,apis:{show:function(i){var t=r.lazySink(i).getOrDie(),u=Tt(tt.none()),n=o.blocker(),e=t.getSystem().build(Je({},n,{components:n.components.concat([pu(i)]),behaviours:zu([Rm("dialog-blocker-events",[Kr(c,function(t,n){Fe(i.element(),"aria-busy")&&(Ie(i.element(),"aria-busy"),u.get().each(function(t){return Qd.remove(i,t)}))}),Kr(a,function(t,n){De(i.element(),"aria-busy","true");var e=n.event().getBusySpec();u.get().each(function(t){Qd.remove(i,t)});var o=e(i,s),r=t.getSystem().build(o);u.set(tt.some(r)),Qd.append(i,pu(r)),r.hasConfigured(Yd)&&Yd.focusIn(r)})])])}));ms(t,e),Yd.focusIn(i)},hide:function(n){le(n.element()).each(function(t){n.getSystem().getByDom(t).each(function(t){hs(t)})})},getBody:function(t){return Of(t,r,"body")},getFooter:function(t){return Of(t,r,"footer")},setIdle:function(t){Ar(t,c)},setBusy:function(t,n){Dr(t,a,{getBusySpec:n})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Hs(r.modalBehaviours,[Qd.config({}),Yd.config({mode:"cyclic",onEnter:r.onExecute,onEscape:r.onEscape,useTabstopAt:r.useTabstopAt}),Rm(e,[oi(function(t){var n,e,o;iB(t.element(),Of(t,r,"title").element()),n=t.element(),e=Of(t,r,"body").element(),o=tt.from(Me(n,"id")).fold(function(){var t=qe("dialog-describe");return De(e,"id",t),t},d),De(n,"aria-describedby",o)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),fB=[ur("type"),ur("text"),ar("level",["info","warn","error","success"]),ur("icon"),vr("url","")],lB=[ur("type"),ur("text"),Sr("primary",!1),zo("name","name",ro(function(){return qe("button-name")}),tr),gr("icon")],dB=[ur("type"),ur("name"),ur("label")],mB=nr,gB=[ur("type"),ur("name"),gr("label")],pB=gB,hB=tr,vB=gB,bB=tr,yB=gB,xB=Vo(Uo),wB=gB.concat([Sr("sandboxed",!0)]),SB=tr,CB=gB.concat([gr("placeholder")]),kB=tr,OB=gB.concat([fr("items",[ur("text"),ur("value")]),yr("size",1)]),EB=tr,TB=gB.concat([Sr("constrain",!0)]),BB=Io([ur("width"),ur("height")]),AB=gB.concat([gr("placeholder")]),DB=tr,_B=gB.concat([wr("filetype","file",["image","media","file"])]),MB=Io([ur("value"),vr("meta",{})]),FB=gB.concat([ur("type"),xr("tag","textarea"),cr("init")]),IB=tr,VB=[ur("type"),ur("html"),wr("presets","presentation",["presentation","document"])],RB=gB.concat([ir("currentState",Io([rr("blob"),ur("url")]))]),HB=gB.concat([vr("columns","auto")]),NB=(HT=[ur("value"),gr("text"),gr("icon")],No(HT)),PB=[ur("type"),lr("header",tr),lr("cells",Vo(tr))],zB=function(n){return zo("items","items",eo(),Vo(Wo(function(t){return Xo("Checking item of "+n,LB,t).fold(function(t){return mt.error(Ko(t))},function(t){return mt.value(t)})})))},LB=Jo("type",{alertbanner:fB,bar:(LT=zB("bar"),[ur("type"),LT]),button:lB,checkbox:dB,colorinput:pB,colorpicker:vB,dropzone:yB,grid:(PT=zB("grid"),[ur("type"),(zT="columns",ir(zT,Zo)),PT]),iframe:wB,input:CB,selectbox:OB,sizeinput:TB,textarea:AB,urlinput:_B,customeditor:FB,htmlpanel:VB,imagetools:RB,collection:HB,label:(NT=zB("label"),[ur("type"),ur("label"),NT]),table:PB}),jB=[ur("type"),lr("items",LB)],UB=[ur("title"),lr("items",LB)],WB=[ur("type"),fr("tabs",UB)],GB=Io([ar("type",["submit","cancel","custom"]),zo("name","name",ro(function(){return qe("button-name")}),tr),ur("text"),gr("icon"),wr("align","end",["start","end"]),Sr("primary",!1),Sr("disabled",!1)]),XB=Io([ur("title"),ir("body",Jo("type",{panel:jB,tabpanel:WB})),xr("size","normal"),lr("buttons",GB),vr("initialData",{}),Cr("onAction",Q),Cr("onChange",Q),Cr("onSubmit",Q),Cr("onClose",Q),Cr("onCancel",Q),vr("onTabChange",Q)]),YB=function(t){return S(t)?[t].concat(L(it(t),YB)):E(t)?L(t,YB):[]},qB=function(t){return w(t.type)&&w(t.name)},KB={checkbox:mB,colorinput:hB,colorpicker:bB,dropzone:xB,input:kB,iframe:SB,sizeinput:BB,selectbox:EB,size:BB,textarea:DB,urlinput:MB,customeditor:IB,collection:NB},JB=function(t){var n=L(F(YB(t),qB),function(n){return(t=n,tt.from(KB[t.type])).fold(function(){return[]},function(t){return[ir(n.name,t)]});var t});return Io(n)},$B=function(t){return{internalDialog:Yo(Xo("dialog",XB,t)),dataValidator:JB(t),initialData:t.initialData}},QB={open:function(t,n){var e=$B(n);return t(e.internalDialog,e.initialData,e.dataValidator)},redial:function(t){return $B(t)}},ZB=qe("update-dialog"),tA=qe("update-title"),nA=qe("update-body"),eA=qe("update-footer"),oA=function(t){var e=[],o={};return nt(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?mt.error(e):mt.value(o)},rA=Nf({name:"TabButton",configFields:[vr("uid",undefined),rr("value"),zo("dom","dom",io(function(t){return{attributes:{role:"tab",id:qe("aria"),"aria-selected":"false"}}}),$o()),dr("action"),vr("domModification",{}),Vs("tabButtonBehaviours",[hm,Yd,lm]),rr("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:Qm(t.action),behaviours:Hs(t.tabButtonBehaviours,[hm.config({}),Yd.config({mode:"execution",useSpace:!0,useEnter:!0}),lm.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),iA=Z([rr("tabs"),rr("dom"),vr("clickToDismiss",!1),Vs("tabbarBehaviours",[el,Yd]),Eu(["tabClass","selectedClass"])]),uA=mf({factory:rA,name:"tabs",unit:"tab",overrides:function(o,t){var r=function(t,n){el.dehighlight(t,n),Dr(t,jn(),{tabbar:t,button:n})},i=function(t,n){el.highlight(t,n),Dr(t,Ln(),{tabbar:t,button:n})};return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=el.isHighlighted(n,t);(e&&o.clickToDismiss?r:e?Q:i)(n,t)},domModification:{classes:[o.markers.tabClass]}}}}),aA=Z([uA]),cA=Pf({name:"Tabbar",configFields:iA(),partFields:aA(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Hs(t.tabbarBehaviours,[el.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){De(n.element(),"aria-selected","true")},onDehighlight:function(t,n){De(n.element(),"aria-selected","false")}}),Yd.config({mode:"flow",getInitial:function(t){return el.getHighlighted(t).map(function(t){return t.element()})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),sA=Nf({name:"Tabview",configFields:[Vs("tabviewBehaviours",[Qd])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:Hs(t.tabviewBehaviours,[Qd.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),fA=Z([vr("selectFirst",!0),Bu("onChangeTab"),Bu("onDismissTab"),vr("tabs",[]),Vs("tabSectionBehaviours",[])]),lA=ff({factory:cA,schema:[rr("dom"),sr("markers",[rr("tabClass"),rr("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),dA=ff({factory:sA,name:"tabview"}),mA=Z([lA,dA]),gA=Pf({name:"TabSection",configFields:fA(),partFields:mA(),factory:function(i,t,n,e){var o=function(t,n){kf(t,i,"tabbar").each(function(t){n(t).each(_r)})};return{uid:i.uid,dom:i.dom,components:t,behaviours:Rs(i.tabSectionBehaviours),events:Xr(z([i.selectFirst?[oi(function(t,n){o(t,el.getFirst)})]:[],[Kr(Ln(),function(t,n){var o,r,e=n.event().button();o=e,r=lm.getValue(o),kf(o,i,"tabview").each(function(e){R(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();De(e.element(),"aria-labelledby",Me(o.element(),"id")),Qd.set(e,n),i.onChangeTab(e,o,n)})})}),Kr(jn(),function(t,n){var e=n.event().button();i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return kf(t,i,"tabview").map(function(t){return Qd.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){var t=el.getCandidates(n);return R(t,function(t){return lm.getValue(t)===e}).filter(function(t){return!el.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),pA=function(t){return Y((n=t,e=function(t,n){return n<t?-1:t<n?1:0},(o=U.call(n,0)).sort(e),o));var n,e,o},hA=function(i,u,t){hu(i,'[role="dialog"]').each(function(r){t.get().map(function(t){return Ki(u,"height","0"),Math.min(t,(e=i,o=hu(n=r,".tox-dialog-wrap").getOr(n),("fixed"===$i(o,"position")?Math.max(h.document.documentElement.clientHeight,h.window.innerHeight):Math.max(h.document.documentElement.offsetHeight,h.document.documentElement.scrollHeight))-(n.dom().getBoundingClientRect().height-e.dom().getBoundingClientRect().height)));var n,e,o}).each(function(t){Ki(u,"height",t+"px")})})},vA=function(a){var c;return{smartTabHeight:(c=Tt(tt.none()),{extraEvents:[oi(function(t){vu(t.element(),'[role="tabpanel"]').each(function(u){var n;Ki(u,"visibility","hidden"),t.getSystem().getByDom(u).toOption().each(function(t){var o,r,i,n=(r=u,i=t,_(o=a,function(t,n){Qd.set(i,o[n].view());var e=r.dom().getBoundingClientRect();return Qd.set(i,[]),e.height})),e=pA(n);c.set(e)}),hA(t.element(),u,c),nu(u,"visibility"),n=t,Y(a).each(function(t){return gA.showTab(n,t.value)}),$C.requestAnimationFrame(function(){hA(t.element(),u,c)})})}),Kr(Vn(),function(n){vu(n.element(),'[role="tabpanel"]').each(function(t){hA(n.element(),t,c)})}),Kr(Wh,function(r,t){vu(r.element(),'[role="tabpanel"]').each(function(n){var t=gl();Ki(n,"visibility","hidden");var e=Zi(n,"height").map(function(t){return parseInt(t,10)});nu(n,"height");var o=n.dom().getBoundingClientRect().height;e.forall(function(t){return t<o})?(c.set(tt.from(o)),hA(r.element(),n,c)):e.each(function(t){Ki(n,"height",t+"px")}),nu(n,"visibility"),t.each(ml)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}},bA="send-data-to-section",yA="send-data-to-view",xA=function(t,d){return{dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[],behaviours:zu([Zx(0),oO.config({channel:nA,updateState:function(t,n){return tt.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){switch(t.body.type){case"tabpanel":return[(r={tabs:t.body.tabs},i=d,u=Tt({}),a=function(t){var n=lm.getValue(t),e=oA(n).getOr({}),o=u.get(),r=vt(o,e);u.set(r)},c=function(t){var n=u.get();lm.setValue(t,n)},s=Tt(null),f=_(r.tabs,function(t){return{value:t.title,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:i.shared.providers.translate(t.title)},view:function(){return[Ux.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:_(t.items,function(t){return mC(n,t,i)}),formBehaviours:zu([Yd.config({mode:"acyclic",useTabstopAt:m(gw)}),Rm("TabView.form.events",[oi(c),ri(a)]),qu.config({channels:kt([{key:bA,value:{onReceive:a}},{key:yA,value:{onReceive:c}}])})])}})]}}}),l=vA(f).smartTabHeight,gA.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=lm.getValue(n);Dr(t,Uh,{title:o,oldTitle:s.get()}),s.set(o)},tabs:f,components:[gA.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[cA.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:zu([nb.config({})])}),gA.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:zu([Rm("tabpanel",l.extraEvents),Yd.config({mode:"acyclic"}),Uf.config({find:function(t){return Y(gA.getViewItems(t))}}),lm.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([bA],{}),u.get()},setValue:function(t,n){u.set(n),t.getSystem().broadcastOn([yA],{})}}})])}))];default:return[(e={items:t.body.items},o=d,n=tg(Ux.sketch(function(n){return{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:_(e.items,function(t){return mC(n,t,o)})}})),{dom:{tag:"div",classes:["tox-dialog__body"]},components:[n.asSpec()],behaviours:zu([Yd.config({mode:"acyclic",useTabstopAt:m(gw)}),Qx(n),iw(n,{postprocess:function(t){return oA(t).fold(function(t){return console.error(t),{}},function(t){return t})}})])})]}var e,o,n,r,i,u,a,c,s,f,l},initialData:t})])}},wA=function(o,e){var t=function(t,r){return Kr(t,function(e,o){n(e,function(t,n){r(t,o.event(),e)})})},n=function(n,e){oO.getState(n).get().each(function(t){e(t.internalDialog,n)})};return[Zr(Nt(),pw),t(zh,function(t){return t.onSubmit(o())}),t(Rh,function(t,n){t.onChange(o(),{name:n.name()})}),t(Ph,function(t,n){t.onAction(o(),{name:n.name(),value:n.value()})}),t(Uh,function(t,n){t.onTabChange(o(),n.title())}),t(Hh,function(t){e.onClose(),t.onClose()}),t(Nh,function(t,n,e){t.onCancel(o()),Ar(e,Hh)}),ri(function(t){var n=o();lm.setValue(t,n.getData())}),Kr(jh,function(t,n){return e.onUnblock()}),Kr(Lh,function(t,n){return e.onBlock(n.event())})]},SA=function(t,n){var e=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r,t)?e:o).push(u)}return{pass:e,fail:o}}(n.map(function(t){return t.footerButtons}).getOr([]),function(t){return"start"===t.align}),o=function(t,n){return Bh.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:_(n,function(t){return t.memento.asSpec()})})};return[o("start",e.pass),o("end",e.fail)]},CA=function(t,i){return{dom:Rg('<div class="tox-dialog__footer"></div>'),components:[],behaviours:zu([oO.config({channel:eA,initialData:t,updateState:function(t,n){var r=_(n.buttons,function(t){var n,e,o=tg((e=i,hS(n=t,n.type,e)));return{name:t.name,align:t.align,memento:o}});return tt.some({lookupByName:function(t,n){return e=t,o=n,R(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:SA})])}},kA=function(t){return Zm.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(t){Ar(t,Nh)}})},OA=function(t,n,e){var o=function(t){return[lu(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:Je({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:zu([oO.config({channel:tA,renderComponents:o})])}},EA=function(n,e){if(n.getRoot().getSystem().isConnected()){var o=Uf.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return Ux.getField(o,e).fold(function(){var t=n.getFooter();return oO.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return tt.some(t)})}return tt.none()},TA=function(c,o){var t=function(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)},s={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t;return lm.getValue(n)},setData:function(a){t(function(t){var n,e,o=s.getData(),r=bt(o,a),i=(n=r,e=c.getRoot(),oO.getState(e).get().map(function(t){return Yo(Xo("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();lm.setValue(u,i)})},disable:function(t){EA(c,t).each(mp.disable)},enable:function(t){EA(c,t).each(mp.enable)},focus:function(t){EA(c,t).each(hm.focus)},block:function(n){if(!w(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){Dr(t,Lh,{message:n})})},unblock:function(){t(function(t){Ar(t,jh)})},showTab:function(e){t(function(t){var n=c.getBody();oO.getState(n).get().exists(function(t){return t.isTabPanel()})&&Uf.getCurrent(n).each(function(t){gA.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([ZB],n),t.getSystem().broadcastOn([tA],n.internalDialog),t.getSystem().broadcastOn([nA],n.internalDialog),t.getSystem().broadcastOn([eA],n.internalDialog),s.setData(n.initialData)})},close:function(){t(function(t){Ar(t,Hh)})}};return s},BA=function(t,n,e){var o,r,i,u,a,c,s,f,l,d,m,g=(r={title:e.shared.providers.translate(t.internalDialog.title),draggable:!0},i=e.shared.providers,u=sB.parts().title(OA(r,tt.none(),i)),a=sB.parts().draghandle({dom:Rg('<div class="tox-dialog__draghandle"></div>')}),c=sB.parts().close(kA(i)),s=[u].concat(r.draggable?[a]:[]).concat([c]),Bh.sketch({dom:Rg('<div class="tox-dialog__header"></div>'),components:s})),p=(f={body:t.internalDialog.body},l=e,sB.parts().body(xA(f,l))),h=(d={buttons:t.internalDialog.buttons},m=e.shared.providers,sB.parts().footer(CA(d,m))),v=wA(function(){return x},{onClose:function(){return n.closeWindow()},onBlock:function(e){sB.setBusy(y,function(t,n){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:Rg('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){sB.setIdle(y)}}),b="normal"!==t.internalDialog.size?"large"===t.internalDialog.size?"tox-dialog--width-lg":"tox-dialog--width-md":[],y=gu(sB.sketch({lazySink:e.shared.getSink,onEscape:function(t){return Ar(t,Nh),tt.some(!0)},useTabstopAt:function(t){return!gw(t)&&("button"!==ke(t)||"disabled"!==Me(t,"disabled"))},modalBehaviours:zu([oO.config({channel:ZB,updateState:function(t,n){return tt.some(n)},initialData:t}),hm.config({}),Rm("execute-on-form",v.concat([ei(Nt(),function(t,n){Yd.focusIn(t)})])),Rm("scroll-lock",[oi(function(){zi(zr(),"tox-dialog__disable-scroll")}),ri(function(){ji(zr(),"tox-dialog__disable-scroll")})]),cw({})]),eventOrder:(o={},o[En()]=["execute-on-form"],o[Rn()]=["scroll-lock","reflecting","execute-on-form","alloy.base.behaviour"],o[Hn()]=["alloy.base.behaviour","execute-on-form","reflecting","scroll-lock"],o),dom:{tag:"div",classes:["tox-dialog"].concat(b),styles:{position:"relative"}},components:[g,p,h],dragBlockClass:"tox-dialog-wrap",parts:{blocker:{dom:Rg('<div class="tox-dialog-wrap"></div>'),components:[{dom:{tag:"div",classes:["tox-dialog-wrap__backdrop"]}}]}}})),x=TA({getRoot:function(){return y},getBody:function(){return sB.getBody(y)},getFooter:function(){return sB.getFooter(y)},getFormWrapper:function(){var t=sB.getBody(y);return Uf.getCurrent(t).getOr(t)}},n.redial);return{dialog:y,instanceApi:x}},AA=function(t,n,e){var o,r,i,u,a,c,s,f,l=qe("dialog-label"),d=tg((i={title:t.internalDialog.title,draggable:!0},u=l,a=e.shared.providers,Bh.sketch({dom:Rg('<div class="tox-dialog__header"></div>'),components:[OA(i,tt.some(u),a),kA(a)],containerBehaviours:zu([RT.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return bu(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),m=tg((c={body:t.internalDialog.body},xA(c,e))),g=tg((s={buttons:t.internalDialog.buttons},f=e.shared.providers,CA(s,f))),p=wA(function(){return v},{onBlock:function(){},onUnblock:function(){},onClose:function(){return n.closeWindow()}}),h=gu({dom:{tag:"div",classes:["tox-dialog"],attributes:(o={role:"dialog"},o["aria-labelledby"]=l,o)},eventOrder:(r={},r[On()]=[oO.name(),qu.name()],r[En()]=["execute-on-form"],r[Rn()]=["reflecting","execute-on-form"],r),behaviours:zu([Yd.config({mode:"cyclic",onEscape:function(t){return Ar(t,Hh),tt.some(!0)},useTabstopAt:function(t){return!gw(t)&&("button"!==ke(t)||"disabled"!==Me(t,"disabled"))}}),oO.config({channel:ZB,updateState:function(t,n){return tt.some(n)},initialData:t}),Rm("execute-on-form",p),cw({})]),components:[d.asSpec(),m.asSpec(),g.asSpec()]}),v=TA({getRoot:function(){return h},getFooter:function(){return g.get(h)},getBody:function(){return m.get(h)},getFormWrapper:function(){var t=m.get(h);return Uf.getCurrent(t).getOr(t)}},n.redial);return{dialog:h,instanceApi:v}},DA=function(t,n){return sB.parts().close(Zm.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:zu([nb.config({})])}))},_A=function(){return sB.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},MA=function(t,n){return sB.parts().body({dom:{tag:"div",classes:["tox-dialog__body","todo-tox-fit"]},components:[{dom:Rg("<p>"+n.translate(t)+"</p>")}]})},FA=function(t){return sB.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},IA=function(t,n){return[Bh.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),Bh.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},VA=function(e){return sB.sketch({lazySink:e.lazySink,onEscape:function(){return e.onCancel(),tt.some(!0)},dom:{tag:"div",classes:["tox-dialog"].concat(e.extraClasses)},components:[{dom:{tag:"div",classes:["tox-dialog__header"]},components:[e.partSpecs.title,e.partSpecs.close]},e.partSpecs.body,e.partSpecs.footer],parts:{blocker:{dom:Rg('<div class="tox-dialog-wrap"></div>'),components:[{dom:{tag:"div",classes:["tox-dialog-wrap__backdrop"]}}]}},modalBehaviours:zu([Rm("basic-dialog-events",[Kr(Nh,function(t,n){e.onCancel()}),Kr(zh,function(t,n){e.onSubmit()})])])})},RA=function(c){var u,a,e=(u=c.backstage.shared,{open:function(t,n){var e=function(){sB.hide(r),n()},o=tg(hS({name:"close-alert",text:"OK",primary:!0,icon:tt.none()},"cancel",u.providers)),r=gu(VA({lazySink:function(){return u.getSink()},partSpecs:{title:_A(),close:DA(function(){e()},u.providers),body:MA(t,u.providers),footer:FA(IA([],[o.asSpec()]))},onCancel:function(){return e()},onSubmit:Q,extraClasses:["tox-alert-dialog"]}));sB.show(r);var i=o.get(r);hm.focus(i)}}),o=(a=c.backstage.shared,{open:function(t,n){var e=function(t){sB.hide(i),n(t)},o=tg(hS({name:"yes",text:"Yes",primary:!0,icon:tt.none()},"submit",a.providers)),r=hS({name:"no",text:"No",primary:!0,icon:tt.none()},"cancel",a.providers),i=gu(VA({lazySink:function(){return a.getSink()},partSpecs:{title:_A(),close:DA(function(){e(!1)},a.providers),body:MA(t,a.providers),footer:FA(IA([],[r,o.asSpec()]))},onCancel:function(){return e(!1)},onSubmit:function(){return e(!0)},extraClasses:["tox-confirm-dialog"]}));sB.show(i);var u=o.get(i);hm.focus(u)}}),r=function(t,i){return QB.open(function(t,n,e){var o=n,r=BA({dataValidator:e,initialData:o,internalDialog:t},{redial:QB.redial,closeWindow:function(){sB.hide(r.dialog),i(r.instanceApi)}},c.backstage);return sB.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},t)},i=function(t,u,a){return QB.open(function(t,n,e){var o=Yo(Xo("data",e,n)),r=AA({dataValidator:e,initialData:o,internalDialog:t},{redial:QB.redial,closeWindow:function(){$m.hide(i),a(r.instanceApi)}},c.backstage),i=gu($m.sketch({lazySink:c.backstage.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},inlineBehaviours:zu([Rm("window-manager-inline-events",[Kr(Nn(),function(t,n){Ar(r.dialog,Nh)})])])}));return $m.showAt(i,u,pu(r.dialog)),r.instanceApi.setData(o),Yd.focusIn(r.dialog),r.instanceApi},t)};return{open:function(t,n,e){return n!==undefined&&"toolbar"===n.inline?i(t,c.backstage.shared.anchors.toolbar(),e):n!==undefined&&"cursor"===n.inline?i(t,c.backstage.shared.anchors.cursor(),e):r(t,e)},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){o.open(t,function(t){n(t)})}}};Ke.add("silver",function(t){var n=YT(t),e=n.mothership,o=n.uiMothership,r=n.backstage,i=n.renderUI,u=n.getUi;rB(t,r),Ge(qe("silver-demo"),e),Ge(qe("silver-ui-demo"),o),eh(t,r.shared);var a=RA({backstage:r});return{renderUI:i,getWindowManagerImpl:Z(a),getNotificationManagerImpl:function(){return ig(t,{backstage:r},o)},ui:u()}}),function mD(){}}(window);