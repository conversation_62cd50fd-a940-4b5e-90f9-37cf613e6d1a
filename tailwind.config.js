const config = require('./tasks/helpers/getConfig.js');

module.exports = {
	content: ['./app/FrontModule/**/*.latte', './app/PostType/**/*.latte'],
	prefix: 'tw-',
	safelist: ['tw-text-left', 'tw-text-center', 'tw-text-right', 'tw-hidden'],
	corePlugins: {
		preflight: false,
	},
	theme: {
		screens: config.mediaQueries.breakpoints,
		extend: {
			colors: {
				primary: 'var(--color-primary)',
				secondary: 'var(--color-secondary)',
				black: 'var(--color-black)',
				white: 'var(--color-white)',
				green: 'var(--color-green)',
				blue: 'var(--color-blue)',
				orange: 'var(--color-orange)',
				red: 'var(--color-red)',
				bd: 'var(--color-bd)',
				bg: 'var(--color-bg)',
				link: 'var(--color-link)',
				hover: 'var(--color-hover)',
			},
			fontFamily: {
				primary: 'var(--font-primary)',
				secondary: 'var(--font-secondary)',
			},
			spacing: {
				xs: 'var(--spacing-xs)',
				sm: 'var(--spacing-sm)',
				md: 'var(--spacing-md)',
				lg: 'var(--spacing-lg)',
				xl: 'var(--spacing-xl)',
				'2xl': 'var(--spacing-2xl)',
			},
		},
	},
	plugins: [require('@tailwindcss/container-queries')],
};
