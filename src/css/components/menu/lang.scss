@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-lang {
	$s: &;
	position: relative;
	height: 100%;
	&__btn {
		@include mixins.button-reset;
		display: flex;
		gap: 1rem;
		align-items: center;
		height: 100%;
		padding: 1rem 1.5rem;
		background: variables.$color-white;
	}
	&__img {
		object-fit: contain;
	}
	&__arrow {
		width: 1.2rem;
		transition: transform variables.$t;
	}
	&__list {
		@extend %reset-ul;
		position: absolute;
		top: 100%;
		right: -0.1rem;
		width: 20rem;
		padding: 1rem 2rem;
		border: 0.1rem solid variables.$color-bd;
		background: variables.$color-white;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: flex;
		gap: 1rem;
		align-items: center;
		padding: 0.5rem 0;
		color: inherit;
		text-decoration: none;
	}

	// STATES
	&__item.is-active &__link {
		font-weight: bold;
	}

	// HOVERS
	.no-hoverevents &.is-open,
	.hoverevents &:hover {
		#{$s}__arrow {
			transform: rotate(-180deg);
		}
		#{$s}__list {
			opacity: 1;
			visibility: visible;
		}
	}
}
