@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-open {
	$s: &;
	&__btn {
		.icon-svg {
			transition: transform variables.$t;
		}
	}

	// STATEs
	.js &__box {
		position: absolute;
		top: -5000px;
		left: -5000px;
		opacity: 0;
		transition: opacity variables.$t;
	}
	.js &:has(&__inp:checked) > &__box,
	.js &.is-open > &__box {
		position: static;
		top: auto;
		left: auto;
		opacity: 1;
	}
	.js &:has(&__inp:checked) &__add::after,
	.js &.is-open &__add::after {
		transform: translateY(-50%);
	}
	&__btn .is-open .icon-svg {
		transform: scale(-1);
	}
}
