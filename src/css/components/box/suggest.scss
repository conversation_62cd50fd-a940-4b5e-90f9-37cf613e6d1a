@use 'base/variables';
@use 'base/functions';

.b-suggest {
	position: absolute;
	top: 100%;
	right: 0;
	left: 0;
	z-index: 50;
	padding: functions.spacing('sm');
	border: 0.1rem solid variables.$color-bd;
	background: variables.$color-white;
	opacity: 0;
	visibility: hidden;
	transition: opacity variables.$t, visibility 0s variables.$t;
	&__list {
		margin: functions.spacing('sm') * -1;
		&:first-child {
			margin-top: functions.spacing('sm') * -1;
		}
	}
	&__item {
		border-top: 0.1rem solid variables.$color-bd;
	}
	&__link {
		display: block;
		padding: functions.spacing(xs) functions.spacing('sm');
		transition: color variables.$t, background-color variables.$t;
	}

	// STATEs
	&.is-visible {
		opacity: 1;
		visibility: visible;
		transition-delay: 0s, 0s;
	}

	.is-selected &__link,
	.hoverevents &__link:hover {
		background: variables.$color-bg;
	}
}
