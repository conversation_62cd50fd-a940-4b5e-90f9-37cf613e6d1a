export const number = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	const keyChunks = key.split('-');
	keyChunks.pop();
	parent.append(tplElement);
	tplElement.outerHTML = `

<div class="inp u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'>
	${scheme.label ? `<label for="custom-${key}" class="inp-label title">${scheme.label}</label>` : ''}
	<div class="inp-fix u-mb-sm">
		<input
			type="number"
			min="${scheme.min}"
			max="${scheme.max}"
			step="${scheme.step}"
			name="custom-${key}"
			id="custom-${key}"
			class="inp-text"
			value="${value}"
			data-action="change->CustomField#updateValue"
		>
		<div class="inp-text__holder"></div>
	</div>
</div>
`;
};
