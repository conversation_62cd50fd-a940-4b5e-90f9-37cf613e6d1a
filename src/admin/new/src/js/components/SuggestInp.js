import $ from 'jquery';
import { ApplicationController } from 'stimulus-use';
import { SuggestMenu } from '../class/SuggestMenu';
import { Suggest } from '../class/Suggest';

export default class SuggestInp extends ApplicationController {
	static targets = ['input', 'wrapper', 'idInput'];
	static values = {
		url: Object,
	};

	suggestMenu = null;

	blurHandler = null;

	connect() {
		if (!this.urlValue) return;

		const { link, searchParameterName, params } = this.urlValue;

		const menu = $('<div class="b-suggest"></div>');
		$(this.wrapperTarget).append(menu);

		const suggest = new Suggest(this.inputTarget, {
			minLength: 2,
			typeInterval: 250,
			url: `${link}${params && Object.keys(params).length > 0 ? `?${new URLSearchParams(params).toString()}` : ''}`,
			inputName: searchParameterName,
		});

		this.suggestMenu = new SuggestMenu(menu[0], suggest, {
			item: '[data-id]',
		}).init();

		this.suggestMenu.on('menuselect', () => {
			const $item = $(this.suggestMenu.items).eq(this.suggestMenu.selectedIndex - 1);
			const $link = $item.find('a');
			const $id = $item.data('id');

			if ($id && this.hasIdInputTarget) {
				this.idInputTarget.value = $id;
				this.inputTarget.value = $item.text();
			} else if ($link) {
				window.location = $link.attr('href');
			}

			this.suggestMenu.hide();
		});

		if (this.hasInputTarget && this.hasIdInputTarget) {
			this.blurHandler = () => {
				if (this.hasInputTarget && this.inputTarget.value.trim() === '') {
					this.idInputTarget.value = '';
				}
			};
			this.inputTarget.addEventListener('blur', this.blurHandler);
		}
	}

	disconnect() {
		if (this.suggestMenu) {
			this.suggestMenu.destroy();
		}
		if (this.hasIdInputTarget && this.blurHandler) {
			this.idInputTarget.removeEventListener('blur', this.blurHandler);
		}
	}

	clear() {
		if (this.hasInputTarget) {
			this.inputTarget.value = '';
		}
		if (this.hasIdInputTarget) {
			this.idInputTarget.value = '';
		}
		this.dispatch('updateSuggestValue');
	}
}
