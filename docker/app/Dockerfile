FROM php:8.3-apache

COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/local/bin
RUN IPE_GD_WITHOUTAVIF=1 install-php-extensions gd intl curl mbstring exif mysqli xdebug zip redis
# ^ use gd without avif, it currently takes ages to build (see https://github.com/mlocati/docker-php-extension-installer/issues/514)

ENV APACHE_DOCUMENT_ROOT /var/www/html/www
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf
RUN echo "memory_limit = 1G" >> /usr/local/etc/php/conf.d/memlimit.ini

RUN echo "ServerName superadmin.superkoders.test" >> /etc/apache2/apache2.conf
# Configure file upload limits for 16MB uploads
RUN echo "upload_max_filesize = 16M" >> /usr/local/etc/php/conf.d/uploads.ini && \
    echo "post_max_size = 20M" >> /usr/local/etc/php/conf.d/uploads.ini && \
    echo "max_file_uploads = 20" >> /usr/local/etc/php/conf.d/uploads.ini && \
    echo "max_execution_time = 300" >> /usr/local/etc/php/conf.d/uploads.ini && \
    echo "max_input_time = 300" >> /usr/local/etc/php/conf.d/uploads.ini

RUN a2enmod rewrite
