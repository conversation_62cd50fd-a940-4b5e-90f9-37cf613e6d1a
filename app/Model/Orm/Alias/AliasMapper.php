<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class AliasMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'alias';

	public function findByName(string $string): ICollection
	{
		$builder = $this->builder();
		$builder->orWhere('alias like %_like_', $string);
		return $this->toCollection($builder);
	}


	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
