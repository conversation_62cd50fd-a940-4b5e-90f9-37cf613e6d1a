<?php

declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
use Brick\Math\BigDecimal;
use IteratorAggregate;
use Nextras\Orm\Entity\Embeddable\Embeddable;
use Traversable;

/**
 * @property BigDecimal|null $standard {container BigDecimalContainer}
 * @property BigDecimal|null $reduced {container BigDecimalContainer}
 * @property BigDecimal|null $secondReduced {container BigDecimalContainer}
 * @property BigDecimal|null $superReduced {container BigDecimalContainer}
 * @property BigDecimal|null $parking {container BigDecimalContainer}
 *
 * @implements IteratorAggregate<VatRate, BigDecimal|null>
 */
class VatRates extends Embeddable implements IteratorAggregate
{

	public function get(VatRate $rate): BigDecimal|null
	{
		return match($rate) {
			VatRate::Standard => $this->standard,
			VatRate::Reduced => $this->reduced,
			VatRate::SecondReduced => $this->secondReduced,
			VatRate::SuperReduced => $this->superReduced,
			VatRate::Parking => $this->parking,
			VatRate::None => BigDecimal::zero(),
		};
	}

	public function getIterator(): Traversable
	{
		yield VatRate::Standard => $this->standard;
		yield VatRate::Reduced => $this->reduced;
		yield VatRate::SecondReduced => $this->secondReduced;
		yield VatRate::SuperReduced => $this->superReduced;
		yield VatRate::Parking => $this->parking;
	}

}
