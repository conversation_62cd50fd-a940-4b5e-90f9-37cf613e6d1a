<?php

declare(strict_types=1);

namespace App\Model\Orm\File;

use App\Model\ConfigService;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;

final class FileModel
{
	public function __construct(
		private readonly Orm           $orm,
		private readonly FileRepository $fileRepository,
		private readonly Repository  $esAllRepository,
		private readonly ConfigService  $configService,
	) {}


	public function add(\Nette\Http\FileUpload $file): File
	{
		$newFile = new File();
		$this->fileRepository->attach($newFile);
		$newFile->name = $file->getUntrustedName();

		$this->fileRepository->persistAndFlush($newFile);

		$filename = $newFile->id.'-'.mb_strtolower($file->getSanitizedName());
		$file = $file->move(WWW_DIR.FILES_DIR.$filename);
		$size = $file->getSize();

		$newFile->filename = $filename;
		$newFile->size = $size;
		$this->fileRepository->persistAndFlush($newFile);

		return $newFile;
	}

	public function getFileUsage(File $file, Mutation $mutation): array
	{
		$esIndex = $this->orm->esIndex->getAllLastActive($mutation);
		$usage = [];
		if ($esIndex !== null) {
			$usage = array_map(
				function (\stdClass $data) {
					return $data->name;
				},
				$this->esAllRepository->searchFileUsage($esIndex, $file)
			);
		}
		return $usage;
	}


//	public function delete(File $file) : void
//	{
//		if (file_exists($this->configService->get('wwwDir').$file->url)) {
//			unlink($this->configService->get('wwwDir').$file->url);
//		}
//		$this->orm->file->removeAndFlush($file);
//	}

	public function markAsDelete(File $file) : void
	{
		$currentPath = $this->configService->get('wwwDir').$file->url;
		if (file_exists($currentPath)) {
			$pathInfo = pathinfo($currentPath);
			$newFilename = '_deleted_' . $pathInfo['basename'];
			$dirname = $pathInfo['dirname'] ?? '';
			$newPath = $dirname . '/' . $newFilename;

			rename($currentPath, $newPath);

			// Update the file entity with new filename
			$file->filename = $newFilename;
		}

		$file->isDeleted = true;
		$file->deletedTime = new \DateTimeImmutable();
		$this->orm->file->persistAndFlush($file);
	}

}
