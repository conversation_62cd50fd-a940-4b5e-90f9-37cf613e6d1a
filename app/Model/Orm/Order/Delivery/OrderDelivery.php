<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\RefreshResult;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;
use stdClass;

/**
 * @property-read int $id {primary}
 * @property Order|null $order {1:1 Order::$delivery}
 * @property DeliveryMethodConfiguration|null $deliveryMethod {m:1 DeliveryMethodConfiguration::$orderItems}
 * @property DeliveryInformation|null $information {1:1 DeliveryInformation::$delivery, isMain=true, cascade=[persist, remove]}
 *
 * @property DateTimeImmutable|null $dateDeliveryFrom
 * @property DateTimeImmutable|null $dateDeliveryTo
 * @property DateTimeImmutable|null $dateExpedition
 */
final class OrderDelivery extends OrderItem
{
	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, DeliveryMethodConfiguration $deliveryMethod): OrderDelivery
	{
		$item = new self();
		$item->order = $order;
		$item->deliveryMethod = $deliveryMethod;
		$informationClass = $deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		$item->information = new $informationClass($item);
		$item->vatRate = $item->getCurrentVatRate();
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->amount = $item->getMaxAvailableAmount();

		$item->dateDeliveryFrom = $item->getCurrentDateDeliveryFrom();
		$item->dateDeliveryTo = $item->getCurrentDateDeliveryTo();
		$item->dateExpedition = $item->getCurrentDateDeliveryExpedition();

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return 1;
	}

	public function getCurrentUnitPrice(): Money
	{
		$priceAmount = $this->deliveryMethod->price($this->order->priceLevel, $this->order->country, $this->order);
		if ($priceAmount === null) {
			throw new \LogicException('Delivery method hasnt got any prices.');
		}
		return $priceAmount;
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->deliveryMethod->getVatRate($this->order->country);
	}

	protected function getCurrentDateDeliveryFrom(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryFrom();
	}

	protected function getCurrentDateDeliveryTo(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryTo();
	}

	protected function getCurrentDateDeliveryExpedition(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryExpedition();
	}

	public function getName(): string
	{
		return $this->deliveryMethod->name;
	}

	public function getDesc(): string
	{
		return $this->deliveryMethod->desc;
	}

	public function refresh(): ?RefreshResult
	{
		try {
			$expectedPrice   = $this->getCurrentUnitPrice();
			$expectedVatRate = $this->getCurrentVatRate();
			$expectedDateDeliveryFrom = $this->getCurrentDateDeliveryFrom();
			$expectedDateDeliveryTo = $this->getCurrentDateDeliveryTo();
			$expectedDateDeliveryExpedition = $this->getCurrentDateDeliveryExpedition();
		} catch (\Throwable) {
			return null;
		}

		$result = new RefreshResult($this);
		$result->availableAmount = 1;
		$result->updated = true;

		if (!$this->unitPrice->asMoney()->isEqualTo($expectedPrice)) {
			$result->addMessage('delivery_price_changed');

			if ($this->vatRate !== $expectedVatRate) {
				$result->addMessage('delivery_price_and_vats_changed');
				$this->vatRate = $expectedVatRate;
			}

			$this->unitPrice = Price::from($expectedPrice);

			return $result;
		}

		if ($this->vatRate !== $expectedVatRate) {
			$result->addMessage('delivery_vat_changed');

			$this->vatRate = $expectedVatRate;

			return $result;
		}

		if (
			$this->dateDeliveryFrom?->getTimestamp() !== $expectedDateDeliveryFrom?->getTimestamp() ||
			$this->dateDeliveryTo?->getTimestamp() !== $expectedDateDeliveryTo?->getTimestamp() ||
			$this->dateExpedition?->getTimestamp() !== $expectedDateDeliveryExpedition?->getTimestamp()
		) {
			$result->addMessage('delivery_date_changed');

			$this->dateDeliveryFrom = $expectedDateDeliveryFrom;
			$this->dateDeliveryTo = $expectedDateDeliveryTo;
			$this->dateExpedition = $expectedDateDeliveryExpedition;

			return $result;
		}

		return null;
	}
}
