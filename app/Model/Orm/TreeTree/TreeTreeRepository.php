<?php declare(strict_types = 1);

namespace App\Model\Orm\TreeTree;

use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Repository\Repository;

/**
 * @method TreeTree getById($id)
 */
final class TreeTreeRepository extends Repository
{

	static function getEntityClassNames(): array
	{
		return [TreeTree::class];
	}


	public function replace(Tree $tree, Tree $attachedTree, string $type, int $sort): TreeTree
	{
		if (!$treeTree = $this->getByReferenceAndType($tree, $attachedTree, $type)) {
			$treeTree = $this->createNew();
		}

		return $this->update($treeTree, $tree, $attachedTree, $sort, $type);
	}


	public function getByReferenceAndType(Tree $tree, Tree $attachedTree, string $type): ?TreeTree
	{
		return $this->getBy([
			'type' => $type,
			'mainTree' => $tree,
			'attachedTree' => $attachedTree,
		]);
	}


	public function createNew(): TreeTree
	{
		$newTreeTree = new TreeTree();
		$this->attach($newTreeTree);

		return $newTreeTree;
	}

	public function update(TreeTree $treeTree, Tree $tree, Tree $attachedTree, int $sort, string $type): TreeTree
	{
		$treeTree->type = $type;
		$treeTree->mainTree = $tree;
		$treeTree->attachedTree = $attachedTree;
		$treeTree->sort = $sort;
		return $treeTree;
	}

}
