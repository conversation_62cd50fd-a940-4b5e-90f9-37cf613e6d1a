<?php

declare(strict_types=1);

namespace App\Model\Orm\Redirect;

use App\Model\Orm\Functions\LikeFilterFunction;

/**
 * @method Redirect getById($id)
 */
final class RedirectRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [Redirect::class];
	}

    protected function createCollectionFunction(string $name)
    {
        if ($name === LikeFilterFunction::class) {
            return new LikeFilterFunction();
        } else {
            return parent::createCollectionFunction($name);
        }
    }

}
