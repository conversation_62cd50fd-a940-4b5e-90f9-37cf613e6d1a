<?php declare(strict_types = 1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\Traits\HasCache;
use Nextras\Orm\Collection\ICollection;

class PriceLevelModel
{

	use HasCache;

	public function __construct(
		private PriceLevelRepository $repository
	)
	{
	}


	public function getAllPriceLevel(): ICollection
	{
		if (!isset($this->cache['allPriceLevel'])) {
			$this->cache['allPriceLevel'] = $this->repository->findAll();
		}

		return $this->cache['allPriceLevel'];
	}

}
