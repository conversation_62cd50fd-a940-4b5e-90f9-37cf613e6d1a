<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductReview;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ProductReviewMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'product_review';


	public function getStatistic(Product $product): Result
	{
		$builder = $this->builder()->select('productId, sum(stars) as sum, count(*) as count, sum(stars)/count(*) as average')
			->andWhere('productId = %i', $product->id);

		return $this->connection->queryArgs(
			$builder->getQuerySql(),
			$builder->getQueryParameters()
		);
	}


	public function getProductForReviewFormOrder(?int $userId, ?int $limit = NULL, ?int $offset = NULL, bool $showOnlyEmptyReview = FALSE, ?array $open = NULL): ICollection
	{
//		SELECT SQL_CALC_FOUND_ROWS DISTINCT oi.productId
//		FROM `order_item` AS `oi`
//		LEFT JOIN `order` AS `o` ON o.id = oi.orderId
//		LEFT JOIN `product` AS `p` ON oi.productId = p.id
//		LEFT JOIN `product_review` AS `pr` ON pr.isMain = 1 && pr.productId = p.id && pr.userId = 1934
//		WHERE o.idUser = 1934 AND p.public = 1 AND oi.productId > 0
//		ORDER BY `pr`.`date` , `pr`.`id`
//		LIMIT 12

		$builder = $this->builder()->select('SQL_CALC_FOUND_ROWS DISTINCT oi.productId')
			->from('order_item', 'oi')
			->joinInner('[order] as o', 'o.id = oi.orderId')
			->joinInner( '[product] as p', 'oi.productId = p.id')
			->joinInner('[product_review] as pr', 'pr.isMain = 1 && pr.productId = p.id && pr.userId = %i', $userId)
//			->andWhere('o.idUser = %i',  1934)
			->andWhere('p.public = %i',  1)
			->andWhere('oi.type = %s',  'product')

			->orderBy('`pr`.`date` , `pr`.`id`')
			->limitBy($limit, $offset);

		if ($showOnlyEmptyReview) {
			$builder->andWhere('pr.id is NULL');
		}

		if ($open) {
			$builder->orderBy('p.id = %i desc', $open);
		}

		return $this->toCollection($builder);

	}

}
