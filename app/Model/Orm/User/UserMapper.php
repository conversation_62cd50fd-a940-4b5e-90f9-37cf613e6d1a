<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Security\User;
use Nette\Http\SessionSection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class UserMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'user';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->manyHasManyStorageNamePattern = '%s_%s';
		return $conventions;
	}

	public function findByFilter(SessionSection $filter, User $user, ?string $order): ICollection
	{
		$builder = $this->builder()
			->select('u.*')
			->from($this->tableName, 'u');

		if (isset($filter->data)) {
			if (($filter->data->fulltext ?? '') !== '') {
				$builder->andWhere('( u.email LIKE %_like_ OR firstname LIKE %_like_ OR lastname LIKE %_like_ )', $filter->data->fulltext, $filter->data->fulltext, $filter->data->fulltext);
			}

			if (($filter->data->role ?? []) !== []) {
				$builder->andWhere('u.role IN %s[]', $filter->data->role);
			}

			if (intval($filter->data->mutation ?? 0) !== 0) {
				$builder->joinInner('[user_mutation] as um', '[u.id] = [um.userId]');
				$builder->andWhere('um.mutationId = %i', $filter->data->mutation);
			}
		}

		$builder->andWhere('u.role IN %s[]', $user->getAllowedRoles()); // by default omezeni podle role logged usera

		if (($order ?? '') !== '') {
			$builder->orderBy($order);
		}

		return $this->toCollection($builder);
	}

}
