<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\All\Message\DeleteAllMessage;
use App\Model\Messenger\Elasticsearch\All\Message\ReplaceAllMessage;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use Elastica\Exception\NotFoundException;
use Nette\Utils\ArrayHash;

class Facade
{

	public function __construct(
		private readonly Service $service,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ElasticBusWrapper $elasticBusWrapper,
		private readonly MutationsHolder $mutationsHolder,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly TreeRepository $treeRepository,
		private readonly ProductRepository $productRepository,
		private readonly SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
	)
	{
	}

	public function deleteNow(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esIndex = $this->esIndexRepository->getAllLastActive($mutation)) !== null) {
			try {
				$elasticEntity = new ElasticAll($object);
				$this->service->deleteDoc($esIndex, $elasticEntity);
			} catch (NotFoundException) {
				// ignore if index not found
			}
		}
	}

	public function updateNow(object $object, array $convertors = []): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getAllLastActive($mutation)) !== null) {
			if ($convertors === []) {
				$convertors = $this->convertorProvider->getAll(get_class($object));
			}

			$elasticEntity = new ElasticAll($object, $convertors);

			$this->service->replaceDoc($esAllIndex, $elasticEntity);

		}
	}

	public function update(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getAllLastActive($mutation)) !== null && isset($object->id)) {

			$convertors = $this->convertorProvider->getAllLikeStrings(get_class($object));
			$message = new ReplaceAllMessage(get_class($object), $object->id, $esAllIndex, $convertors);
			$this->elasticBusWrapper->send($message);
		}
	}

	public function delete(object $object): void
	{
		$mutation = $this->mutationsHolder->getDefaultRs();
		if (($esAllIndex = $this->esIndexRepository->getAllLastActive($mutation)) !== null && isset($object->id)) {

			$message = new DeleteAllMessage(get_class($object), $object->id, $esAllIndex);
			$this->elasticBusWrapper->send($message);
		}
	}

	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false): void
	{
		$esIndex->errorCount = 0;
		$esIndex->errorDetail = new ArrayHash();

		$repositories = [
			BlogLocalization::class => $this->blogLocalizationRepository,
			BlogTagLocalization::class => $this->blogTagLocalizationRepository,
			AuthorLocalization::class => $this->authorLocalizationRepository,
			Product::class => $this->productRepository,
			Tree::class => $this->treeRepository,
			SeoLinkLocalization::class => $this->seoLinkLocalizationRepository,
		];

		$lastClass = null;
		$lastItemId = null;
		$lastConvertorStrings = null;
		$sendFirstSignal = true;

		foreach ($repositories as $class => $repository) {
			$ids = $repository->findAllIds($limit);
			$convertorStrings = $this->convertorProvider->getAllLikeStrings($class);

			foreach ($ids as $itemRow) {

				$signals = [];
				if ($sendFirstSignal) {
					$signals[] = ElasticBusWrapper::SIGNAL_FIRST;
					$sendFirstSignal = false;
				}

				$itemId = $itemRow->id;
				$this->elasticBusWrapper->send(
					new ReplaceAllMessage($class, $itemId, $esIndex, $convertorStrings, $signals)
				);

				$lastClass = $class;
				$lastItemId = $itemId;
				$lastConvertorStrings = $convertorStrings;
			}
		}

		if ($lastClass !== null && $lastItemId !== null && $lastConvertorStrings !== null) {
			// send last signal
			$signals = [];
			$signals[] = ElasticBusWrapper::SIGNAL_LAST;

			if ($autoSwitch) {
				$signals[] = ElasticBusWrapper::AUTO_SWITCH;
			}

			$this->elasticBusWrapper->send(
				new ReplaceAllMessage($lastClass, $lastItemId, $esIndex, $lastConvertorStrings, $signals)
			);
		}
	}

}
