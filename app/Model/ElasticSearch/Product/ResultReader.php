<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\Orm\Product\ProductRepository;
use Elastica\ResultSet;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class ResultReader
{

	public function __construct(
		private ProductRepository $productRepository,
	)
	{
	}


	public function mapResultToEntityCollection(ResultSet $result, float $minScore = 0.01): ICollection
	{
		$ids = $this->resultToIdsArray($result, $minScore);
		return $this->idsToCollection($ids);
	}

	/**
	 * @return int[]
	 */
	public function resultToIdsArray(ResultSet $result, float $minScore = 0.01): array
	{
		$ids = [];
		foreach ($result->getResults() as $r) {
			if ($r->getScore() > $minScore) {
				//TODO review result resaders
				$id = (int) $r->getId();
				$ids[] = $id;
			}
		}

		return $ids;
	}


	public function idsToCollection(array $ids): ICollection
	{
		if (count($ids) > 0) {
			return $this->productRepository->findFilteredProducts($ids);
		} else {
			return new EmptyCollection();
		}
	}

}
