<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\Box\Slider;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\Pages;
use stdClass;

class BoxList implements BoxListGenerator
{

	private ?array $list;

	public function __construct(
		private readonly Pages $pages,
	)
	{}


	public function getBoxList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getBoxesForCatalog();
		}

		return $this->list;
	}


	private function getBoxesForCatalog(): array
	{
		$boxes = [];

		$pages = $this->pages;

		$boxes['path'] = new CheckBoxes(
			DiscreteValues::NAMESPACE_FLAG_VALUES,
			function () use ($pages) {
				$ret = [];
				foreach ($pages->eshop->crossroad as $page) {
					$retItem = new stdClass(); // only items in store
					$retItem->id = $page->id;
					$retItem->name = $page->name;
					$ret[] = $retItem;
				}

				return $ret;
			},
			'title_path',
			'',
			'',
			1000,
			true,
			false,
			true,
		);

		$boxes['price'] = new Slider(
			'filter_title_price',
			'',
			20,
			true
		);


		return $boxes;
	}


}
