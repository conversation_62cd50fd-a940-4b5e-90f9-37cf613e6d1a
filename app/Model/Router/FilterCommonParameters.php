<?php

declare(strict_types=1);

namespace App\Model\Router;

final class FilterCommonParameters
{

	public function in(array $params): array
	{

		if (!isset($params['alias'])) {
			$params['alias'] = '';
		}
		return $params;
	}

	public function out(array $params): array
	{
		$paramsToUnset = ['idref', 'action', 'presenter', 'object', 'absolute', 'mutation'];
		foreach ($paramsToUnset as $paramName) {
			if (isset($params[$paramName])) {
				unset($params[$paramName]);
			}
		}

		if (array_key_exists('alias', $params) && ($params['alias'] === null || $params['alias'] === '')) {
			unset($params['alias']);
		}

		return $params;
	}
}
