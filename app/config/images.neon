parameters:
	noImageFile: '/static/img/illust/noimg.svg'
	mainImageDirectory: '/data/images'
	config:
		imageOriginal:
			resize: TRUE
			width:  3840
			height: 3840

		# quality: [0–100]; defaults to 85 (JPG), 80 (WEBP), 30 (AVIF), 100 (PNG)
		imageSizes:
			# SA - part/head/meta.latte && defail noImageSiez for SVG/GIF
			ogImage:
				width:  1200
				height: 630
				keepRatio: true

			max:
				width: 3840
				height: 3840
			2xl:
				width:  1920
				height: 1920
			xl:
				width:  1200
				height: 1200
				# quality: 75
			xl-16-9:
				width:  1200
				height: 675
				keepRatio: true

			lg:
				width: 750
				height: 750
			md:
				width:  560
				height: 560
			sm:
				width:  320
				height: 320
			xs:
				width:  100
				height: 100


			# SA - don't delete
			library:
				width:  300
				height: 300
			s:
				width:  280
				height: 280
			l:
				width:  800
				height: 800





services:
	- App\Model\Image\Storage\BasicStorage(%config.imageSizes%, %wwwDir%, mainImageDirectory: %mainImageDirectory%, forcedDomain: %config.imageFromStage%)
	- App\Model\Image\Resizer(%wwwDir%)
	- App\Model\Image\Setup\ImageSizes(%config.imageSizes%)
	- App\Model\Image\ImageObjectFactory(noImageFile: %noImageFile%, wwwDir: %wwwDir%)
	- App\Model\Image\BetterImageTypeDetector
	- App\Model\Image\Rotator

