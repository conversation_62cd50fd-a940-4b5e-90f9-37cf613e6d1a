
<div class="grid" n:if="$showProductFilter && isset($filter)">
	<div class="grid__cell size--4-12@lg size--3-12@xl" n:snippet="catalogFilter">
		{snippetArea filterArea}
			{* filtr na leve strane *}
			{include APP_DIR . '/FrontModule/Presenters/Catalog/templates/part/filter.latte', class=>'tw-mb-sm'}
		{/snippetArea}
	</div>
	<div class="grid__cell size--8-12@lg size--9-12@xl">
		{snippet filterSetup}
			{* seznam vybranych filtraci *}
			{include APP_DIR . '/FrontModule/Presenters/Catalog/templates/part/selectedFilters.latte'}
		{/snippet}

		{snippet products}
			{snippetArea productsInner}
				{include $templates . '/part/crossroad/products.latte', products: $products, titleTag: 'h2', ajaxPage: true, pager: true}
			{/snippetArea}
		{/snippet}
	</div>
</div>
