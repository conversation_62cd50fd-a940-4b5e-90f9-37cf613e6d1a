{if $hasResults}
	{if $trees->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_trees} {$trees->count()}
			</h2>
			<ul>
				{foreach $trees as $page}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $page}">
							{$page->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($trees) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$trees->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}

	{if $blogs->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_blogs} {$blogs->count()}
			</h2>
			<ul>
				{foreach $blogs as $page}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $page}">
							{$page->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($blogs) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$blogs->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}
	{if $products->count()}
		<div class="b-suggest__group">
			<h2>
				{_search_tab_blogs} {$products->count()}
			</h2>
			<ul>
				{foreach $products as $product}
					{breakIf $iterator->getCounter() > 5}
					<li>
						<a href="{link $product}">
							{$product->nameAnchor}
						</a>
					</li>
				{/foreach}
			</ul>
			{if count($products) > 5}
				<p>
					<a href="{link $pages->search, 'search' => $search}">
						{_search_show_all} {$products->count()}
					</a>
				</p>
			{/if}
		</div>
	{/if}
{else}
	<h2>
		{_search_title}
	</h2>
	<p>
		{_search_nothing}
	</p>
{/if}

