<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\User;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Components\VisualPaginator\VisualPaginator;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordForm;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordFormFactory;
use App\FrontModule\Components\GoogleConnect\GoogleConnect;
use App\FrontModule\Components\GoogleConnect\GoogleConnectFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\FrontModule\Components\LostPasswordForm\LostPasswordForm;
use App\FrontModule\Components\LostPasswordForm\LostPasswordFormFactory;
use App\FrontModule\Components\OrderHistory\OrderHistory;
use App\FrontModule\Components\OrderHistory\OrderHistoryFactory;
use App\FrontModule\Components\ProfileForm\ProfileForm;
use App\FrontModule\Components\ProfileForm\ProfileFormFactory;
use App\FrontModule\Components\RegistrationForm\RegistrationForm;
use App\FrontModule\Components\RegistrationForm\RegistrationFormFactory;
use App\FrontModule\Components\UserAddressForm\UserAddressForm;
use App\FrontModule\Components\UserAddressForm\UserAddressFormFactory;
use App\FrontModule\Components\UserSideMenu\UserSideMenu;
use App\FrontModule\Components\UserSideMenu\UserSideMenuFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use Nette\Application\AbortException;
use Nette\Application\Attributes\Persistent;
use Nette\Application\UI\InvalidLinkException;

/**
 * @property Tree $object
 * @method Tree getObject()
 */
final class UserPresenter extends BasePresenter
{
	#[Persistent]
	public string $backlink = '';

	public function __construct(
		private readonly ProfileFormFactory $profileFormFactory,
		private readonly ChangePasswordFormFactory $changePasswordFormFactory,
		private readonly LostPasswordFormFactory $lostPasswordFormFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly RegistrationFormFactory $registrationFormFactory,
		private readonly UserSideMenuFactory $userSideMenuFactory,
		private readonly UserHashModel $userHashModel,
		private readonly GoogleConnectFactory $googleConnectFactory,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly UserAddressFormFactory $userAddressFormFactory,
		private readonly OrderHistoryFactory $orderHistoryFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		$this->setObject($this->orm->tree->getById($this->params['idref']));
	}


	public function actionDefault(): void
	{
		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl();
			}
		} else {
			$this->redirectToLoginPage();
		}
	}


	public function actionLogin(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}


	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}


	public function actionProfil(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
	}


	public function actionRegistration(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {

			$userHash = $this->userHashModel->getHash($hashToken, UserHash::TYPE_LOST_PASSWORD);
			if ($userHash && $userHash->isValid()) {

			} else {

				$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
				$this->redirect($this->mutation->pages->lostPassword);
			}

		} else {
			$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
			$this->redirect($this->mutation->pages->lostPassword);
		}

	}

	public function actionOrderHistory(string $orderHash = null): void
	{
		if (isset($orderHash)) { // detail
			if ($this->user->loggedIn) {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
					'user' => $this->userEntity,
				]);
			} else {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
				]);
			}
			$this->template->order = $order;
		} elseif (!$this->user->loggedIn) { // list
			$this->redirectToLoginPage();
		}

		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl('userOrderHistoryArea');
				$this->redrawControl('orderHistoryControl');
			}
		}
	}

	public function handleRepeatOrder(int $orderId): void
	{
		$order = $this->orm->order->getById($orderId);

		if (!isset($order)) {
			$this->error('Invalid order ID');
		}

		if ($order->user->id === $this->userEntity->id) {
			foreach ($order->products as $orderItem) {
				// TODO:
				//$this->basket->addProductVariant($orderItem->variant, $orderItem->amount);
			}
		}

		$this->redirect($this->mutation->pages->step1);
	}

	public function handleLogout(): void
	{
		$this->getUser()->logout(TRUE);
		$this->flashMessage('msg_info_logout');
		$this->redirectToUserPage('title');
	}


	public function createComponentVp(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}


	/**
	 * presmerovani na prihlasovaci stranku, v pripade pristupu do zabezpecene sekce
	 */
	private function redirectToLoginPage(): never
	{
		$this->redirectToUserPage('userLogin');
	}


	/**
	 * presmerovani na detail uziv. sekce, pri pristupu na registraci, zapomenute heslo a prihlaseni
	 */
	private function redirectToProfilePage(): never
	{
		$this->redirectToUserPage('userProfil');
	}


	/**
	 * @param string $uid
	 * @throws AbortException
	 * @throws InvalidLinkException
	 */
	private function redirectToUserPage(string $uid): never
	{
		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link($this->mutation->pages->$uid);
//				echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}

		//, array('backlink' => $this->storeRequest())
		$this->redirect($this->mutation->pages->$uid);
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	protected function createComponentUserSideMenu(): UserSideMenu
	{
		return $this->userSideMenuFactory->create($this->object);
	}


	public function createComponentRegistrationForm(): RegistrationForm
	{
		return $this->registrationFormFactory->create($this->object);
	}


	public function createComponentProfileForm(): ProfileForm
	{
		return $this->profileFormFactory->create($this->object, $this->userEntity);
	}


	public function createComponentChangePasswordForm(): ChangePasswordForm
	{
		return $this->changePasswordFormFactory->create($this->object, $this->userEntity);
	}

	public function createComponentLostPasswordForm(): LostPasswordForm
	{
		$hash = NULL;
		$params = $this->getParameters();
		if (isset($params['hashToken'])) {
			$hash = $params['hashToken'];
		}
		return $this->lostPasswordFormFactory->create($this->mutation, $this->object, $hash);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		$googleLogin = $this->googleLoginFactory->create($this->mutation);

		$googleLogin->onLogin[] = fn() => $this->redirect($this->mutation->pages->userSection);

		$googleLogin->onEmailTaken[] = function () {
			$this->flashMessage('msg_info_google_login_email_taken', 'error');
			$this->redirect('this');
		};

		$googleLogin->onError[] = function () {
			$this->flashMessage('msg_info_google_login_error', 'error');
			$this->redirect('this');
		};

		return $googleLogin;
	}

	protected function createComponentGoogleConnect(): GoogleConnect
	{
		$googleConnect = $this->googleConnectFactory->create($this->mutation);

		$googleConnect['googleLogin']->onLogin[] = fn() => $this->redirect('this');

		$googleConnect['googleLogin']->onEmailTaken[] = function () {
			$this->flashMessage('msg_info_google_login_email_taken', 'error');
			$this->redirect('this');
		};

		$googleConnect['googleLogin']->onError[] = function () {
			$this->flashMessage('msg_info_google_login_error', 'error');
			$this->redirect('this');
		};

		return $googleConnect;
	}

	public function createComponentUserAddressForm(): UserAddressForm
	{
		return $this->userAddressFormFactory->create($this->getObject(), $this->userEntity);
	}

	public function createComponentOrderHistory(): OrderHistory
	{
		return $this->orderHistoryFactory->create($this->getObject(), $this->userEntity, $this->mutation);
	}

}
