<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Homepage;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Mutation\BrowserMutationDetector;
use App\Model\Orm\Routable;
use App\PostType\Page\Model\Orm\Tree;

final class HomepagePresenter extends BasePresenter
{

	public function __construct(
		private readonly BrowserMutationDetector $browserMutationDetector,
	)
	{
		parent::__construct();
	}

	public function actionDefault(Tree $object): void
	{
		$this->detectHpMutation($object);
		$this->setObject($this->mutationHolder->getMutation()->rootPage);
	}

	public function renderDefault(): void
	{
		$this->template->isHomepage = true;
	}


	private function detectHpMutation(Tree $object): void
	{
		$signal = $this->getSignal();
		if ($signal === null) {
			$anotherHp = $this->browserMutationDetector->detect($object, $this->mutation);
			if ($anotherHp instanceof Routable) {
				$this->redirect($anotherHp, ['mutation' => $anotherHp->getMutation()]);
			}
		}
	}

}
