<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Product;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\ProductParameters\ProductParameters;
use App\FrontModule\Components\ProductParameters\ProductParametersFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nette\Application\UI\Multiplier;
use Nextras\Orm\Collection\EmptyCollection;

/**
 * @method ProductVariant getObject()
 */
class ProductPresenter extends BasePresenter
{

	private ?ProductVariant $variant;

	private Product $product;

	private ProductLocalization $productLocalization;

	public function __construct(
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProductParametersFactory $productParametersFactory,
	)
	{
		parent::__construct();
	}


	public function actionDetail(ProductLocalization $object, mixed $v = null): void
	{
		$variantId = $v;
		$this->productLocalization = $object;
		$this->product = $this->productLocalization->product;

		if ($variantId !== null) {
			$this->variant = $object->getActiveVariantByMutation($this->mutation, $variantId);
			if ($this->variant !== null && $this->variant->product->id !== $this->product->id) {
				$this->redirect($this->productLocalization);
			}
		} else {
			$this->variant = $object->getFirstActiveVariantByMutation($this->mutation);
		}

		if ($this->variant === null && $this->getParameter('show') === 1) {
			$this->variant = $this->product->firstVariant;
		}

		$this->setObject($this->productLocalization);
	}


	public function renderDetail(): void
	{
		// sluzby
		$this->template->specialServices = [];
		$this->template->product = $this->product;
		$this->template->productLocalization = $this->productLocalization;
		$this->template->variant = $this->variant;
	}


	public function actionPreorder(int $id): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentProductParameters(): ProductParameters
	{
		return $this->productParametersFactory->create($this->product);
	}

	protected function createComponentAddToCart(): AddToCart
	{
		return $this->addToCartFactory->create(
			product: $this->product,
			productVariant: $this->variant,
			currentState: $this->currentState,
			priceLevel: $this->priceLevel,
			type: AddToCart::TYPE_DETAIL,
		);

	}

}
