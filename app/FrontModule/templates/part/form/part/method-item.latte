{default $class = false}
{default $name = false}
{default $id = false}
{default $title = false}
{default $delivery = false}
{default $price = 0}
{default $imgs = []}
{default $dataAction = false}
{default $tooltipContent = false}

<li n:class="f-method__item, $class"{if $dataAction} data-action="{$dataAction}"{/if}>
	<label class="f-method__label inp-item inp-item--radio">
		<input n:name="$name:$id" class="f-method__inp inp-item__inp" data-controller="autosubmit" data-action="autosubmit#submitForm">
		<span class="f-method__inner inp-item__text">
			<span class="f-method__img">
				{capture $sizes}
					{if count($imgs) == 1}
						width="70" height="70"
					{elseif count($imgs) == 2}
						width="70" height="35"
					{elseif count($imgs) == 3}
						width="70" height="25"
					{/if}
				{/capture}
				{foreach $imgs as $img}
					<img src="{$img->getSize('xs')->src}" alt="" loading="lazy" {$sizes|noescape}>
				{/foreach}
			</span>
			<span class="f-method__content">
				<b class="f-method__title">
					{$title}
					<a n:if="$tooltipContent" href="#popup-{$id}" data-modal data-modal-sm>?</a>
				</b>
				<span class="f-method__description">
					{block description}{/block}
				</span>
			</span>
			<span n:if="$name == 'deliveryMethod'" class="f-method__delivery">
				{include $templates . '/part/box/deliveryInfo.latte', deliveryDate: $deliveryDate,}
			</span>
			<span class="f-method__price">
				{if $price->isZero()}
					{_"free"}
				{else}
					{$price|money} {* |money *}
				{/if}
			</span>
		</span>
	</label>
	<div n:if="$tooltipContent" class="u-js-hide">
		<div id="popup-{$id}">
			<div class="u-mb-last-0 tw-text-center">
				<h2 class="tw-text-center">
					{$title}
				</h2>
				<p>
					{$tooltipContent}
				</p>
				<p>
					<button type="button" class="btn btn--secondary btn--long js-modal-close">
						<span class="btn__text">
							<span>
								{_'btn_close'}
							</span>
						</span>
					</button>
				</p>
			</div>
		</div>
	</div>
</li>
