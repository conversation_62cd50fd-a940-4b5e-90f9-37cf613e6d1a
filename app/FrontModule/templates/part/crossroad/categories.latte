{default $class = 'tw-mb-lg'}
{default $titleLang = false}
{default $items = $object->crossroad ?? []}
{default $categoriesProductCount = []}


<section n:if="$items->count() > 0" n:class="c-categories, $class">
	<h2 n:if="$titleLang" class="c-categories__title">
		{_$titleLang}
	</h2>

	<ul class="c-categories__list grid">
		<li n:foreach="$items as $item" class="c-categories__item grid__cell size--3-12">
			{if isset($filter)}
				{php $cleanFilterParamCopy = null}
				{capture $link}{link $item, filter => $cleanFilterParamCopy, 'pager-page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}
			{else}
				{capture $link}{link $item}{/capture}
			{/if}
			<a href="{$link}" class="c-categories__link h4 tw-mb-0">
				{$item->nameAnchor}
				{if isset($categoriesProductCount[$item->id])}
					({$categoriesProductCount[$item->id]})
				{/if}
			</a>
		</li>
	</ul>
</section>