<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Cart;

use App\FrontModule\Components\CartDelivery\CartDelivery;
use App\FrontModule\Components\CartDelivery\CartDeliveryFactory;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDelivery;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory;
use App\FrontModule\Components\CartUserDetail\CartUserDetail;
use App\FrontModule\Components\CartUserDetail\CartUserDetailFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\ShoppingCart\ShoppingCart;
use App\Model\TranslatorDB;
use Brick\Money\Money;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Controls\TextInput;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 */
final class Cart extends UI\Control
{
	private ICollection $products;

	private ICollection $vouchers;

	private array $amounts;

	private bool $showFlashes = false;

	public function __construct(
		private readonly State $currentState,
		private readonly PriceLevel $priceLevel,
		private readonly ShoppingCart $shoppingCart,
		private readonly TranslatorDB $translator,
		private readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly CartFreeDeliveryFactory $cartFreeDeliveryFactory,
		private readonly CartDeliveryFactory $cartDeliveryFactory,
		private readonly CartUserDetailFactory $cartUserDetailFactory,
	)
	{
		$this->onAnchor[] = $this->initCart(...);
	}

	public function initCart(): void
	{
		$this->products = $this->shoppingCart->getProducts();
		$this->vouchers = $this->shoppingCart->getVouchers();
		$this->amounts = $this->shoppingCart->getAmounts();
	}

	public function refreshCart(): void
	{
		if (($changed = $this->shoppingCart->refresh()) !== []) {
			foreach ($changed as $changedItem) {
				$this->flashMessage($this->translate($changedItem->message->text, $this->translateParams($changedItem->message->params)), $changedItem->message->type);
			}

			$this->initCart();
		}
	}

	public function getTemplateParameters(): array
	{
		return [
			'translator' => $this->translator,
			'object' => $this->presenter->getObject(),
			'templates' => FE_TEMPLATE_DIR,
			'pages' => $this->mutationHolder->getMutation()->pages,
			'mutation' => $this->mutationHolder->getMutation(),
			'state' => $this->currentState,
			'priceLevel' => $this->priceLevel,
			'shoppingCart' => $this->shoppingCart,
			'products' => $this->products,
			'vouchers' => $this->vouchers,
			'totalPrice' => $this->shoppingCart->getTotalPrice(),
			'totalPriceVat' => $this->shoppingCart->getTotalPriceVat(),
			'totalPriceWithDelivery' => $this->shoppingCart->getTotalPriceWithDelivery(),
			'totalPriceWithDeliveryVat' => $this->shoppingCart->getTotalPriceWithDeliveryVat(),
		];
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->setParameters($this->getTemplateParameters());
	}

	public function render(): void
	{
		$this->refreshCart();
		$this->beforeRender();

		$this->template->showVoucherInput = $this->orm->voucherCode->findBy([
			'voucher->mutation' => $this->mutationHolder->getMutation(),
			'voucher->public' => 1,
			'voucher->publicFrom<=' => new DateTimeImmutable(),
			'voucher->publicTo>=' => new DateTimeImmutable(),
			'isUsed' => 0
		])->countStored() > 0;

		$this->template->render(__DIR__ . '/cart.latte');
	}

	public function renderHeader(): void
	{
		$this->initCart();
		$this->beforeRender();

		$this->template->render(__DIR__ . '/cartHeader.latte');
	}



	public function createComponentFreeDelivery(): CartFreeDelivery
	{
		return $this->cartFreeDeliveryFactory->create($this->mutationHolder->getMutation(), $this->currentState, $this->priceLevel);
	}

	public function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$products = $form->addContainer('products');
		/** @var ProductItem $productItem */
		foreach ($this->shoppingCart->getProducts() as $productItem) {
			$container = $products->addContainer($productItem->variant->id);
			$container->addInteger('quantity', 'quantity')->setDefaultValue($productItem->amount);
		}

		$form->addText('voucher', 'voucher')->setNullable();

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function createComponentCartDelivery(): CartDelivery
	{
		return $this->cartDeliveryFactory->create();
	}

	public function createComponentCartUserDetail(): CartUserDetail
	{
		return $this->cartUserDetailFactory->create();
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$flashes = [];
		foreach ($values->products as $productVariantId => $data) {
			if($data->quantity !== $this->amounts[$productVariantId]){
				$variant = $this->orm->productVariant->getById($productVariantId);
				if($data->quantity > $this->amounts[$productVariantId]) {
					$amount = (int) abs($data->quantity - $this->amounts[$productVariantId]);
					$quantityAdded = $this->shoppingCart->addProduct($variant, $amount);
				} else {
					$quantityAdded = $amount = (int) ($this->amounts[$productVariantId] - $data->quantity);
					$this->shoppingCart->subtractProduct($variant, $amount);
				}

				if ($this->showFlashes) {
					if ($quantityAdded === 0) {
						$flashes['error'] = 'cart_message_no_updated_quantity';
					} elseif ($quantityAdded !== $amount) {
						$flashes['warning'] = 'cart_message_no_enough_quantity';
					} else {
						$flashes['ok'] = 'cart_message_quantity_updated';
					}
				}
			}
		}

		if ($values->voucher !== null) {
			$voucherCode = $this->orm->voucherCode->getByCode($values->voucher, $this->mutationHolder->getMutation());
			$response = VoucherCode::ERROR_NO_EXISTS;
			if ($voucherCode !== null) {
				$response = $this->shoppingCart->addVoucher($voucherCode);
			}

			if ($response === true) {
				$this->shoppingCart->flushVouchers([$voucherCode]);
				$msg = TranslatorDB::ALREADY_TRANSLATED_MARKER . $this->translator->translate('cart_message_voucher_added');
				$flashes['ok'] = $this->translate($msg, ['%voucher%' => $voucherCode->getName()]);
			} else {
				$msg = TranslatorDB::ALREADY_TRANSLATED_MARKER . $this->translator->translate($response);
				$replace = [];
				if ($voucherCode?->voucher !== null){
					$replace = [
						'%minOrderPrice%' => Filters::formatMoney(Money::of($voucherCode->voucher->minPriceOrder ?? 0, $voucherCode->voucher->getCurrency())),
					];
				}
				$flashes['warning'] = $this->translate($msg, $replace);
			}
			$this['form']['voucher']->setValue(null);
		}

		$this->initCart();

		foreach ($this->amounts as $productVariantId => $amount) {
			/** @var TextInput $control */
			$control = $this['form']['products'][$productVariantId]['quantity']; // @phpstan-ignore-line
			$control->setValue($amount);
		}


		if ($flashes !== []) {
			foreach ($flashes as $type => $message) {
				$this->flashMessage($this->translate($message), $type);
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();

	}

	public function handleDeleteItem(int $variantId): void
	{
		$variant = $this->orm->productVariant->getById($variantId);
		if ($variant !== null) {
			$this->shoppingCart->removeProduct($variant);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_product_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleDeleteVoucherItem(int $voucherCodeId): void
	{
		$voucherCode = $this->orm->voucherCode->getById($voucherCodeId);
		if ($voucherCode !== null) {
			$this->shoppingCart->removeVoucher($voucherCode);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_voucher_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function translate(string $key, array $replace = []): string
	{
		if ( ! empty($replace)) {
			return str_replace(array_keys($replace), array_values($replace), $this->translator->translate($key));
		}
		return $this->translator->translate($key);
	}

	public function translateParams(array $params = []): array
	{
		$keysToTranslate = ['%reason%'];
		foreach ($params as $key => $value) {
			if (in_array($key, $keysToTranslate)) {
				$params[$key] = $this->translate($value, $params);
			}
		}
		return $params;
	}

	public function setShowFlashes(bool $showFlashes = true): Cart
	{
		$this->showFlashes = $showFlashes;
		return $this;
	}

}
