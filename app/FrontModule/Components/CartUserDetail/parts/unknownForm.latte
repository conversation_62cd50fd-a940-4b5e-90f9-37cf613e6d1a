{form registrationForm class=>'f-basket', novalidate=>'novalidate'}
	<h2 class="tw-mb-sm  h3">
		1. {_'personal_info_title'}
	</h2>
	{control formMessage, $flashes, $form}
	<a n:href="changeEmail!" data-naja data-naja-history="off" data-naja-loader="body">{_order_change_email}</a>
	{include '../../inp.latte', form=>$form, name=>email, validate: true}
	{include '../../inp.latte', form=>$form, name=>password, validate: true}

	<div class="u-maw-3-12 u-mb-last-0">
		<p>
			<button n:name="save" class="btn btn--block">
				<span class="btn__text">
					{_'btn_register'}
				</span>
			</button>
		</p>
		<p>
			<button n:name="next" class="btn btn--secondary btn--block">
				<span class="btn__text">
					{_'btn_step2_continue_without_registration'}
				</span>
			</button>
		</p>
	</div>
{/form}
