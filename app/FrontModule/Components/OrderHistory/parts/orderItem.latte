{default $class = 'tw-mb-lg'}
{default $showStatus = false}
{default $showPager = false}

<div n:class="c-orders, $class">
	<table class="c-orders__table">
		<thead>
			<tr>
				<td>
					{_'order_number'}
				</td>
				<td>
					{_'order_status'}
				</td>
				<td>
					{_'order_created'}
				</td>
				<td>
					{_'order_delivery'}
				</td>
				<td>
					{_'order_payment'}
				</td>
				<td>
					{_'order_total_price_vat'}
				</td>
			</tr>
		</thead>
		<tbody>
		{foreach $orders as $order}
			{varType App\Model\Orm\Order\Order $order}
			<tr>
				<td>
					<a href="{plink $mutation->pages->userOrderHistory $order->hash}">
						{$order->orderNumber}
					</a>
				</td>
				<td>
					<span n:if="$showStatus" class="c-orders__status c-orders__status--{$order->state->value}"></span>
					{_'order_status_' . $order->state->value}
				</td>
				<td>
					{$order->placedAt|date:'j. n. Y'}
				</td>
				<td>
					{$order->delivery->getName()}
				</td>
				<td>
					{$order->payment->getName()}
					<span n:if="($paymentState = $order->payment->information->getState()) !== null">{_'order_payment_status_user_' . $paymentState->value|lower}</span>
				</td>
				<td>
					{$order->getTotalPriceWithDeliveryVat()|money}
				</td>
			</tr>
		{/foreach}
		</tbody>
	</table>

	<div class="c-orders__paging" n:if="$showPager">
		{control pager}
	</div>
</div>
