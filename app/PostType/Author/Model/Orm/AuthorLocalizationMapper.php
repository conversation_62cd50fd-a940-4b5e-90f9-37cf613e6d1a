<?php declare(strict_types = 1);

namespace App\PostType\Author\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class AuthorLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'author_localization';

	/**
	 * @return ICollection|AuthorLocalization[]
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if (count($excluded) > 0) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}


	public function findRandom(): ICollection
	{
		$builder = $this->builder()
			->orderBy('RAND()');

		return $this->toCollection($builder);
	}

	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('a.id')
			->from($this->tableName, 'a')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('al.id')
			->from($this->tableName, 'al')
			->andWhere('al.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

}
