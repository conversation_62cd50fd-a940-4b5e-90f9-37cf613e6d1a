<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\Definition;

use App\Model\CustomField\SuggestUrl;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

interface RelationInfoFactory
{

	public function create(
		IEntity $sourceEntity,
		string $propertyName,
		SuggestUrl $suggestUrl,

		?string $inputSuggestPropertyName = RelationInfo::DEFAULT_REMOTE_PROPERTY_NAME,
		?string $inputPlaceHolder = RelationInfo::DEFAULT_PLACEHOLDER,
		?string $iconFilename = null,
		?string $toggleName = null,
		?bool $dragAndDrop = false,
		?ICollection $builderCollection = null,
	): RelationInfo;

}
