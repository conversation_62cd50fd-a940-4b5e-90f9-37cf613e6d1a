<?php

declare(strict_types=1);

namespace App\Api\Entity\Response;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\File\File;
use App\Model\Orm\ImageEntity;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use JsonSerializable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use stdClass;
use function array_is_list;
use function array_map;
use function get_class;
use function is_array;
use function is_iterable;
use function is_object;
use function iterator_to_array;

final class CustomFields implements JsonSerializable
{
	public function __construct(
		private readonly stdClass $data,
	) {}

	public static function from(stdClass|null $data): self|null
	{
		return $data !== null
			? new self($data)
			: null;
	}

	public function jsonSerialize(): mixed
	{
		return $this->resolveLazyValues($this->data);
	}

	private function resolveLazyValues(mixed $value): mixed
	{
		if ($value instanceof ICollection) {
			return array_map(
				$this->resolveEntity(...),
				iterator_to_array($value, false),
			);
		}

		if ($value instanceof LazyValue) {
			$entity = $value->getEntity();
			if ($entity === null) {
				return null;
			}

			return $this->resolveEntity($entity);
		}

		if ((is_array($value) && array_is_list($value)) || is_iterable($value)) {
			return array_map(
				$this->resolveLazyValues(...),
				is_array($value) ? $value : iterator_to_array($value),
			);
		}

		if (is_object($value)) {
			$result = [];
			// @phpstan-ignore-next-line
			foreach ($value as $key => $subValue) {
				$result[$key] = $this->resolveLazyValues($subValue);
			}

			return $result;
		}

		return $value;
	}

	private function resolveEntity(IEntity $entity): mixed
	{
		if ($entity instanceof ImageEntity) {
			return [
				'type' => 'image',
				'id' => $entity->id,
				'url' => $entity->url,
			];
		}

		if ($entity instanceof File) {
			return [
				'type' => 'file',
				'id' => $entity->id,
				'fileType' => $entity->type,
				'url' => $entity->url,
			];
		}

		return [
			'type' => $this->translateEntityType(get_class($entity)),
			'id' => $entity->getPersistedId(),
		];
	}

	/**
	 * @param class-string<IEntity> $entityClass
	 */
	private function translateEntityType(string $entityClass): string
	{
		return match ($entityClass) {
			CommonTree::class => 'tree',
			CatalogTree::class => 'catalog',
			Author::class => 'author',
			Blog::class => 'blog',
			BlogTag::class => 'blogTag',
			default => $entityClass,
		};
	}
}
