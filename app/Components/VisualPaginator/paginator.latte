{default $class = $param['class'] ?? false}

<div n:if="$paginator->pageCount > 1" n:class="paging, $class">
	<p n:if="($param['showMoreBtn'] ?? false) && !$paginator->isLast()" class="paging__btn tw-text-center">
		{if isset($startPage)}
		{else}
			{var $startPage = $paginator->page}
		{/if}

		{if isset($filter)}
			{capture $link}{plink //this, 'filter' => $filter, 'pager-page' => $paginator->page + 1, 'more'=>$startPage}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}
		{else}
			{capture $link}{link //this, 'page' => $paginator->page + 1, 'more'=>$startPage}{/capture}
			{php $link = htmlspecialchars_decode($link)}
		{/if}

		<a href="{$link}" class="btn btn--secondary"{if $noFollowEnabled} rel="nofollow"{/if} data-naja data-naja-loader="body">
			<span class="btn__text">
				{if $paginator->getPage() + 1 == $paginator->getLastPage()}
					{var $futureItemNumber = $paginator->getItemCount() - $paginator->getPage() * $paginator->getItemsPerPage()}
				{else}
					{var $futureItemNumber = $paginator->getItemsPerPage()}
				{/if}


				{_btn_show_more_count|replace:'%count', (string)$futureItemNumber}
			</span>
		</a>
	</p>

	<p n:if="$param['showCount'] ?? false" class="tw-text-center">
		{_"showing"}
		{if isset($startPage)}
			{if $paginator->itemsOnFirstPage}
				{if $startPage == 1}
					{var $from = 1}
					{var $to= ($paginator->page-1)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage}
				{else}
					{var $from = ($startPage-2)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage + 1}
					{var $to= ($paginator->page-1)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage}
				{/if}
			{else}
				{var $from = ($startPage-1)*$paginator->itemsPerPage+1}
				{var $to = $paginator->page*$paginator->itemsPerPage}
			{/if}

			{if $paginator->isLast()}
				{var $to = $paginator->itemCount}
			{/if}
			{$from}-{$to}
		{else}
			{if $paginator->isLast()}
				{($paginator->offset)+1}–{$paginator->itemCount}
			{elseif $paginator->isFirst() && $paginator->itemsOnFirstPage}
				1–{$paginator->itemsOnFirstPage}
			{elseif $paginator->isFirst()}
				1–{$paginator->getItemsPerPageBasic()}
			{else}
				{$paginator->offset+1}–{$paginator->offset+$paginator->itemsPerPage}
			{/if}
		{/if}
		{_"of"}
		{$paginator->itemCount}
	</p>

	<div n:if="$param['showPages'] ?? true" class="paging__pages">
		<ul class="paging__list">
			{if !$paginator->isFirst()}
				{if isset($filter)}
					{capture $link}{plink this, 'filter' => $filter, 'pager-page' => $paginator->page - 1}{/capture}
					{php $link = urldecode(htmlspecialchars_decode($link))}
				{else}
					{capture $link}{link this, 'page' => $paginator->page - 1}{/capture}
				{/if}
				<li class="paging__item paging__item--prev">
					<a href="{$link}" class="paging__link item-icon"{if $noFollowEnabled} rel="nofollow"{/if} rel="prev" data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
						{('test-chevron-left')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_paging_prev}
						</span>
					</a>
				</li>
			{/if}

			{foreach $steps as $key => $step}
				{if isset($filter)}
					{capture $link}{plink this, 'filter' => $filter, 'pager-page' => $step}{/capture}
					{php $link = urldecode(htmlspecialchars_decode($link))}
				{else}
					{capture $link}{link this, 'page' => $step}{/capture}
					{php $link = htmlspecialchars_decode($link)}
				{/if}
				{if $step == $paginator->page}
					<li class="paging__item">
						<strong class="paging__link is-active">{$paginator->page}</strong>
					</li>
				{else}
					<li class="paging__item">
						<a href="{$link}" class="paging__link"{if $noFollowEnabled} rel="nofollow"{/if} data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
							{$step}
						</a>
					</li>
				{/if}
				{if $iterator->nextValue > $step + 1}
					<li class="paging__item">
						&hellip;
					</li>
				{/if}
			{/foreach}

			{if !$paginator->isLast()}
				{if isset($filter)}
					{capture $link}{plink this, 'filter' => $filter, 'pager-page' => $paginator->page + 1}{/capture}
					{php $link = urldecode(htmlspecialchars_decode($link))}
				{else}
					{capture $link}{link this, 'page' => $paginator->page + 1}{/capture}
					{php $link = htmlspecialchars_decode($link)}
				{/if}
				<li class="paging__item paging__item--next">
					<a href="{$link}" class="paging__link item-icon"{if $noFollowEnabled} rel="nofollow"{/if} rel="next" data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
						<span class="item-icon__text">{_paging_next}</span>
						{('test-chevron-right')|icon, 'item-icon__icon'}
					</a>
				</li>
			{/if}
		</ul>
	</div>
</div>
