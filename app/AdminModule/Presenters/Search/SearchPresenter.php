<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Search;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\All\Repository;
use App\PostType\Page\Model\Orm\Tree;

final class SearchPresenter extends BasePresenter
{

	public function __construct(
		private readonly Repository $elasticAllRepository,
	)
	{
		parent::__construct();
	}


	public function actionPage(?int $parentId = null, ?int $pathId = null, ?string $source = null): void
	{
		if (isset($this->params['q']) && $this->params['q']) {
			$q = trim($this->params['q']);


			$_excluded = explode('|', $source ?? '');
			$excluded = [];
			foreach ($_excluded as $i) {
				if ($i) {
					$excluded[] = $i;
				}
			}

			$pages = $this->orm->tree->searchByName($q, $parentId, $pathId, $excluded);
			$this->template->result = $pages;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
		$this->view = "suggestPage";
	}


	public function actionCategory(?int $mutationId = null): void
	{

		$q = $this->getHttpRequest()->getPost('q');
		if ($q) {
			$q = trim($q);
			$mutation = $this->orm->mutation->getById($mutationId);
			$pages = $this->orm->tree->searchByName($q, null);

			$pages = $this->orm->tree->findBy([
				'type' => Tree::TYPE_CATALOG,
				'rootId' => $mutation->rootId,
				'id' => $pages->fetchPairs(null, 'id'),
			]);
			$this->template->result = $pages;
		}



		if ($this->isAjax()) {
			$this->setLayout(false);
		}
		$this->view = "suggestCategory";
	}


	public function actionPages(?int $mutationId = null): void
	{

		$q = $this->getHttpRequest()->getPost('q');
		if ($q) {
			$q = trim($q);
			$mutation = $this->orm->mutation->getById($mutationId);
			$pages = $this->orm->tree->searchByName($q);
			if ($mutation) {
				$pages = $pages->findBy([
					'rootId' => $mutation->rootId
				]);
			}
			$this->template->result = $pages;
		}


		if ($this->isAjax()) {
			$this->setLayout(false);
		}
		$this->view = "suggestCategory";
	}


	public function actionWiredPage(): void
	{
		if (isset($this->params['q']) && $this->params['q']) {
			$q = trim($this->params['q']);

			$defaultMutation = $this->orm->mutation->getDefault();
			$pages = $this->orm->tree->searchByName($q)->findBy(['rootId' => $defaultMutation->rootId]);
			$this->template->result = $pages;

		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
		$this->view = "suggestLinkedCategories";
	}


	public function renderSuggestGlobal(): void
	{
		$count = $this->orm->mutation->findAll()->count();
		$this->template->isMoreThanOneLanguage = $count > 1;

		$searchQuery = $this->getHttpRequest()->getPost('q');
		if ($searchQuery === null || $searchQuery === '') {
			$searchQuery = trim($this->getHttpRequest()->getQuery('q'));
		}
		if ($searchQuery !== null && $searchQuery !== '') {
			$esIndex = $this->orm->esIndex->getAllLastActive($this->orm->mutation->getRsDefault());
			$esResult = $this->elasticAllRepository->elasticSearchAdminSuggest($esIndex, $searchQuery, 10, 0);
			$this->template->esResult = $esResult;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

}
