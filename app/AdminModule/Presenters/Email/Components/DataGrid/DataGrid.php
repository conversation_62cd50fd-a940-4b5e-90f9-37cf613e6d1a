<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Email\Components\DataGrid;

use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use LogicException;
use Nette\Application\UI\Control;
use Nette\Utils\Html;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly bool $isDeveloper,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly EmailTemplateRepository $emailTemplateRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();
		$conditions = [];
		if(!$this->isDeveloper) {
			$conditions['isHidden'] = 0;
		}

		$collection = $this->emailTemplateRepository->findBy($conditions);

		$grid->setItemsPerPageList([30, 50]);

		if (!$this->isDeveloper) {
			$collection = $collection->findBy(['isDeveloper' => 0]);
		}

		$grid->setDataSource($collection);
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('subject', 'subject')->setSortable()->setFilterText();

		if ($this->isDeveloper) {
			$grid->addColumnText('key', 'label_key')->setFilterText();
			$grid->addColumnText('isDeveloper', 'onlyForDevelopers')
				->setTemplateEscaping(false)
				->setRenderer(
					function (EmailTemplate $emailTemplate) {
						return ($emailTemplate->isDeveloper === 1 ) ? '<span class="btn-success btn btn-xs">Pro developery</span>' : '';
					}
				);
			$grid->addColumnStatus('isHidden', 'isHidden')
				->addOption(1, 'hidden')->setClass('btn-danger')->endOption()
				->addOption(0, 'visible')->setIcon('check')->endOption()
				->onChange[] = [$this, 'isHiddenChange']
			;
//			$grid->addColumnText('isHidden', 'isHidden')
//				->setRenderer(
//					function (EmailTemplate $emailTemplate) {
//						$checkbox = Html::el('input')
//							->setAttribute('id', 'toggle-hidden-'.$emailTemplate->id)
//							->setAttribute('data-id', $emailTemplate->id)
//							->setAttribute('type', 'checkbox')
//							->setAttribute('class', 'toggle-hidden');
//						if ($emailTemplate->isHidden === 1 ) {
//							$checkbox->setAttribute('checked', 'checked');
//						}
//						return $checkbox;
//					}
//				);
		}

		$this->addColumnMutation($grid);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

	public function handleIsHiddenChange($emailTemplateId, $newValue): void
	{
		$emailTemplate = $this->emailTemplateRepository->getById($emailTemplateId);
		$emailTemplate->isHidden = $newValue;
		$this->emailTemplateRepository->persistAndFlush($emailTemplate);
	}

}
