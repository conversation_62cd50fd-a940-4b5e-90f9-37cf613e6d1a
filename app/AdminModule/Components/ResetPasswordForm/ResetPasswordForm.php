<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ResetPasswordForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\Orm;
use App\Model\Orm\User\UserModel;
use App\Model\Translator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class ResetPasswordForm extends UI\Control
{

	public function __construct(
		private readonly string $hash,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly UserModel $userModel,
		private readonly MessageForFormFactory $messageForFormFactory,
	) {}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/resetPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addHidden('hash', $this->hash);

		$form->addPassword('password', 'password')->setRequired();
		$form->addPassword('passwordVerify', 'password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formResetSuccess'];
		$form->onError[] = [$this, 'formResetError'];
		return $form;
	}

	public function formResetError(UI\Form $form): void
	{
		bd($form->errors);
	}


	public function formResetSuccess(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		bd($valuesAll);
		$hashObject = $this->orm->userHash->getBy(['hash' => $valuesAll['hash']]);

		$userEntity = $hashObject->user;

		if ($this->userModel->save($userEntity, $valuesAll, $userEntity->id) !== false) {
			$this->userModel->deleteHash($userEntity, $hashObject);
			$this->flashMessage('form_reset_password', 'ok');
			$this->presenter->flashMessage('form_reset_password', 'ok');
		}

		if (!$userEntity->id) {
			$form['password']->addError('user_not_found');
			$this->flashMessage('user_not_found', 'error');
		}

		$form['password']->setValue('');
		$form['passwordVerify']->setValue('');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect(':Admin:Sign:default');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
