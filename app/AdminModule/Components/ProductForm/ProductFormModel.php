<?php

declare(strict_types=1);

namespace App\AdminModule\Components\ProductForm;

use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationModel;

final class ProductFormModel
{

	public function __construct(
		private readonly ProductLocalizationModel $productLocalizationModel,
		private readonly ProductVariantLocalizationModel $productVariantLocalizationModel,
		private readonly Orm $orm,
	) {}


	public function handleLocalizationButtonInSubmit(string $submitAction, Product $product): void
	{
		if (preg_match('/^(' . ProductForm::SUBMIT_MUTATION_CREATE . '|' . ProductForm::SUBMIT_MUTATION_REMOVE . ')([a-zA-Z]+)$/', $submitAction, $matches)) {
			$mutation = $this->orm->mutation->getByCode(strtolower($matches[2]));
			$localization = $product->getLocalization($mutation);

			if ($matches[1] === ProductForm::SUBMIT_MUTATION_CREATE) {
				$this->productLocalizationModel->create($mutation, $product);
				$this->productVariantLocalizationModel->createForAllVariant($mutation, $product);
			} else if ($matches[1] === ProductForm::SUBMIT_MUTATION_REMOVE) {
				$this->productLocalizationModel->delete($mutation, $product);
				$this->productVariantLocalizationModel->deleteForAllVariant($mutation, $product);
				/* todo elastic
				foreach ($product->variants as $variant) {
					$this->esProductService->deleteFromAllMutation($variant);
				}
				*/
			}
		}
	}

}
